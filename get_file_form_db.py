#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从MySQL数据库获取数据并生成Excel文件
"""

import pymysql
import pandas as pd
from datetime import datetime
import os
import sys

class DatabaseToExcel:
    def __init__(self, host, port, user, password, database):
        """
        初始化数据库连接参数
        
        Args:
            host (str): 数据库主机地址
            port (int): 数据库端口
            user (str): 用户名
            password (str): 密码
            database (str): 数据库名
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.connection = None
    
    def connect(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            print(f"✓ 成功连接到数据库: {self.database}")
            return True
        except Exception as e:
            print(f"✗ 数据库连接失败: {str(e)}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            print("✓ 数据库连接已关闭")
    
    def execute_query(self, sql):
        """
        执行SQL查询
        
        Args:
            sql (str): SQL查询语句
            
        Returns:
            list: 查询结果列表
        """
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql)
                result = cursor.fetchall()
                print(f"✓ 查询成功，共获取 {len(result)} 条数据")
                return result
        except Exception as e:
            print(f"✗ 查询失败: {str(e)}")
            return None
    
    def export_to_excel(self, data, filename=None, sheet_name='数据'):
        """
        将数据导出到Excel文件
        
        Args:
            data (list): 查询结果数据
            filename (str): 输出文件名，如果为None则自动生成
            sheet_name (str): Excel工作表名称
            
        Returns:
            str: 生成的文件路径
        """
        if not data:
            print("✗ 没有数据可以导出")
            return None
        
        # 生成文件名
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"数据导出_{timestamp}.xlsx"
        
        try:
            # 转换为DataFrame
            df = pd.DataFrame(data)
            
            # 导出到Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # 获取绝对路径
            abs_path = os.path.abspath(filename)
            print(f"✓ Excel文件已生成: {abs_path}")
            print(f"✓ 数据行数: {len(df)}")
            print(f"✓ 数据列数: {len(df.columns)}")
            
            return abs_path
        except Exception as e:
            print(f"✗ Excel文件生成失败: {str(e)}")
            return None

def main():
    """主函数"""
    # 数据库配置 - 请根据实际情况修改
    DB_CONFIG = {
        'host': '*************',        # 数据库主机地址
        'port': 3306,              # 数据库端口
        'user': 'root',   # 用户名
        'password': 'root@2024', # 密码
        'database': 'smartdecision' # 数据库名
    }
    
    # SQL查询语句
    SQL_QUERY = """
    SELECT RESOURCE_CODE, ENAME, CNAME 
    FROM crd_datasource_params 
    WHERE FIELD_TYPE='2' 
      AND VERSION='V1' 
      AND RESOURCE_CODE NOT IN ('yinkesit10001', '976652b23d41493c89396367fbfba2a9', 'test001') 
      AND CNAME NOT IN ('返回码', '返回代码', '错误码', '状态') 
    ORDER BY RESOURCE_CODE
    """
    
    # 创建数据库连接实例
    db_excel = DatabaseToExcel(**DB_CONFIG)
    
    try:
        # 连接数据库
        if not db_excel.connect():
            sys.exit(1)
        
        # 执行查询
        print("正在执行查询...")
        data = db_excel.execute_query(SQL_QUERY)
        
        if data is None:
            sys.exit(1)
        
        # 导出到Excel
        print("正在生成Excel文件...")
        excel_file = db_excel.export_to_excel(
            data, 
            filename="crd_datasource_params_export.xlsx",
            sheet_name="数据源参数"
        )
        
        if excel_file:
            print(f"\n🎉 任务完成！Excel文件已保存到: {excel_file}")
        else:
            print("\n❌ Excel文件生成失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        sys.exit(1)
    finally:
        # 关闭数据库连接
        db_excel.disconnect()

if __name__ == "__main__":
    print("=" * 50)
    print("         MySQL数据导出到Excel工具")
    print("=" * 50)
    main()
