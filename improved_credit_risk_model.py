#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的信贷风险预测模型
解决数据泄露、过拟合和数据不平衡问题
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime, timedelta
import joblib

# 机器学习相关
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.preprocessing import LabelEncoder, StandardScaler, RobustScaler
from sklearn.impute import SimpleImputer
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.metrics import (
    roc_auc_score,
    roc_curve,
    precision_recall_curve,
    classification_report,
    confusion_matrix,
    f1_score,
    average_precision_score,
    accuracy_score,
)
from sklearn.calibration import calibration_curve, CalibratedClassifierCV

# 模型
import lightgbm as lgb
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression

# 不平衡数据处理
from imblearn.over_sampling import SMOTE, ADASYN
from imblearn.under_sampling import RandomUnderSampler
from imblearn.combine import SMOTETomek

# 设置
RANDOM_STATE = 42
np.random.seed(RANDOM_STATE)
warnings.filterwarnings("ignore")
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


class ImprovedCreditRiskModel:
    """改进的信贷风险预测模型"""

    def __init__(self, random_state=42):
        self.random_state = random_state
        self.models = {}
        self.preprocessors = {}
        self.feature_importance = None
        self.selected_features = None

    def load_and_clean_data(self, file_path, target_col="好坏标签"):
        """加载并清理数据，严格防止数据泄露"""
        print("=== 数据加载与清理 ===")

        # 加载数据
        data = pd.read_csv(file_path)
        print(f"✓ 数据加载成功，形状: {data.shape}")

        # 转换目标变量
        if data[target_col].dtype == "object":
            label_mapping = {"坏": 1, "好": 0}  # 坏客户=高风险=1
            data[target_col] = data[target_col].map(label_mapping)
            print(f"✓ 目标变量转换完成: {dict(data[target_col].value_counts())}")

        # 分离特征和目标
        y = data[target_col].copy()
        X = data.drop(columns=[target_col])

        # 严格的特征筛选 - 移除可能包含未来信息的特征
        suspicious_keywords = [
            "距今",
            "最近",
            "当前",
            "现在",
            "今日",
            "本月",
            "本年",
            "逾期",
            "违约",
            "拒绝",
            "失败",
            "成功",
            "通过",
            "涉赌",
            "涉诈",
            "风险",
            "黑名单",
            "白名单",
        ]

        suspicious_features = []
        for col in X.columns:
            for keyword in suspicious_keywords:
                if keyword in col:
                    suspicious_features.append(col)
                    break

        print(f"\n⚠️  发现 {len(suspicious_features)} 个可疑特征（可能包含未来信息）:")
        for feat in suspicious_features[:10]:  # 只显示前10个
            print(f"   - {feat}")
        if len(suspicious_features) > 10:
            print(f"   ... 还有 {len(suspicious_features) - 10} 个")

        # 移除可疑特征
        X_clean = X.drop(columns=suspicious_features)
        print(f"\n✓ 移除可疑特征后，剩余特征数: {X_clean.shape[1]}")

        # 基本数据清理
        X_clean = self._basic_cleaning(X_clean)

        return X_clean, y

    def _basic_cleaning(self, X):
        """基本数据清理"""
        print("\n=== 基本数据清理 ===")

        # 处理特殊值
        special_values = [-999, -1, 999, 9999]
        for val in special_values:
            replaced_count = (X == val).sum().sum()
            if replaced_count > 0:
                X = X.replace(val, np.nan)
                print(f"✓ 将 {replaced_count} 个 {val} 替换为 NaN")

        # 删除高缺失率列（>70%）
        missing_ratios = X.isnull().sum() / len(X)
        high_missing_cols = missing_ratios[missing_ratios > 0.7].index.tolist()
        if high_missing_cols:
            X = X.drop(columns=high_missing_cols)
            print(f"✓ 删除 {len(high_missing_cols)} 个高缺失率列")

        # 删除常数列
        constant_cols = []
        for col in X.select_dtypes(include=[np.number]).columns:
            if X[col].nunique() <= 1:
                constant_cols.append(col)

        if constant_cols:
            X = X.drop(columns=constant_cols)
            print(f"✓ 删除 {len(constant_cols)} 个常数列")

        print(f"清理后特征数: {X.shape[1]}")
        return X

    def handle_imbalance(self, X_train, y_train, method="smote"):
        """处理数据不平衡"""
        print(f"\n=== 处理数据不平衡 (方法: {method}) ===")

        print(f"原始分布: {dict(y_train.value_counts())}")

        if method == "smote":
            sampler = SMOTE(random_state=self.random_state)
        elif method == "adasyn":
            sampler = ADASYN(random_state=self.random_state)
        elif method == "smote_tomek":
            sampler = SMOTETomek(random_state=self.random_state)
        elif method == "undersample":
            sampler = RandomUnderSampler(random_state=self.random_state)
        else:
            print("不进行重采样")
            return X_train, y_train

        X_resampled, y_resampled = sampler.fit_resample(X_train, y_train)
        print(f"重采样后分布: {dict(pd.Series(y_resampled).value_counts())}")

        return X_resampled, y_resampled

    def conservative_feature_selection(self, X_train, y_train, n_features=30):
        """保守的特征选择，避免过拟合"""
        print(f"\n=== 保守特征选择 (目标特征数: {n_features}) ===")

        # 使用多种方法进行特征选择
        methods = {
            "f_classif": SelectKBest(score_func=f_classif, k=n_features),
            "mutual_info": SelectKBest(score_func=mutual_info_classif, k=n_features),
        }

        selected_features_sets = {}

        for method_name, selector in methods.items():
            selector.fit(X_train, y_train)
            selected_features = X_train.columns[selector.get_support()].tolist()
            selected_features_sets[method_name] = set(selected_features)
            print(f"{method_name} 选择的特征数: {len(selected_features)}")

        # 取交集，确保特征的稳定性
        common_features = set.intersection(*selected_features_sets.values())
        print(f"所有方法共同选择的特征数: {len(common_features)}")

        # 如果交集太少，使用并集但限制数量
        if len(common_features) < n_features // 2:
            all_features = set.union(*selected_features_sets.values())
            self.selected_features = list(all_features)[:n_features]
            print(f"使用并集特征，最终选择: {len(self.selected_features)} 个")
        else:
            self.selected_features = list(common_features)
            print(f"使用交集特征: {len(self.selected_features)} 个")

        return self.selected_features

    def train_conservative_models(self, X_train, y_train, X_val, y_val):
        """训练保守的模型，防止过拟合"""
        print("\n=== 训练保守模型 ===")

        # 保守的LightGBM参数
        lgb_params = {
            "objective": "binary",
            "metric": "auc",
            "boosting_type": "gbdt",
            "num_leaves": 15,  # 减少叶子数
            "learning_rate": 0.01,  # 降低学习率
            "feature_fraction": 0.8,
            "bagging_fraction": 0.8,
            "bagging_freq": 5,
            "min_data_in_leaf": 50,  # 增加最小叶子样本数
            "lambda_l1": 0.1,  # 增加L1正则化
            "lambda_l2": 0.1,  # 增加L2正则化
            "is_unbalance": True,
            "verbose": -1,
            "random_state": self.random_state,
            "force_row_wise": True,
        }

        # 训练LightGBM
        train_data = lgb.Dataset(X_train, label=y_train)
        val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)

        lgb_model = lgb.train(
            lgb_params,
            train_data,
            valid_sets=[val_data],
            num_boost_round=500,  # 减少迭代次数
            callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)],
        )

        self.models["lightgbm"] = lgb_model
        print(f"✓ LightGBM训练完成，最佳迭代: {lgb_model.best_iteration}")

        # 保守的随机森林
        rf_model = RandomForestClassifier(
            n_estimators=100,  # 减少树的数量
            max_depth=5,  # 限制树的深度
            min_samples_split=50,  # 增加分裂最小样本数
            min_samples_leaf=20,  # 增加叶子最小样本数
            class_weight="balanced",
            random_state=self.random_state,
            n_jobs=-1,
        )

        rf_model.fit(X_train, y_train)
        self.models["random_forest"] = rf_model
        print("✓ 随机森林训练完成")

        # 逻辑回归
        lr_model = LogisticRegression(
            class_weight="balanced",
            C=0.1,  # 增加正则化强度
            random_state=self.random_state,
            max_iter=1000,
        )

        lr_model.fit(X_train, y_train)
        self.models["logistic"] = lr_model
        print("✓ 逻辑回归训练完成")

        return self.models

    def evaluate_models(self, X_test, y_test):
        """评估模型性能"""
        print("\n=== 模型性能评估 ===")

        results = []

        for model_name, model in self.models.items():
            # 预测
            if model_name == "lightgbm":
                y_pred_proba = model.predict(X_test, num_iteration=model.best_iteration)
            else:
                y_pred_proba = model.predict_proba(X_test)[:, 1]

            # 计算指标
            auc = roc_auc_score(y_test, y_pred_proba)
            pr_auc = average_precision_score(y_test, y_pred_proba)

            # 找到最佳阈值
            fpr, tpr, thresholds = roc_curve(y_test, y_pred_proba)
            optimal_idx = np.argmax(tpr - fpr)
            optimal_threshold = thresholds[optimal_idx]

            # 使用最佳阈值计算分类指标
            y_pred = (y_pred_proba >= optimal_threshold).astype(int)

            from sklearn.metrics import precision_score, recall_score

            precision = precision_score(y_test, y_pred)
            recall = recall_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred)
            accuracy = accuracy_score(y_test, y_pred)

            results.append(
                {
                    "model": model_name,
                    "auc": auc,
                    "pr_auc": pr_auc,
                    "precision": precision,
                    "recall": recall,
                    "f1": f1,
                    "accuracy": accuracy,
                    "optimal_threshold": optimal_threshold,
                }
            )

            print(
                f"{model_name:15} - AUC: {auc:.4f}, PR-AUC: {pr_auc:.4f}, F1: {f1:.4f}"
            )

        return pd.DataFrame(results)

    def diagnose_model_issues(self, results_df, y_train, y_test):
        """诊断模型问题"""
        print("\n" + "=" * 60)
        print("=== 模型诊断报告 ===")
        print("=" * 60)

        # 1. 数据不平衡分析
        print(f"\n1. 数据分布分析:")
        train_pos_rate = (y_train == 1).mean() * 100
        test_pos_rate = (y_test == 1).mean() * 100
        print(f"   训练集正样本比例: {train_pos_rate:.1f}%")
        print(f"   测试集正样本比例: {test_pos_rate:.1f}%")

        if abs(train_pos_rate - test_pos_rate) > 5:
            print(f"   ⚠️  训练集和测试集分布差异较大")

        # 2. 模型性能分析
        print(f"\n2. 模型性能分析:")
        best_auc = results_df["auc"].max()
        worst_auc = results_df["auc"].min()

        if best_auc > 0.95:
            print(f"   ⚠️  最佳AUC = {best_auc:.4f} (可能过拟合)")
        elif best_auc < 0.65:
            print(f"   ⚠️  最佳AUC = {best_auc:.4f} (性能较差)")
        else:
            print(f"   ✓ 最佳AUC = {best_auc:.4f} (性能合理)")

        # 3. 模型一致性分析
        auc_std = results_df["auc"].std()
        if auc_std > 0.05:
            print(f"   ⚠️  模型间AUC差异较大 (std={auc_std:.4f})")
        else:
            print(f"   ✓ 模型间性能一致 (std={auc_std:.4f})")

        # 4. 建议
        print(f"\n3. 改进建议:")
        if best_auc > 0.95:
            print(f"   - 检查是否存在数据泄露")
            print(f"   - 增加正则化强度")
            print(f"   - 减少模型复杂度")
        elif best_auc < 0.65:
            print(f"   - 增加更多有效特征")
            print(f"   - 尝试特征工程")
            print(f"   - 检查数据质量")

        if train_pos_rate < 10:
            print(f"   - 考虑使用更强的重采样方法")
            print(f"   - 调整模型的class_weight参数")

        return results_df

    def save_model(self, model_name, file_path):
        """保存模型"""
        if model_name in self.models:
            model_data = {
                "model": self.models[model_name],
                "selected_features": self.selected_features,
                "preprocessors": self.preprocessors,
            }
            joblib.dump(model_data, file_path)
            print(f"✓ 模型 {model_name} 已保存到 {file_path}")
        else:
            print(f"❌ 模型 {model_name} 不存在")


def main():
    """主函数 - 演示改进的模型训练流程"""
    print("开始改进的信贷风险模型训练...")

    # 初始化模型
    model = ImprovedCreditRiskModel(random_state=42)

    # 1. 加载和清理数据
    X, y = model.load_and_clean_data("result_data.csv")

    # 2. 数据集划分
    X_temp, X_test, y_temp, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    X_train, X_val, y_train, y_val = train_test_split(
        X_temp, y_temp, test_size=0.125, random_state=42, stratify=y_temp
    )

    print(f"\n数据集划分:")
    print(f"训练集: {X_train.shape[0]} 样本")
    print(f"验证集: {X_val.shape[0]} 样本")
    print(f"测试集: {X_test.shape[0]} 样本")

    # 3. 特征预处理
    # 数值型特征填充
    numeric_features = X_train.select_dtypes(include=[np.number]).columns
    numeric_imputer = SimpleImputer(strategy="median")

    X_train_processed = X_train.copy()
    X_val_processed = X_val.copy()
    X_test_processed = X_test.copy()

    if len(numeric_features) > 0:
        X_train_processed[numeric_features] = numeric_imputer.fit_transform(
            X_train[numeric_features]
        )
        X_val_processed[numeric_features] = numeric_imputer.transform(
            X_val[numeric_features]
        )
        X_test_processed[numeric_features] = numeric_imputer.transform(
            X_test[numeric_features]
        )

    # 4. 特征选择
    selected_features = model.conservative_feature_selection(
        X_train_processed, y_train, n_features=30
    )

    X_train_final = X_train_processed[selected_features]
    X_val_final = X_val_processed[selected_features]
    X_test_final = X_test_processed[selected_features]

    # 5. 处理数据不平衡
    X_train_balanced, y_train_balanced = model.handle_imbalance(
        X_train_final, y_train, method="smote"
    )

    # 6. 训练模型
    models = model.train_conservative_models(
        X_train_balanced, y_train_balanced, X_val_final, y_val
    )

    # 7. 评估模型
    results = model.evaluate_models(X_test_final, y_test)

    # 8. 诊断问题
    model.diagnose_model_issues(results, y_train, y_test)

    # 9. 保存最佳模型
    best_model_name = results.loc[results["auc"].idxmax(), "model"]
    model.save_model(best_model_name, f"best_model_{best_model_name}.pkl")

    print(f"\n🎯 训练完成！最佳模型: {best_model_name}")
    print(f"📊 详细结果:")
    print(results.round(4))


if __name__ == "__main__":
    main()
