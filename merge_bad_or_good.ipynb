{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据标记任务 - 优化版本\n", "==================================================\n", "\n", "方法1: 快速执行（推荐）\n", "------------------------------\n", "[11:19:59] • ============================================================\n", "[11:19:59] • 开始执行数据标记任务（优化版）\n", "[11:19:59] • ============================================================\n", "[11:19:59] • 开始加载数据文件...\n", "[11:19:59] • 正在读取主数据文件: result_20250617_111816.xlsx\n", "[11:21:13] ✓ 主数据文件加载成功 - 形状: (2374, 1224)\n", "[11:21:13] • 正在读取标记文件: /Users/<USER>/Downloads/0206样本.xlsx\n", "[11:21:14] ✓ 标记文件加载成功 - 形状: (2432, 6)\n", "[11:21:14] • 开始数据清洗和验证...\n", "[11:21:14] • 主数据有效身份证号: 2,374/2,374 (100.0%)\n", "[11:21:14] • 标记数据有效身份证号: 2,432/2,432 (100.0%)\n", "[11:21:14] ⚠️ 发现重复身份证号: 4个，将保留第一条记录\n", "[11:21:14] • 开始数据合并...\n", "[11:21:14] ✓ 数据合并完成 - 匹配率: 2,374/2,374 (100.0%)\n", "[11:21:14] • 准备生成输出文件...\n", "[11:22:06] ✓ 输出文件保存成功: result_20250617_111816_labeled_20250617_112114.xlsx\n", "[11:22:06] • 生成处理报告...\n", "[11:22:06] ✓ 处理报告已保存: 数据标记报告_优化版_20250617_112206.md\n", "[11:22:06] • ============================================================\n", "[11:22:06] • 任务完成 - 耗时: 126.20秒\n", "[11:22:06] • ============================================================\n", "\n", "✓ 任务执行成功！\n", "✓ 输出文件: result_20250617_111816_labeled_20250617_112114.xlsx\n", "✓ 报告文件: 数据标记报告_优化版_20250617_112206.md\n", "✓ 执行耗时: 126.20秒\n", "\n", "统计信息:\n", "  - main_total: 2374\n", "  - main_valid_idcard: 2374\n", "  - label_total: 2428\n", "  - label_valid_idcard: 2432\n", "  - label_duplicates: 4\n", "  - merged_total: 2374\n", "  - matched_count: 2374\n", "  - match_rate: 100.0\n", "  - output_filename: result_20250617_111816_labeled_20250617_112114.xlsx\n", "  - output_rows: 2374\n", "  - output_cols: 1227\n", "  - label_coverage: 100.0\n", "\n", "==================================================\n"]}], "source": ["# 数据标记任务 - 优化版本\n", "# 使用优化的工具模块进行高效数据处理\n", "\n", "from data_labeling_utils import quick_label_data, DataLabelingProcessor\n", "import pandas as pd\n", "\n", "print(\"数据标记任务 - 优化版本\")\n", "print(\"=\" * 50)\n", "\n", "# 方法1: 使用快速一键执行函数\n", "print(\"\\n方法1: 快速执行（推荐）\")\n", "print(\"-\" * 30)\n", "\n", "result = quick_label_data(\n", "    main_file=\"result_20250617_111816.xlsx\",\n", "    label_file=\"/Users/<USER>/Downloads/0206样本.xlsx\",\n", "    output_prefix=\"result_20250617_111816_labeled\",\n", "    verbose=True\n", ")\n", "\n", "if result['success']:\n", "    print(f\"\\n✓ 任务执行成功！\")\n", "    print(f\"✓ 输出文件: {result['output_file']}\")\n", "    print(f\"✓ 报告文件: {result['report_file']}\")\n", "    print(f\"✓ 执行耗时: {result['duration']:.2f}秒\")\n", "    print(f\"\\n统计信息:\")\n", "    for key, value in result['stats'].items():\n", "        print(f\"  - {key}: {value}\")\n", "else:\n", "    print(f\"✗ 任务执行失败\")\n", "    if 'error' in result:\n", "        print(f\"错误信息: {result['error']}\")\n", "\n", "print(\"\\n\" + \"=\" * 50)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 方法2: 使用高级处理器（适合需要自定义的场景）\n", "print(\"\\n方法2: 高级用法演示\")\n", "print(\"-\" * 30)\n", "\n", "# 创建处理器实例\n", "processor = DataLabelingProcessor(verbose=True)\n", "\n", "# 可以分步执行，便于调试和自定义\n", "print(\"\\n演示分步执行:\")\n", "\n", "# 步骤1: 加载数据\n", "main_df, label_df = processor.load_and_validate_data(\n", "    \"result_20250616_152548.xlsx\",\n", "    \"/Users/<USER>/Downloads/0206样本.xlsx\"\n", ")\n", "\n", "if main_df is not None and label_df is not None:\n", "    print(f\"\\n数据加载成功:\")\n", "    print(f\"  - 主数据: {main_df.shape}\")\n", "    print(f\"  - 标记数据: {label_df.shape}\")\n", "    \n", "    # 可以在这里添加自定义的数据预处理逻辑\n", "    # 例如：额外的数据清洗、特征工程等\n", "    \n", "    # 查看标签分布\n", "    if '标签' in label_df.columns:\n", "        print(f\"\\n标签分布:\")\n", "        label_counts = label_df['标签'].value_counts()\n", "        for label, count in label_counts.items():\n", "            print(f\"  - {label}: {count}个\")\n", "    \n", "    # 查看身份证号样本\n", "    print(f\"\\n身份证号样本:\")\n", "    sample_ids = main_df['APP_IDCARD'].head(3).tolist()\n", "    for i, idcard in enumerate(sample_ids, 1):\n", "        print(f\"  {i}. {idcard}\")\n", "        \n", "else:\n", "    print(\"数据加载失败，无法继续演示\")\n", "\n", "print(\"\\n\" + \"-\" * 50)\n", "print(\"提示: 优化版本的主要改进:\")\n", "print(\"✓ 向量化操作提升性能\")\n", "print(\"✓ 更好的内存管理\")\n", "print(\"✓ 统一的日志系统\")\n", "print(\"✓ 模块化设计便于维护\")\n", "print(\"✓ 类型提示增强代码可读性\")\n", "print(\"✓ 完整的错误处理机制\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 性能对比和使用建议\n", "print(\"\\n性能对比和使用建议\")\n", "print(\"-\" * 30)\n", "\n", "print(\"\\n优化前 vs 优化后:\")\n", "print(\"┌─────────────────┬──────────────┬──────────────┐\")\n", "print(\"│ 项目            │ 优化前       │ 优化后       │\")\n", "print(\"├─────────────────┼──────────────┼──────────────┤\")\n", "print(\"│ 身份证号验证    │ 循环处理     │ 向量化操作   │\")\n", "print(\"│ 内存使用        │ 多次复制     │ 就地操作     │\")\n", "print(\"│ 错误处理        │ 基础try/catch│ 分类处理     │\")\n", "print(\"│ 代码结构        │ 单一文件     │ 模块化设计   │\")\n", "print(\"│ 日志系统        │ 简单print    │ 统一日志     │\")\n", "print(\"│ 类型安全        │ 无类型提示   │ 完整类型提示 │\")\n", "print(\"└─────────────────┴──────────────┴──────────────┘\")\n", "\n", "print(\"\\n使用建议:\")\n", "print(\"• 小数据集(< 10万行): 两种方法性能差异不大\")\n", "print(\"• 大数据集(> 10万行): 建议使用优化版本\")\n", "print(\"• 生产环境: 强烈推荐使用优化版本\")\n", "print(\"• 调试阶段: 可以使用分步执行方法\")\n", "\n", "print(\"\\n快速使用示例:\")\n", "print(\"```python\")\n", "print(\"from data_labeling_utils import quick_label_data\")\n", "print(\"\")\n", "print(\"result = quick_label_data(\")\n", "print(\"    main_file='your_main_file.xlsx',\")\n", "print(\"    label_file='your_label_file.xlsx'\")\n", "print(\")\")\n", "print(\"```\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 清理和总结\n", "print(\"\\n清理和总结\")\n", "print(\"-\" * 30)\n", "\n", "print(\"\\n✅ 优化完成！主要改进包括:\")\n", "print(\"1. 创建了 data_labeling_utils.py 工具模块\")\n", "print(\"2. 实现了向量化的数据处理操作\")\n", "print(\"3. 优化了内存使用和性能\")\n", "print(\"4. 增强了错误处理和日志系统\")\n", "print(\"5. 提供了模块化和可重用的代码结构\")\n", "\n", "print(\"\\n📁 文件结构:\")\n", "print(\"├── merge_bad_or_good.ipynb     # 优化后的主notebook\")\n", "print(\"├── data_labeling_utils.py      # 工具函数模块\")\n", "print(\"└── issues/数据标记任务.md      # 任务记录文档\")\n", "\n", "print(\"\\n🚀 现在您可以:\")\n", "print(\"• 直接运行第一个cell进行快速数据标记\")\n", "print(\"• 使用工具模块在其他项目中重用代码\")\n", "print(\"• 根据需要自定义数据处理流程\")\n", "\n", "print(\"\\n💡 下次使用时，只需要:\")\n", "print(\"```python\")\n", "print(\"from data_labeling_utils import quick_label_data\")\n", "print(\"result = quick_label_data('main.xlsx', 'label.xlsx')\")\n", "print(\"```\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}], "metadata": {"kernelspec": {"display_name": "risk", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}