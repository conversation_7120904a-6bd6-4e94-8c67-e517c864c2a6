#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单字段分类分析工具
使用基本的规则匹配对Excel中的数据字段进行分类和降维
"""

import pandas as pd
import os
import re
from collections import defaultdict, Counter
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleFieldAnalyzer:
    """简单字段分类分析器"""
    
    def __init__(self, excel_file, sheet_name=0):
        """
        初始化字段分类分析器
        
        Args:
            excel_file (str): Excel文件路径
            sheet_name (str/int): 工作表名称或索引
        """
        self.excel_file = excel_file
        self.sheet_name = sheet_name
        self.df = None
        self.field_categories = {}
        self.category_keywords = self._initialize_category_keywords()
    
    def _initialize_category_keywords(self):
        """初始化各分类的关键词"""
        categories = {
            '标识类': ['id', 'uuid', 'guid', '编号', '代码', 'code', 'key', '标识', '主键', '键值'],
            '名称类': ['name', '名称', '标题', 'title', '姓名', '称呼', '全称', '简称', '别名'],
            '描述类': ['desc', '描述', 'description', '说明', '备注', 'remark', 'memo', 'note', '概要', '详情'],
            '状态类': ['status', '状态', '标志', 'flag', '指示', '是否', '启用', '禁用', '激活', '冻结', '有效'],
            '时间类': ['time', '时间', 'date', '日期', '年', '月', '日', '创建', '更新', '修改', '开始', '结束', '期限'],
            '数值类': ['count', '数量', 'amount', '金额', 'number', '次数', '总数', '合计', 'sum', 'total', '百分比', '比率'],
            '类型类': ['type', '类型', '种类', '分类', '类别', 'category', 'kind', 'class', '形式', '模式'],
            '关系类': ['parent', '父', '子', 'child', '关联', '相关', '映射', 'map', 'relation', '引用', '链接'],
            '地址类': ['address', '地址', '位置', 'location', '省', '市', '区', '县', '街道', '路', '号', '邮编', '坐标'],
            '联系类': ['contact', '联系', '电话', 'phone', '邮箱', 'email', '手机', 'mobile', '传真', 'fax', 'qq', '微信'],
            '内容类': ['content', '内容', '正文', 'text', '数据', 'data', '信息', 'info', '值', 'value', '结果'],
            '配置类': ['config', '配置', 'setting', '设置', '选项', 'option', '参数', 'param', '属性', 'property'],
            '排序类': ['order', '排序', '顺序', 'sort', '优先级', 'priority', '级别', 'level', '序号', '序列'],
            '权限类': ['permission', '权限', 'auth', '授权', 'role', '角色', 'access', '访问', '控制', '安全'],
            '路径类': ['path', '路径', 'url', '链接', 'link', '文件', 'file', '目录', 'directory', '路由', 'route'],
            '错误类': ['error', '错误', 'exception', '异常', 'fail', '失败', 'message', '信息', '警告', 'warning']
        }
        return categories
    
    def load_data(self):
        """加载Excel数据"""
        try:
            logger.info(f"正在加载数据文件: {self.excel_file}")
            self.df = pd.read_excel(self.excel_file, sheet_name=self.sheet_name)
            
            # 显示数据基本信息
            logger.info(f"数据加载成功，共 {len(self.df)} 行，{len(self.df.columns)} 列")
            logger.info(f"列名: {list(self.df.columns)}")
            
            return True
        except Exception as e:
            logger.error(f"数据加载失败: {str(e)}")
            return False
    
    def _preprocess_field_name(self, field_name):
        """预处理字段名"""
        if not isinstance(field_name, str):
            return ""
        
        # 转换为小写
        field = field_name.lower()
        
        # 处理驼峰命名
        field = re.sub(r'([a-z])([A-Z])', r'\1_\2', field)
        
        # 处理下划线、点和短横线分隔的单词
        field = re.sub(r'[_\.\-]', ' ', field)
        
        # 移除非字母数字字符
        field = re.sub(r'[^a-z0-9\s]', '', field)
        
        return field.strip()
    
    def _match_category_by_keywords(self, field_name):
        """使用关键词匹配对字段进行分类"""
        processed_field = self._preprocess_field_name(field_name)
        
        # 匹配得分
        category_scores = defaultdict(int)
        
        # 对每个类别的关键词进行匹配
        for category, keywords in self.category_keywords.items():
            for keyword in keywords:
                if keyword.lower() in processed_field:
                    # 关键词在字段名中出现，增加得分
                    # 完全匹配得分更高
                    if processed_field == keyword.lower():
                        category_scores[category] += 5
                    # 以关键词开头得分较高
                    elif processed_field.startswith(keyword.lower()):
                        category_scores[category] += 3
                    # 以关键词结尾得分较高
                    elif processed_field.endswith(keyword.lower()):
                        category_scores[category] += 3
                    # 包含关键词得分一般
                    else:
                        category_scores[category] += 1
        
        # 如果没有匹配到任何类别，返回"未分类"
        if not category_scores:
            return "未分类"
        
        # 返回得分最高的类别
        return max(category_scores.items(), key=lambda x: x[1])[0]
    
    def _simple_chinese_category(self, text):
        """对中文字段名进行简单分类"""
        if not isinstance(text, str) or not text:
            return "未分类"
        
        # 简单的中文关键词匹配
        if any(word in text for word in ['时间', '日期', '年', '月', '日']):
            return "时间类"
        elif any(word in text for word in ['数量', '金额', '总数', '次数', '比率', '百分比']):
            return "数值类"
        elif any(word in text for word in ['描述', '说明', '备注', '详情']):
            return "描述类"
        elif any(word in text for word in ['状态', '标志', '是否', '启用', '禁用', '有效']):
            return "状态类"
        elif any(word in text for word in ['编号', '代码', '标识', '主键']):
            return "标识类"
        elif any(word in text for word in ['名称', '标题', '姓名', '全称', '简称']):
            return "名称类"
        elif any(word in text for word in ['类型', '种类', '分类', '类别']):
            return "类型类"
        elif any(word in text for word in ['父', '子', '关联', '相关', '映射', '关系']):
            return "关系类"
        elif any(word in text for word in ['地址', '位置', '省', '市', '区', '县', '街道']):
            return "地址类"
        elif any(word in text for word in ['联系', '电话', '邮箱', '手机', '传真']):
            return "联系类"
        
        # 根据字段长度进行简单推断
        if len(text) <= 4:
            return "名称类"  # 短字段名可能是名称
        elif len(text) >= 10:
            return "描述类"  # 长字段名可能是描述
        
        return "未分类"
    
    def categorize_fields(self):
        """对字段进行分类"""
        if self.df is None:
            logger.error("请先加载数据")
            return False
        
        logger.info("开始对字段进行分类...")
        
        # 创建结果列表
        categories = []
        category_counts = Counter()
        
        # 确定要处理的字段列名（英文名和中文名）
        ename_col = 'ENAME' if 'ENAME' in self.df.columns else None
        cname_col = 'CNAME' if 'CNAME' in self.df.columns else None
        
        if not ename_col and not cname_col:
            logger.error("数据中找不到ENAME或CNAME列")
            return False
        
        # 对每行进行分类
        for i, row in self.df.iterrows():
            # 优先使用英文名进行关键词匹配
            if ename_col and pd.notna(row[ename_col]):
                category = self._match_category_by_keywords(str(row[ename_col]))
                
                # 如果英文名无法分类，尝试使用中文名
                if category == "未分类" and cname_col and pd.notna(row[cname_col]):
                    category = self._simple_chinese_category(str(row[cname_col]))
            # 如果没有英文名，使用中文名进行分类
            elif cname_col and pd.notna(row[cname_col]):
                category = self._simple_chinese_category(str(row[cname_col]))
            else:
                category = "未分类"
            
            categories.append(category)
            category_counts[category] += 1
            
            # 记录结果
            self.field_categories[i] = category
        
        # 添加分类结果到数据框
        self.df['字段分类'] = categories
        
        # 显示分类统计
        logger.info("字段分类统计:")
        for category, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {category}: {count} 个字段 ({count/len(self.df)*100:.1f}%)")
        
        return True
    
    def find_similar_fields(self):
        """查找完全相同的字段名"""
        if self.df is None:
            logger.error("请先加载数据")
            return False
        
        logger.info("开始查找相同字段...")
        
        # 使用中文名进行匹配
        if 'CNAME' in self.df.columns:
            # 统计每个中文名出现的次数
            cname_counts = Counter(self.df['CNAME'].dropna())
            
            # 找出出现多次的字段名
            duplicates = {name: count for name, count in cname_counts.items() if count > 1}
            
            if duplicates:
                logger.info(f"找到 {len(duplicates)} 个重复的中文字段名:")
                for name, count in sorted(duplicates.items(), key=lambda x: x[1], reverse=True)[:20]:
                    logger.info(f"  '{name}' 出现 {count} 次")
                
                if len(duplicates) > 20:
                    logger.info(f"  以及其他 {len(duplicates) - 20} 个重复字段...")
            else:
                logger.info("没有找到重复的中文字段名")
        
        # 使用英文名进行匹配
        if 'ENAME' in self.df.columns:
            # 统计每个英文名出现的次数
            ename_counts = Counter(self.df['ENAME'].dropna())
            
            # 找出出现多次的字段名
            duplicates = {name: count for name, count in ename_counts.items() if count > 1}
            
            if duplicates:
                logger.info(f"找到 {len(duplicates)} 个重复的英文字段名:")
                for name, count in sorted(duplicates.items(), key=lambda x: x[1], reverse=True)[:20]:
                    logger.info(f"  '{name}' 出现 {count} 次")
                
                if len(duplicates) > 20:
                    logger.info(f"  以及其他 {len(duplicates) - 20} 个重复字段...")
            else:
                logger.info("没有找到重复的英文字段名")
        
        return True
    
    def analyze_resource_codes(self):
        """分析资源编码与字段的关系"""
        if self.df is None or 'RESOURCE_CODE' not in self.df.columns:
            logger.error("请先加载数据，并确保数据中包含RESOURCE_CODE列")
            return False
        
        logger.info("开始分析资源编码...")
        
        # 统计每个资源编码的字段数量
        resource_counts = Counter(self.df['RESOURCE_CODE'])
        total_resources = len(resource_counts)
        
        logger.info(f"共有 {total_resources} 个不同的资源编码")
        
        # 显示字段数量最多的几个资源编码
        top_resources = resource_counts.most_common(10)
        logger.info("字段数量最多的资源编码:")
        for resource, count in top_resources:
            logger.info(f"  {resource}: {count} 个字段")
        
        # 分析每个资源编码包含的字段分类情况
        if '字段分类' in self.df.columns:
            logger.info("分析资源编码的字段分类分布...")
            
            resource_categories = {}
            for resource in resource_counts:
                # 获取该资源的所有字段
                resource_df = self.df[self.df['RESOURCE_CODE'] == resource]
                
                # 统计各分类的数量
                category_counts = Counter(resource_df['字段分类'])
                
                # 保存结果
                resource_categories[resource] = category_counts
            
            # 显示几个资源的分类分布
            for resource, count in top_resources[:5]:
                logger.info(f"资源编码 {resource} 的字段分类分布:")
                categories = resource_categories[resource]
                for category, cat_count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
                    percentage = cat_count / count * 100
                    logger.info(f"  {category}: {cat_count} 个字段 ({percentage:.1f}%)")
        
        return True
    
    def export_categories(self, output_file='field_categories_simple.xlsx'):
        """
        导出字段分类结果到Excel
        
        Args:
            output_file (str): 输出文件名
        """
        if self.df is None or '字段分类' not in self.df.columns:
            logger.error("请先对字段进行分类")
            return False
        
        logger.info(f"正在导出分类结果到 {output_file}...")
        
        try:
            # 按分类对数据框进行排序
            sorted_df = self.df.sort_values('字段分类')
            
            # 导出到Excel
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 导出所有数据
                sorted_df.to_excel(writer, sheet_name='所有字段', index=False)
                
                # 为每个分类创建单独的工作表
                for category in sorted(sorted_df['字段分类'].unique()):
                    # 对于每个分类，创建一个子数据框
                    category_df = sorted_df[sorted_df['字段分类'] == category]
                    
                    # Excel工作表名称不能超过31个字符
                    sheet_name = category[:30] if len(category) > 30 else category
                    
                    # 导出到工作表
                    category_df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # 创建统计工作表
                stats = pd.DataFrame({
                    '分类': sorted(Counter(sorted_df['字段分类']).keys()),
                    '字段数量': [Counter(sorted_df['字段分类'])[cat] for cat in sorted(Counter(sorted_df['字段分类']).keys())]
                })
                stats = stats.sort_values('字段数量', ascending=False)
                stats.to_excel(writer, sheet_name='分类统计', index=False)
            
            logger.info(f"分类结果已成功导出到 {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"导出失败: {str(e)}")
            return False
    
    def analyze_field_patterns(self):
        """分析字段名称的模式"""
        if self.df is None:
            logger.error("请先加载数据")
            return False
        
        logger.info("开始分析字段名称模式...")
        
        # 分析英文字段名
        if 'ENAME' in self.df.columns:
            english_fields = self.df['ENAME'].dropna().astype(str).tolist()
            
            # 分析命名风格（驼峰、下划线等）
            camel_case = sum(1 for field in english_fields if re.search(r'[a-z][A-Z]', field))
            snake_case = sum(1 for field in english_fields if '_' in field)
            kebab_case = sum(1 for field in english_fields if '-' in field)
            
            logger.info(f"英文字段命名风格分析 (共 {len(english_fields)} 个字段):")
            logger.info(f"  驼峰命名 (camelCase): {camel_case} 个 ({camel_case/len(english_fields)*100:.1f}%)")
            logger.info(f"  下划线命名 (snake_case): {snake_case} 个 ({snake_case/len(english_fields)*100:.1f}%)")
            logger.info(f"  连字符命名 (kebab-case): {kebab_case} 个 ({kebab_case/len(english_fields)*100:.1f}%)")
            
            # 分析字段长度
            field_lengths = [len(field) for field in english_fields]
            avg_length = sum(field_lengths) / len(field_lengths)
            max_length = max(field_lengths)
            min_length = min(field_lengths)
            
            logger.info(f"英文字段长度分析:")
            logger.info(f"  平均长度: {avg_length:.1f} 个字符")
            logger.info(f"  最长字段: {max_length} 个字符")
            logger.info(f"  最短字段: {min_length} 个字符")
            
            # 分析常见前缀
            prefixes = []
            for field in english_fields:
                if '_' in field:
                    prefix = field.split('_')[0]
                else:
                    match = re.match(r'^[a-z]+', field)
                    prefix = match.group(0) if match else field[:3]
                prefixes.append(prefix.lower())
            
            prefix_counts = Counter(prefixes)
            logger.info("常见字段前缀:")
            for prefix, count in prefix_counts.most_common(10):
                logger.info(f"  '{prefix}': {count} 个字段")
        
        # 分析中文字段名
        if 'CNAME' in self.df.columns:
            chinese_fields = self.df['CNAME'].dropna().astype(str).tolist()
            
            # 分析字段长度
            field_lengths = [len(field) for field in chinese_fields]
            avg_length = sum(field_lengths) / len(field_lengths)
            max_length = max(field_lengths)
            min_length = min(field_lengths)
            
            logger.info(f"中文字段长度分析:")
            logger.info(f"  平均长度: {avg_length:.1f} 个字符")
            logger.info(f"  最长字段: {max_length} 个字符")
            logger.info(f"  最短字段: {min_length} 个字符")
            
            # 分析常见中文字符
            char_counts = Counter(''.join(chinese_fields))
            logger.info("常见中文字符:")
            for char, count in char_counts.most_common(20):
                logger.info(f"  '{char}': {count} 次")
        
        return True

def main():
    """主函数"""
    # 输入文件
    input_file = "crd_datasource_params_export.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        logger.error(f"文件不存在: {input_file}")
        return
    
    # 创建分析器
    analyzer = SimpleFieldAnalyzer(input_file)
    
    # 加载数据
    if not analyzer.load_data():
        return
    
    # 对字段进行分类
    if not analyzer.categorize_fields():
        return
    
    # 查找相同字段
    analyzer.find_similar_fields()
    
    # 分析资源编码
    analyzer.analyze_resource_codes()
    
    # 分析字段命名模式
    analyzer.analyze_field_patterns()
    
    # 导出分类结果
    analyzer.export_categories()
    
    logger.info("字段分析完成！")

if __name__ == "__main__":
    main() 