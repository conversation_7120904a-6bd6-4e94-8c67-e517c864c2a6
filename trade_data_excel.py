from sqlalchemy import create_engine
import pandas as pd
import json
from datetime import datetime


engine = create_engine('mysql+pymysql://root:root%402024@192.168.2.103:3306/smartdecision?charset=utf8mb4')


sql1 = """
SELECT ctsd.SERIAL_NUMBER, ctsd.RESOURCE_CODE, ctsd.SERVER_RESPONSE_DATA
FROM crd_trade_server_data ctsd
INNER JOIN crd_trade_log ctl ON ctsd.SERIAL_NUMBER = ctl.SERIAL_NUMBER
WHERE ctl.PRODUCT_CODE = '202088888820250206093418' ;
"""
df_data = pd.read_sql(sql1, engine)

sql2 = """
select RESOURCE_CODE, ENAME, CNAME 
from crd_datasource_params 
where FIELD_TYPE='2' and VERSION='V1' 
and RESOURCE_CODE not in ('yinkesit10001', '976652b23d41493c89396367fbfba2a9', 'test001') 
and CNAME not in ('返回码', '返回代码', '错误码', '状态') 
and ENAME not in ('supplement_case_list','loan_willingness_profile','execution_limited','execution_pro','loan_rate_profile') 
order by RESOURCE_CODE;
"""
df_params = pd.read_sql(sql2, engine)

# 2. 生成唯一列名，只用于数据处理，不用于输出
cname_counts = df_params['CNAME'].value_counts()
def get_unique_key(row):
    if cname_counts[row['CNAME']] > 1:
        return f"{row['CNAME']}({row['RESOURCE_CODE']})"
    else:
        return row['CNAME']
df_params['UNIQUE_KEY'] = df_params.apply(get_unique_key, axis=1)
# 保留输出用的列名
df_params['OUTPUT_CNAME'] = df_params['ENAME']

# （RESOURCE_CODE, ENAME）-> 数据处理用KEY
param_map = {(row['RESOURCE_CODE'], row['ENAME']): row['UNIQUE_KEY'] for _, row in df_params.iterrows()}

# 3. 数据预处理 (SERIAL_NUMBER, RESOURCE_CODE) -> {字段: 值}
data_map = {}
for _, row in df_data.iterrows():
    sn = row['SERIAL_NUMBER']
    rc = row['RESOURCE_CODE']
    try:
        data = json.loads(row['SERVER_RESPONSE_DATA'])
        def normalize_null(v):
            if v is None:
                return -999
            if isinstance(v, str) and v.strip().lower().replace('"', '') == 'null':
                return -999
            return v
        data = {k: normalize_null(v) for k, v in data.items()}
    except Exception:
        data = {}
    data_map[(sn, rc)] = data

# 4. 生成结果数据
serial_numbers = df_data['SERIAL_NUMBER'].unique()
result_rows = []
for sn in serial_numbers:
    row_dict = {'SERIAL_NUMBER': sn}
    for _, row in df_params.iterrows():
        rc = row['RESOURCE_CODE']
        en = row['ENAME']
        col_key = row['UNIQUE_KEY']
        data = data_map.get((sn, rc), None)
        value = data.get(en) if data and en in data else None
        row_dict[col_key] = value
    result_rows.append(row_dict)

# 5. 构建输出DataFrame
columns = ['SERIAL_NUMBER'] + list(df_params['UNIQUE_KEY'])
output_df = pd.DataFrame(result_rows, columns=columns)

# 6. 设置输出列标题（全部用CNAME而不带(RESOURCE_CODE)）
col_rename = {'SERIAL_NUMBER': 'SERIAL_NUMBER'}
for _, row in df_params.iterrows():
    col_rename[row['UNIQUE_KEY']] = row['OUTPUT_CNAME']
output_df.rename(columns=col_rename, inplace=True)

# 7. 导出到Excel
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
filename = f"result_{timestamp}.xlsx"
output_df.to_excel(filename, index=False)
print("处理完成，结果已保存为 " + filename)
