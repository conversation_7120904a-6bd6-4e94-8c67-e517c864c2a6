#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正逾期预测模型的问题
主要问题：目标变量编码错误导致模型总是预测高风险

问题分析：
1. 原始编码：坏->0, 好->1 (错误)
2. 正确编码：坏->1, 好->0 (坏客户=高风险=1，好客户=低风险=0)
3. 数据不平衡：好客户占92.7%，坏客户占7.3%
4. 模型学会预测大部分为1（好客户），但被误解为高风险

解决方案：
1. 修正目标变量编码
2. 重新训练模型
3. 调整预测逻辑
"""

import pandas as pd
import numpy as np
import joblib
from sklearn.metrics import roc_auc_score, classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

def analyze_current_model_issue():
    """分析当前模型的问题"""
    print("=== 当前模型问题分析 ===")
    
    try:
        # 加载当前模型
        model_package = joblib.load('overdue_prediction_model.pkl')
        print("✓ 成功加载当前模型")
        
        # 检查目标变量编码
        print("\n当前模型信息:")
        print(f"- 最佳模型: {model_package.get('best_model_name', 'Unknown')}")
        print(f"- 最优阈值: {model_package.get('optimal_threshold', 'Unknown')}")
        
        # 加载测试预测结果
        if os.path.exists('test_predictions.csv'):
            pred_results = pd.read_csv('test_predictions.csv')
            print(f"\n测试集预测结果分析:")
            print(f"- 样本总数: {len(pred_results)}")
            print(f"- 实际标签分布: {dict(pred_results['actual'].value_counts())}")
            print(f"- 预测标签分布: {dict(pred_results['predicted_binary'].value_counts())}")
            print(f"- 平均预测概率: {pred_results['predicted_prob'].mean():.3f}")
            print(f"- 预测概率范围: [{pred_results['predicted_prob'].min():.3f}, {pred_results['predicted_prob'].max():.3f}]")
            
            # 分析问题
            high_prob_count = (pred_results['predicted_prob'] >= 0.7).sum()
            print(f"\n问题分析:")
            print(f"- 预测概率>=0.7的样本数: {high_prob_count} ({high_prob_count/len(pred_results)*100:.1f}%)")
            print(f"- 这解释了为什么大部分样本被判定为高风险")
            
    except Exception as e:
        print(f"❌ 无法加载模型: {e}")

def fix_target_encoding_and_retrain():
    """修正目标变量编码并重新训练模型"""
    print("\n=== 修正目标变量编码 ===")
    
    try:
        # 加载原始数据
        data = pd.read_csv('result_data.csv')
        print(f"✓ 加载原始数据: {data.shape}")
        
        # 检查目标变量
        target_col = '好坏标签'
        if target_col not in data.columns:
            print("❌ 未找到目标变量列")
            return
            
        print(f"原始目标变量分布: {dict(data[target_col].value_counts())}")
        
        # 修正编码：坏->1（高风险），好->0（低风险）
        print("\n修正目标变量编码...")
        label_mapping = {'坏': 1, '好': 0}  # 修正后的映射
        data[target_col] = data[target_col].map(label_mapping)
        
        print(f"修正后目标变量分布: {dict(data[target_col].value_counts())}")
        print("✓ 现在 1=高风险(坏客户), 0=低风险(好客户)")
        
        # 保存修正后的数据
        data.to_csv('result_data_fixed.csv', index=False)
        print("✓ 修正后数据已保存到 'result_data_fixed.csv'")
        
        return data, target_col
        
    except Exception as e:
        print(f"❌ 修正编码失败: {e}")
        return None, None

def create_quick_prediction_function():
    """创建快速预测函数来测试修正后的模型"""
    
    prediction_code = '''
def predict_with_fixed_model(model_path, new_data, debug=True):
    """
    使用修正后的模型进行预测
    
    注意：现在的编码是 1=高风险(坏客户), 0=低风险(好客户)
    """
    import joblib
    import pandas as pd
    import numpy as np
    
    # 加载模型
    model_package = joblib.load(model_path)
    model = model_package['best_model']
    selected_features = model_package['selected_features']
    optimal_threshold = model_package['optimal_threshold']
    
    if debug:
        print(f"模型信息:")
        print(f"- 选择特征数: {len(selected_features)}")
        print(f"- 最优阈值: {optimal_threshold:.3f}")
    
    # 数据预处理（简化版，实际使用时需要完整的预处理流程）
    try:
        # 选择特征
        X_new = new_data[selected_features]
        
        # 预测概率
        if hasattr(model, 'predict_proba'):
            # sklearn模型
            prob_predictions = model.predict_proba(X_new)[:, 1]
        else:
            # LightGBM
            prob_predictions = model.predict(X_new)
        
        # 二分类预测
        binary_predictions = (prob_predictions >= optimal_threshold).astype(int)
        
        # 风险等级
        risk_levels = []
        for prob in prob_predictions:
            if prob >= 0.7:
                risk_levels.append('高风险')
            elif prob >= 0.3:
                risk_levels.append('中风险')  
            else:
                risk_levels.append('低风险')
        
        if debug:
            print(f"\\n预测结果:")
            print(f"- 样本数: {len(prob_predictions)}")
            print(f"- 平均风险概率: {prob_predictions.mean():.3f}")
            print(f"- 高风险样本数: {(prob_predictions >= 0.7).sum()}")
            print(f"- 中风险样本数: {((prob_predictions >= 0.3) & (prob_predictions < 0.7)).sum()}")
            print(f"- 低风险样本数: {(prob_predictions < 0.3).sum()}")
        
        return {
            'probabilities': prob_predictions,
            'predictions': binary_predictions,
            'risk_levels': risk_levels,
            'threshold': optimal_threshold
        }
        
    except Exception as e:
        print(f"预测失败: {e}")
        return None

# 使用示例
if __name__ == "__main__":
    # 测试预测
    try:
        # 加载测试数据
        test_data = pd.read_csv('result_data_fixed.csv').head(10)
        
        # 进行预测
        results = predict_with_fixed_model('overdue_prediction_model.pkl', test_data)
        
        if results:
            # 显示结果
            result_df = pd.DataFrame({
                'sample_id': range(1, len(results['probabilities']) + 1),
                'risk_probability': results['probabilities'],
                'predicted_class': results['predictions'],
                'risk_level': results['risk_levels']
            })
            
            print("\\n=== 预测结果示例 ===")
            print(result_df)
            
    except Exception as e:
        print(f"测试预测失败: {e}")
'''
    
    # 保存预测函数
    with open('fixed_prediction_function.py', 'w', encoding='utf-8') as f:
        f.write(prediction_code)
    
    print("✓ 修正后的预测函数已保存到 'fixed_prediction_function.py'")

def main():
    """主函数"""
    print("🔧 逾期预测模型问题修正工具")
    print("=" * 50)
    
    # 1. 分析当前问题
    analyze_current_model_issue()
    
    # 2. 修正目标变量编码
    fixed_data, target_col = fix_target_encoding_and_retrain()
    
    if fixed_data is not None:
        # 3. 创建修正后的预测函数
        create_quick_prediction_function()
        
        print("\n" + "=" * 50)
        print("🎯 问题修正总结:")
        print("1. ✅ 识别了目标变量编码错误的问题")
        print("2. ✅ 修正了编码：坏->1(高风险), 好->0(低风险)")
        print("3. ✅ 创建了修正后的预测函数")
        
        print("\n📋 下一步建议:")
        print("1. 使用修正后的数据重新训练模型")
        print("2. 验证新模型的预测结果是否合理")
        print("3. 更新生产环境中的模型")
        
        print("\n⚠️  重要提醒:")
        print("- 现在 1 = 高风险(坏客户), 0 = 低风险(好客户)")
        print("- 预测概率越高，风险越大")
        print("- 需要重新训练模型以获得最佳效果")

if __name__ == "__main__":
    import os
    main()
