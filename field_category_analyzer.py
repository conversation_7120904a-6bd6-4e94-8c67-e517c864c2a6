#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段分类分析工具
使用自然语言处理和规则匹配对数据字段进行语义分类
"""

import pandas as pd
import numpy as np
import re
import jieba
import jieba.posseg as pseg
from collections import defaultdict, Counter
import os
import logging
import matplotlib.pyplot as plt
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import openpyxl
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FieldCategoryAnalyzer:
    """字段分类分析器"""
    
    def __init__(self, excel_file, sheet_name=0):
        """
        初始化字段分类分析器
        
        Args:
            excel_file (str): Excel文件路径
            sheet_name (str/int): 工作表名称或索引
        """
        self.excel_file = excel_file
        self.sheet_name = sheet_name
        self.df = None
        self.field_categories = {}
        self.category_keywords = self._initialize_category_keywords()
        
        # 加载自定义词典
        self._load_custom_dict()
    
    def _load_custom_dict(self):
        """加载自定义词典到jieba"""
        # 数据库和IT相关术语
        custom_terms = [
            ('资源编码', 'n'), ('返回码', 'n'), ('错误码', 'n'), ('状态码', 'n'),
            ('字段类型', 'n'), ('数据源', 'n'), ('参数', 'n'), ('配置', 'n'),
            ('主键', 'n'), ('外键', 'n'), ('索引', 'n'), ('视图', 'n'),
            ('数据库', 'n'), ('表', 'n'), ('列', 'n'), ('行', 'n'),
            ('查询', 'v'), ('插入', 'v'), ('更新', 'v'), ('删除', 'v'),
            ('字符串', 'n'), ('整数', 'n'), ('浮点数', 'n'), ('布尔值', 'n'),
            ('日期', 'n'), ('时间戳', 'n'), ('二进制', 'n'), ('JSON', 'n')
        ]
        
        # 添加到jieba词典
        for term, pos in custom_terms:
            jieba.add_word(term, freq=1000, tag=pos)
    
    def _initialize_category_keywords(self):
        """初始化各分类的关键词"""
        categories = {
            '标识类': ['id', 'uuid', 'guid', '编号', '代码', 'code', 'key', '标识', '主键', '键值'],
            '名称类': ['name', '名称', '标题', 'title', '姓名', '称呼', '全称', '简称', '别名'],
            '描述类': ['desc', '描述', 'description', '说明', '备注', 'remark', 'memo', 'note', '概要', '详情'],
            '状态类': ['status', '状态', '标志', 'flag', '指示', '是否', '启用', '禁用', '激活', '冻结', '有效'],
            '时间类': ['time', '时间', 'date', '日期', '年', '月', '日', '创建', '更新', '修改', '开始', '结束', '期限'],
            '数值类': ['count', '数量', 'amount', '金额', 'number', '次数', '总数', '合计', 'sum', 'total', '百分比', '比率'],
            '类型类': ['type', '类型', '种类', '分类', '类别', 'category', 'kind', 'class', '形式', '模式'],
            '关系类': ['parent', '父', '子', 'child', '关联', '相关', '映射', 'map', 'relation', '引用', '链接'],
            '地址类': ['address', '地址', '位置', 'location', '省', '市', '区', '县', '街道', '路', '号', '邮编', '坐标'],
            '联系类': ['contact', '联系', '电话', 'phone', '邮箱', 'email', '手机', 'mobile', '传真', 'fax', 'qq', '微信'],
            '内容类': ['content', '内容', '正文', 'text', '数据', 'data', '信息', 'info', '值', 'value', '结果'],
            '配置类': ['config', '配置', 'setting', '设置', '选项', 'option', '参数', 'param', '属性', 'property'],
            '排序类': ['order', '排序', '顺序', 'sort', '优先级', 'priority', '级别', 'level', '序号', '序列'],
            '权限类': ['permission', '权限', 'auth', '授权', 'role', '角色', 'access', '访问', '控制', '安全'],
            '路径类': ['path', '路径', 'url', '链接', 'link', '文件', 'file', '目录', 'directory', '路由', 'route'],
            '错误类': ['error', '错误', 'exception', '异常', 'fail', '失败', 'message', '信息', '警告', 'warning']
        }
        return categories
    
    def load_data(self):
        """加载Excel数据"""
        try:
            logger.info(f"正在加载数据文件: {self.excel_file}")
            self.df = pd.read_excel(self.excel_file, sheet_name=self.sheet_name)
            
            # 显示数据基本信息
            logger.info(f"数据加载成功，共 {len(self.df)} 行，{len(self.df.columns)} 列")
            logger.info(f"列名: {list(self.df.columns)}")
            
            return True
        except Exception as e:
            logger.error(f"数据加载失败: {str(e)}")
            return False
    
    def _preprocess_field_name(self, field_name):
        """预处理字段名"""
        if not isinstance(field_name, str):
            return ""
        
        # 转换为小写
        field = field_name.lower()
        
        # 处理驼峰命名
        field = re.sub(r'([a-z])([A-Z])', r'\1_\2', field)
        
        # 处理下划线、点和短横线分隔的单词
        field = re.sub(r'[_\.\-]', ' ', field)
        
        # 移除非字母数字字符
        field = re.sub(r'[^a-z0-9\s]', '', field)
        
        return field.strip()
    
    def _match_category_by_keywords(self, field_name):
        """使用关键词匹配对字段进行分类"""
        processed_field = self._preprocess_field_name(field_name)
        
        # 匹配得分
        category_scores = defaultdict(int)
        
        # 对每个类别的关键词进行匹配
        for category, keywords in self.category_keywords.items():
            for keyword in keywords:
                if keyword.lower() in processed_field:
                    # 关键词在字段名中出现，增加得分
                    # 完全匹配得分更高
                    if processed_field == keyword.lower():
                        category_scores[category] += 5
                    # 以关键词开头得分较高
                    elif processed_field.startswith(keyword.lower()):
                        category_scores[category] += 3
                    # 以关键词结尾得分较高
                    elif processed_field.endswith(keyword.lower()):
                        category_scores[category] += 3
                    # 包含关键词得分一般
                    else:
                        category_scores[category] += 1
        
        # 如果没有匹配到任何类别，返回"未分类"
        if not category_scores:
            return "未分类"
        
        # 返回得分最高的类别
        return max(category_scores.items(), key=lambda x: x[1])[0]
    
    def _match_category_by_nlp(self, text):
        """使用自然语言处理技术对中文字段名进行分类"""
        if not isinstance(text, str) or not text:
            return "未分类"
        
        # 使用jieba进行词性标注
        words = pseg.cut(text)
        tags = []
        
        for word, flag in words:
            # 词性：n名词, v动词, a形容词, m数词, t时间词, f方位词, s处所词
            tags.append((word, flag))
        
        # 基于词性进行分类
        # 如果包含时间词，优先归为时间类
        for word, flag in tags:
            if flag == 't' or word in ['时间', '日期', '年', '月', '日']:
                return "时间类"
        
        # 如果包含数词和量词组合，可能是数值类
        has_number = any(flag == 'm' for _, flag in tags)
        if has_number:
            return "数值类"
        
        # 如果字段名较长，并且主要是名词，可能是描述类
        nouns = [word for word, flag in tags if flag.startswith('n')]
        if len(nouns) >= 2 and len(text) > 4:
            return "描述类"
        
        # 如果包含表示关系的词，可能是关系类
        relation_words = ['关联', '相关', '父', '子', '映射', '关系']
        if any(word in relation_words for word, _ in tags):
            return "关系类"
        
        # 如果包含表示状态的词，可能是状态类
        status_words = ['状态', '标志', '是否', '有效', '启用', '禁用']
        if any(word in status_words for word, _ in tags):
            return "状态类"
        
        # 如果字段名简短，并且是名词，可能是名称类
        if len(text) <= 4 and all(flag.startswith('n') for _, flag in tags):
            return "名称类"
        
        # 默认返回未分类
        return "未分类"
    
    def categorize_fields(self):
        """对字段进行分类"""
        if self.df is None:
            logger.error("请先加载数据")
            return False
        
        logger.info("开始对字段进行分类...")
        
        # 创建结果列表
        categories = []
        category_counts = Counter()
        
        # 确定要处理的字段列名（英文名和中文名）
        ename_col = 'ENAME' if 'ENAME' in self.df.columns else None
        cname_col = 'CNAME' if 'CNAME' in self.df.columns else None
        
        if not ename_col and not cname_col:
            logger.error("数据中找不到ENAME或CNAME列")
            return False
        
        # 对每行进行分类
        for i, row in self.df.iterrows():
            # 优先使用英文名进行关键词匹配
            if ename_col and pd.notna(row[ename_col]):
                category = self._match_category_by_keywords(str(row[ename_col]))
            # 如果没有英文名或未分类，使用中文名进行NLP分类
            elif cname_col and pd.notna(row[cname_col]):
                category = self._match_category_by_nlp(str(row[cname_col]))
            else:
                category = "未分类"
            
            categories.append(category)
            category_counts[category] += 1
            
            # 记录结果
            self.field_categories[i] = category
        
        # 添加分类结果到数据框
        self.df['字段分类'] = categories
        
        # 显示分类统计
        logger.info("字段分类统计:")
        for category, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {category}: {count} 个字段 ({count/len(self.df)*100:.1f}%)")
        
        return True
    
    def get_similar_fields(self, threshold=0.7):
        """
        查找相似的字段
        
        Args:
            threshold (float): 相似度阈值，0-1之间
            
        Returns:
            list: 相似字段组列表
        """
        if self.df is None or 'CNAME' not in self.df.columns:
            logger.error("请先加载数据，并确保数据中包含CNAME列")
            return []
        
        logger.info(f"开始查找相似字段 (相似度阈值: {threshold})...")
        
        # 提取中文字段名
        field_names = self.df['CNAME'].fillna('').astype(str).tolist()
        
        # 使用TF-IDF向量化字段名
        vectorizer = TfidfVectorizer(analyzer='char', ngram_range=(1, 2))
        tfidf_matrix = vectorizer.fit_transform(field_names)
        
        # 计算余弦相似度
        similarity_matrix = cosine_similarity(tfidf_matrix)
        np.fill_diagonal(similarity_matrix, 0)  # 去掉自身的相似度
        
        # 查找相似字段组
        similar_groups = []
        processed_indices = set()
        
        for i in range(len(field_names)):
            if i in processed_indices:
                continue
                
            # 找出与当前字段相似的所有字段
            similar_indices = np.where(similarity_matrix[i] >= threshold)[0]
            
            if len(similar_indices) > 0:
                # 创建相似字段组
                group = [i] + [idx for idx in similar_indices if idx != i]
                
                # 只有当组中有多个字段时才添加
                if len(group) > 1:
                    similar_fields = [(idx, field_names[idx], self.df.iloc[idx]['RESOURCE_CODE'] 
                                      if 'RESOURCE_CODE' in self.df.columns else '') for idx in group]
                    similar_groups.append(similar_fields)
                    
                    # 标记为已处理
                    processed_indices.update(group)
        
        # 显示结果
        logger.info(f"共找到 {len(similar_groups)} 个相似字段组")
        for i, group in enumerate(similar_groups[:10], 1):  # 只显示前10组
            logger.info(f"相似组 {i}: {', '.join([field for _, field, _ in group])}")
        
        if len(similar_groups) > 10:
            logger.info(f"... 以及 {len(similar_groups) - 10} 个其他相似组")
            
        return similar_groups
    
    def visualize_categories(self):
        """可视化字段分类统计"""
        if self.df is None or '字段分类' not in self.df.columns:
            logger.error("请先对字段进行分类")
            return False
        
        logger.info("正在生成字段分类统计图...")
        
        # 统计各类别的数量
        category_counts = Counter(self.df['字段分类'])
        
        # 过滤掉计数太少的类别，归为"其他"
        threshold = max(1, len(self.df) * 0.01)  # 至少占1%
        filtered_categories = {}
        other_count = 0
        
        for category, count in category_counts.items():
            if count >= threshold:
                filtered_categories[category] = count
            else:
                other_count += count
        
        if other_count > 0:
            filtered_categories['其他'] = other_count
        
        # 按计数降序排序
        sorted_categories = dict(sorted(filtered_categories.items(), key=lambda x: x[1], reverse=True))
        
        # 创建饼图
        plt.figure(figsize=(12, 8))
        labels = sorted_categories.keys()
        sizes = sorted_categories.values()
        explode = [0.1 if category == "未分类" else 0 for category in labels]  # 突出显示"未分类"
        
        colors = plt.cm.Paired(np.linspace(0, 1, len(labels)))
        
        wedges, texts, autotexts = plt.pie(
            sizes, 
            explode=explode,
            labels=labels, 
            autopct='%1.1f%%',
            shadow=True, 
            startangle=90,
            colors=colors
        )
        
        # 设置字体大小
        plt.setp(autotexts, size=9, weight='bold')
        plt.setp(texts, size=10)
        
        # 添加标题
        plt.title('字段分类分布', fontsize=14)
        plt.axis('equal')  # 保证饼图是圆的
        
        # 保存图片
        plt.savefig('field_categories.png', dpi=300, bbox_inches='tight')
        logger.info("字段分类统计图已保存为 field_categories.png")
        
        # 创建条形图（显示详细数量）
        plt.figure(figsize=(12, 8))
        categories = list(sorted_categories.keys())
        counts = list(sorted_categories.values())
        
        bars = plt.bar(categories, counts, color=colors)
        
        # 在条形上方显示具体数值
        for bar in bars:
            height = bar.get_height()
            plt.text(
                bar.get_x() + bar.get_width()/2.,
                height + 0.5,
                f'{int(height)}',
                ha='center', 
                va='bottom',
                fontsize=9
            )
        
        plt.xlabel('字段分类')
        plt.ylabel('字段数量')
        plt.title('各类别字段数量统计')
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        
        # 保存图片
        plt.savefig('field_categories_bar.png', dpi=300, bbox_inches='tight')
        logger.info("字段分类条形图已保存为 field_categories_bar.png")
        
        return True
    
    def export_results(self, output_file='field_categories_result.xlsx'):
        """
        导出字段分类结果到Excel
        
        Args:
            output_file (str): 输出文件名
        """
        if self.df is None or '字段分类' not in self.df.columns:
            logger.error("请先对字段进行分类")
            return False
        
        logger.info(f"正在导出分类结果到 {output_file}...")
        
        # 创建一个带有颜色的Excel文件
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 导出所有数据
            self.df.to_excel(writer, sheet_name='所有字段', index=False)
            
            # 为每个分类创建单独的工作表
            for category in sorted(self.df['字段分类'].unique()):
                category_df = self.df[self.df['字段分类'] == category]
                category_df.to_excel(writer, sheet_name=f'{category[:10]}', index=False)
            
            # 获取工作簿对象
            workbook = writer.book
            
            # 美化"所有字段"工作表
            sheet = workbook['所有字段']
            self._format_worksheet(sheet)
            
            # 为每个分类的工作表设置样式
            for category in sorted(self.df['字段分类'].unique()):
                sheet_name = f'{category[:10]}'
                if sheet_name in workbook.sheetnames:
                    sheet = workbook[sheet_name]
                    self._format_worksheet(sheet)
        
        logger.info(f"分类结果已成功导出到 {output_file}")
        
        # 导出相似字段
        similar_groups = self.get_similar_fields(threshold=0.7)
        if similar_groups:
            similar_file = 'similar_fields.xlsx'
            logger.info(f"正在导出相似字段组到 {similar_file}...")
            
            # 创建DataFrame
            similar_data = []
            for group_id, group in enumerate(similar_groups, 1):
                for idx, field_name, resource_code in group:
                    similar_data.append({
                        '相似组ID': group_id,
                        '字段名': field_name,
                        '资源编码': resource_code,
                        '原始索引': idx
                    })
            
            similar_df = pd.DataFrame(similar_data)
            
            # 导出到Excel
            with pd.ExcelWriter(similar_file, engine='openpyxl') as writer:
                similar_df.to_excel(writer, sheet_name='相似字段', index=False)
                
                # 获取工作簿对象
                workbook = writer.book
                sheet = workbook['相似字段']
                self._format_worksheet(sheet)
            
            logger.info(f"相似字段组已成功导出到 {similar_file}")
            
        return True
    
    def _format_worksheet(self, worksheet):
        """
        美化Excel工作表
        
        Args:
            worksheet: openpyxl工作表对象
        """
        # 定义样式
        header_fill = PatternFill(start_color='1F4E78', end_color='1F4E78', fill_type='solid')
        header_font = Font(name='微软雅黑', size=11, bold=True, color='FFFFFF')
        data_font = Font(name='微软雅黑', size=10)
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 设置列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
        
        # 设置表头样式
        for cell in worksheet[1]:
            cell.fill = header_fill
            cell.font = header_font
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = border
        
        # 设置数据行样式
        for row in worksheet.iter_rows(min_row=2):
            for cell in row:
                cell.font = data_font
                cell.border = border
                
                # 如果是字段分类列，根据类别设置不同的背景色
                if cell.column_letter == get_column_letter_by_name(worksheet, '字段分类'):
                    category_color = get_category_color(cell.value)
                    if category_color:
                        cell.fill = PatternFill(start_color=category_color, end_color=category_color, fill_type='solid')
        
        # 冻结首行
        worksheet.freeze_panes = 'A2'

def get_column_letter_by_name(worksheet, column_name):
    """根据列名获取列字母"""
    for i, cell in enumerate(worksheet[1], 1):
        if cell.value == column_name:
            return cell.column_letter
    return None

def get_category_color(category):
    """根据类别返回颜色代码"""
    color_map = {
        '标识类': 'FFD6EAF8',  # 浅蓝
        '名称类': 'FFFDEBD0',  # 浅橙
        '描述类': 'FFD4EFDF',  # 浅绿
        '状态类': 'FFEBDEF0',  # 浅紫
        '时间类': 'FFFCF3CF',  # 浅黄
        '数值类': 'FFEADBD8',  # 浅红
        '类型类': 'FFD1F2EB',  # 薄荷
        '关系类': 'FFF9E7E7',  # 粉红
        '地址类': 'FFDAF7F4',  # 青绿
        '联系类': 'FFFDEADA',  # 杏色
        '内容类': 'FFE8DAEF',  # 薰衣草
        '配置类': 'FFDDEEDD',  # 灰绿
        '排序类': 'FFDBE5F1',  # 钢蓝
        '权限类': 'FFEAEAEA',  # 银灰
        '路径类': 'FFDFF7C3',  # 嫩绿
        '错误类': 'FFFCBABA',  # 浅红
        '未分类': 'FFF2F2F2'   # 浅灰
    }
    return color_map.get(category, None)

def main():
    """主函数"""
    # 设置matplotlib中文显示
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 输入文件
    input_file = "crd_datasource_params_export.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        logger.error(f"文件不存在: {input_file}")
        return
    
    # 创建分析器
    analyzer = FieldCategoryAnalyzer(input_file)
    
    # 加载数据
    if not analyzer.load_data():
        return
    
    # 对字段进行分类
    if not analyzer.categorize_fields():
        return
    
    # 可视化分类结果
    analyzer.visualize_categories()
    
    # 查找相似字段
    analyzer.get_similar_fields(threshold=0.75)
    
    # 导出结果
    analyzer.export_results(output_file='field_categories_result.xlsx')
    
    logger.info("字段分类分析完成！")

if __name__ == "__main__":
    main() 