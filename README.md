# MySQL数据导出到Excel工具

这个工具集合可以帮助您从MySQL数据库中查询数据并导出为Excel文件。

## 功能特性

- ✅ 连接MySQL数据库
- ✅ 执行自定义SQL查询
- ✅ 导出数据到Excel文件
- ✅ 自动调整Excel列宽
- ✅ 支持配置文件和命令行参数
- ✅ 数据预览功能
- ✅ 详细的错误处理和日志输出

## 文件说明

| 文件名 | 说明 |
|--------|------|
| `get_file_form_db.py` | 基础版本，包含您指定的SQL查询 |
| `enhanced_db_to_excel.py` | 增强版本，支持更多配置选项 |
| `config_template.py` | 配置文件模板 |
| `requirements.txt` | Python依赖包列表 |

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install pymysql pandas openpyxl
```

## 使用方法

### 方法1: 使用基础版本

1. 编辑 `get_file_form_db.py` 文件中的数据库配置：

```python
DB_CONFIG = {
    'host': 'your_host',           # 数据库地址
    'port': 3306,                  # 端口
    'user': 'your_username',       # 用户名
    'password': 'your_password',   # 密码
    'database': 'your_database'    # 数据库名
}
```

2. 运行脚本：

```bash
python get_file_form_db.py
```

### 方法2: 使用增强版本

#### 选项A: 使用配置文件

1. 复制配置模板：

```bash
cp config_template.py config.py
```

2. 编辑 `config.py` 文件，填入真实的数据库信息：

```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'your_username',
    'password': 'your_password',
    'database': 'your_database'
}
```

3. 运行脚本：

```bash
python enhanced_db_to_excel.py
```

#### 选项B: 使用命令行参数

```bash
python enhanced_db_to_excel.py \
    --host localhost \
    --user your_username \
    --password your_password \
    --database your_database
```

## 高级用法

### 自定义SQL查询

```bash
python enhanced_db_to_excel.py \
    --host localhost \
    --user your_username \
    --password your_password \
    --database your_database \
    --sql "SELECT * FROM your_table WHERE condition"
```

### 指定输出文件

```bash
python enhanced_db_to_excel.py \
    --output "我的数据导出.xlsx" \
    --sheet "数据表"
```

### 测试数据库连接

```bash
python enhanced_db_to_excel.py \
    --host localhost \
    --user your_username \
    --password your_password \
    --database your_database \
    --test
```

### 详细输出模式

```bash
python enhanced_db_to_excel.py --verbose
```

## 命令行参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--host` | 数据库主机地址 | localhost |
| `--port` | 数据库端口 | 3306 |
| `--user` | 数据库用户名 | 无 |
| `--password` | 数据库密码 | 无 |
| `--database` | 数据库名称 | 无 |
| `--output` | 输出文件名 | 自动生成 |
| `--sheet` | Excel工作表名称 | 数据 |
| `--sql` | 自定义SQL查询 | 使用默认查询 |
| `--test` | 仅测试数据库连接 | false |
| `--verbose` | 详细输出模式 | false |

## 默认SQL查询

工具默认执行以下SQL查询：

```sql
SELECT RESOURCE_CODE, ENAME, CNAME 
FROM crd_datasource_params 
WHERE FIELD_TYPE='2' 
  AND VERSION='V1' 
  AND RESOURCE_CODE NOT IN ('yinkesit10001', '976652b23d41493c89396367fbfba2a9', 'test001') 
  AND CNAME NOT IN ('返回码', '返回代码', '错误码', '状态') 
ORDER BY RESOURCE_CODE
```

## 输出示例

运行成功后，您会看到类似以下的输出：

```
============================================================
          MySQL数据导出到Excel工具 (增强版)
============================================================
✓ 成功连接到数据库: your_database (localhost:3306)

正在执行查询...
📝 执行SQL查询: SELECT RESOURCE_CODE, ENAME, CNAME FROM crd_datasource_params WHERE FIELD_TYPE='2'...
✓ 查询成功，共获取 150 条数据
✓ 数据字段: RESOURCE_CODE, ENAME, CNAME

正在生成Excel文件...
✓ Excel文件已生成: /path/to/crd_datasource_params_export.xlsx
✓ 数据行数: 150
✓ 数据列数: 3
✓ 文件大小: 25.3 KB

🎉 任务完成！
📁 Excel文件已保存到: /path/to/crd_datasource_params_export.xlsx

📊 数据预览 (前3条):
  RESOURCE_CODE    ENAME       CNAME
0      code001    field1      字段1
1      code002    field2      字段2
2      code003    field3      字段3

✓ 数据库连接已关闭
```

## 错误处理

工具包含完善的错误处理机制：

- 数据库连接失败会显示详细错误信息
- SQL查询错误会显示具体的错误原因
- Excel文件生成失败会提供解决建议
- 支持用户中断操作 (Ctrl+C)

## 安全建议

1. 不要在代码中硬编码数据库密码
2. 使用配置文件时，确保 `config.py` 不被版本控制系统跟踪
3. 考虑使用环境变量存储敏感信息
4. 定期更新依赖包以获取安全补丁

## 故障排除

### 常见问题

1. **连接被拒绝**：检查数据库地址、端口和防火墙设置
2. **认证失败**：验证用户名和密码是否正确
3. **表不存在**：确认数据库中存在 `crd_datasource_params` 表
4. **编码问题**：工具默认使用 UTF-8 编码，确保数据库支持中文

### 依赖问题

如果遇到包安装问题，可以尝试：

```bash
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall
```

## 许可证

此工具仅供学习和内部使用。 