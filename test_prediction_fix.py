#!/usr/bin/env python3
"""
测试预测函数修复是否有效
"""

import pandas as pd
import numpy as np
import pickle
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

# 创建一些测试数据
np.random.seed(42)
n_samples = 1000
n_features = 10

# 生成特征
X = np.random.randn(n_samples, n_features)
# 生成目标变量（不平衡数据）
y = np.random.binomial(1, 0.2, n_samples)  # 20%的正样本

# 分割数据
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 训练一个随机森林模型
model = RandomForestClassifier(n_estimators=100, random_state=42)
model.fit(X_train, y_train)

print("模型训练完成")
print(f"模型类型: {type(model)}")
print(f"是否有predict_proba方法: {hasattr(model, 'predict_proba')}")
print(f"是否有predict方法: {hasattr(model, 'predict')}")

# 测试预测
test_sample = X_test[:5]  # 取前5个样本

# 使用predict_proba获取概率
proba_predictions = model.predict_proba(test_sample)[:, 1]
print(f"\n使用predict_proba的结果: {proba_predictions}")
print(f"概率范围: [{proba_predictions.min():.4f}, {proba_predictions.max():.4f}]")

# 使用predict获取类别预测
class_predictions = model.predict(test_sample)
print(f"\n使用predict的结果: {class_predictions}")
print(f"类别预测范围: [{class_predictions.min()}, {class_predictions.max()}]")

# 验证修复后的逻辑
def test_prediction_logic(model, X_new):
    """测试修复后的预测逻辑"""
    if hasattr(model, 'predict_proba'):
        # sklearn模型（随机森林、逻辑回归等）
        prob_predictions = model.predict_proba(X_new)[:, 1]
        print("使用sklearn逻辑（predict_proba）")
    else:
        # LightGBM
        prob_predictions = model.predict(X_new)
        print("使用LightGBM逻辑（predict）")
    
    return prob_predictions

print("\n=== 测试修复后的预测逻辑 ===")
fixed_predictions = test_prediction_logic(model, test_sample)
print(f"修复后的预测结果: {fixed_predictions}")
print(f"修复后的概率范围: [{fixed_predictions.min():.4f}, {fixed_predictions.max():.4f}]")

# 验证结果是否正确
if np.allclose(fixed_predictions, proba_predictions):
    print("\n✅ 修复成功！预测逻辑正确")
else:
    print("\n❌ 修复失败！预测逻辑仍有问题")

print(f"\n实际标签: {y_test[:5]}")
print(f"预测概率: {fixed_predictions}")
print(f"预测类别 (阈值=0.5): {(fixed_predictions >= 0.5).astype(int)}")
