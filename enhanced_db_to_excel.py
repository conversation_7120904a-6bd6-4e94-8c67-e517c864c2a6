#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版MySQL数据导出到Excel工具
支持配置文件和命令行参数
"""

import pymysql
import pandas as pd
from datetime import datetime
import os
import sys
import argparse

# 尝试导入配置文件
try:
    from config import DATABASE_CONFIG, EXCEL_CONFIG
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
    print("⚠️ 未找到config.py文件，将使用命令行参数或默认配置")

class DatabaseToExcel:
    def __init__(self, host, port, user, password, database):
        """
        初始化数据库连接参数
        
        Args:
            host (str): 数据库主机地址
            port (int): 数据库端口
            user (str): 用户名
            password (str): 密码
            database (str): 数据库名
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.connection = None
    
    def connect(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            print(f"✓ 成功连接到数据库: {self.database} ({self.host}:{self.port})")
            return True
        except Exception as e:
            print(f"✗ 数据库连接失败: {str(e)}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            print("✓ 数据库连接已关闭")
    
    def test_connection(self):
        """测试数据库连接"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                return result is not None
        except Exception:
            return False
    
    def execute_query(self, sql):
        """
        执行SQL查询
        
        Args:
            sql (str): SQL查询语句
            
        Returns:
            list: 查询结果列表
        """
        try:
            with self.connection.cursor() as cursor:
                print(f"📝 执行SQL查询: {sql[:100]}{'...' if len(sql) > 100 else ''}")
                cursor.execute(sql)
                result = cursor.fetchall()
                print(f"✓ 查询成功，共获取 {len(result)} 条数据")
                
                # 显示字段信息
                if result:
                    columns = list(result[0].keys())
                    print(f"✓ 数据字段: {', '.join(columns)}")
                
                return result
        except Exception as e:
            print(f"✗ 查询失败: {str(e)}")
            return None
    
    def export_to_excel(self, data, filename=None, sheet_name='数据'):
        """
        将数据导出到Excel文件
        
        Args:
            data (list): 查询结果数据
            filename (str): 输出文件名，如果为None则自动生成
            sheet_name (str): Excel工作表名称
            
        Returns:
            str: 生成的文件路径
        """
        if not data:
            print("✗ 没有数据可以导出")
            return None
        
        # 生成文件名
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"数据导出_{timestamp}.xlsx"
        
        try:
            # 转换为DataFrame
            df = pd.DataFrame(data)
            
            # 确保输出目录存在
            output_dir = os.path.dirname(filename) if os.path.dirname(filename) else '.'
            os.makedirs(output_dir, exist_ok=True)
            
            # 导出到Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # 自动调整列宽
                worksheet = writer.sheets[sheet_name]
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # 获取绝对路径
            abs_path = os.path.abspath(filename)
            file_size = os.path.getsize(abs_path)
            
            print(f"✓ Excel文件已生成: {abs_path}")
            print(f"✓ 数据行数: {len(df)}")
            print(f"✓ 数据列数: {len(df.columns)}")
            print(f"✓ 文件大小: {file_size / 1024:.1f} KB")
            
            return abs_path
        except Exception as e:
            print(f"✗ Excel文件生成失败: {str(e)}")
            return None

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='MySQL数据导出到Excel工具')
    
    # 数据库连接参数
    parser.add_argument('--host', default='localhost', help='数据库主机地址')
    parser.add_argument('--port', type=int, default=3306, help='数据库端口')
    parser.add_argument('--user', help='数据库用户名')
    parser.add_argument('--password', help='数据库密码')
    parser.add_argument('--database', help='数据库名称')
    
    # 导出参数
    parser.add_argument('--output', help='输出文件名')
    parser.add_argument('--sheet', default='数据', help='Excel工作表名称')
    parser.add_argument('--sql', help='自定义SQL查询语句')
    
    # 其他选项
    parser.add_argument('--test', action='store_true', help='仅测试数据库连接')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    return parser.parse_args()

def get_database_config(args):
    """获取数据库配置"""
    if CONFIG_AVAILABLE:
        config = DATABASE_CONFIG.copy()
        # 命令行参数覆盖配置文件
        if args.host != 'localhost':
            config['host'] = args.host
        if args.port != 3306:
            config['port'] = args.port
        if args.user:
            config['user'] = args.user
        if args.password:
            config['password'] = args.password
        if args.database:
            config['database'] = args.database
    else:
        # 仅使用命令行参数
        config = {
            'host': args.host,
            'port': args.port,
            'user': args.user or input("请输入数据库用户名: "),
            'password': args.password or input("请输入数据库密码: "),
            'database': args.database or input("请输入数据库名称: ")
        }
    
    return config

def main():
    """主函数"""
    args = parse_arguments()
    
    print("=" * 60)
    print("          MySQL数据导出到Excel工具 (增强版)")
    print("=" * 60)
    
    # 获取数据库配置
    try:
        db_config = get_database_config(args)
    except KeyboardInterrupt:
        print("\n⚠️ 用户取消操作")
        return
    
    # 验证必要参数
    required_fields = ['host', 'port', 'user', 'password', 'database']
    for field in required_fields:
        if not db_config.get(field):
            print(f"✗ 缺少必要参数: {field}")
            return
    
    # 创建数据库连接实例
    db_excel = DatabaseToExcel(**db_config)
    
    try:
        # 连接数据库
        if not db_excel.connect():
            return
        
        # 如果只是测试连接
        if args.test:
            if db_excel.test_connection():
                print("✓ 数据库连接测试成功！")
            else:
                print("✗ 数据库连接测试失败！")
            return
        
        # 准备SQL查询
        if args.sql:
            sql_query = args.sql
        else:
            # 使用默认查询
            sql_query = """
            SELECT RESOURCE_CODE, ENAME, CNAME 
            FROM crd_datasource_params 
            WHERE FIELD_TYPE='2' 
              AND VERSION='V1' 
              AND RESOURCE_CODE NOT IN ('yinkesit10001', '976652b23d41493c89396367fbfba2a9', 'test001') 
              AND CNAME NOT IN ('返回码', '返回代码', '错误码', '状态') 
            ORDER BY RESOURCE_CODE
            """
        
        # 执行查询
        print("\n正在执行查询...")
        data = db_excel.execute_query(sql_query)
        
        if data is None:
            return
        
        # 准备输出文件名
        if args.output:
            output_filename = args.output
        elif CONFIG_AVAILABLE:
            output_filename = EXCEL_CONFIG.get('filename', 'crd_datasource_params_export.xlsx')
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"数据导出_{timestamp}.xlsx"
        
        # 准备工作表名称
        sheet_name = args.sheet
        if CONFIG_AVAILABLE and not args.sheet:
            sheet_name = EXCEL_CONFIG.get('sheet_name', '数据')
        
        # 导出到Excel
        print("\n正在生成Excel文件...")
        excel_file = db_excel.export_to_excel(
            data, 
            filename=output_filename,
            sheet_name=sheet_name
        )
        
        if excel_file:
            print(f"\n🎉 任务完成！")
            print(f"📁 Excel文件已保存到: {excel_file}")
            
            # 显示数据预览
            if data and len(data) > 0:
                print(f"\n📊 数据预览 (前3条):")
                df_preview = pd.DataFrame(data[:3])
                print(df_preview.to_string(index=False))
        else:
            print("\n❌ Excel文件生成失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
    finally:
        # 关闭数据库连接
        db_excel.disconnect()

if __name__ == "__main__":
    main() 