{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# LightGBM特征选择和数据降维\n", "\n", "本notebook实现了基于LightGBM的高级特征选择功能，包括：\n", "- 数据预处理和缺失值处理\n", "- 数据不平衡处理\n", "- 多种特征选择方法\n", "- 特征重要性分析\n", "- 性能评估和可视化\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ 库导入完成\n"]}], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import lightgbm as lgb\n", "from sklearn.model_selection import StratifiedKFold, cross_val_score\n", "from sklearn.feature_selection import RFE, SelectKBest, f_classif\n", "from sklearn.preprocessing import LabelEncoder\n", "from sklearn.metrics import roc_auc_score, classification_report\n", "from imblearn.over_sampling import SMOTE\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"✓ 库导入完成\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 1. 数据加载和预处理\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始加载数据...\n", "✓ 数据加载成功 - 形状: (2374, 1227)\n", "\n", "数据概况:\n", "- 总行数: 2,374\n", "- 总列数: 1,227\n", "- 好坏标签分布:\n", "好坏标签\n", "好    2201\n", "坏     173\n", "Name: count, dtype: int64\n", "\n", "-999缺失值统计: 2,322,623个\n"]}], "source": ["# 加载标记数据\n", "input_file = \"result_20250617_111816_labeled_20250617_112114.xlsx\"\n", "\n", "print(\"开始加载数据...\")\n", "df = pd.read_excel(input_file)\n", "print(f\"✓ 数据加载成功 - 形状: {df.shape}\")\n", "\n", "# 检查数据基本信息\n", "print(f\"\\n数据概况:\")\n", "print(f\"- 总行数: {df.shape[0]:,}\")\n", "print(f\"- 总列数: {df.shape[1]:,}\")\n", "print(f\"- 好坏标签分布:\")\n", "print(df['好坏标签'].value_counts())\n", "\n", "# 检查-999缺失值\n", "missing_999_count = (df == -999).sum().sum()\n", "print(f\"\\n-999缺失值统计: {missing_999_count:,}个\")\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始数据预处理...\n", "✓ 已排除APP_IDCARD字段\n", "✓ 已排除姓名字段\n", "✓ 已排除手机号字段\n", "处理-999缺失值...\n", "✓ 有缺失值的列数: 1178\n", "✓ 总缺失值数量: 2,322,623\n", "✓ 删除了892个高缺失率列\n", "✓ 标签编码: {'坏': 0, '好': 1}\n", "✓ 预处理完成 - 最终特征数: 331, 样本数: 2374\n", "\n", "预处理后数据形状: (2374, 331)\n", "标签分布: [ 173 2201]\n"]}], "source": ["# 数据预处理函数\n", "def preprocess_data(df):\n", "    \"\"\"\n", "    数据预处理函数\n", "    - 处理-999缺失值\n", "    - 排除身份证号字段\n", "    - 数据类型优化\n", "    \"\"\"\n", "    print(\"开始数据预处理...\")\n", "    \n", "    # 创建数据副本\n", "    df_processed = df.copy()\n", "    \n", "    # 排除身份证号字段\n", "    if 'APP_IDCARD' in df_processed.columns:\n", "        df_processed = df_processed.drop('APP_IDCARD', axis=1)\n", "        print(\"✓ 已排除APP_IDCARD字段\")\n", "    # 排除姓名和手机号\n", "    if '姓名' in df_processed.columns:\n", "        df_processed = df_processed.drop('姓名', axis=1)\n", "        print(\"✓ 已排除姓名字段\")\n", "    if '手机号' in df_processed.columns:\n", "        df_processed = df_processed.drop('手机号', axis=1)\n", "        print(\"✓ 已排除手机号字段\")\n", "    # 分离特征和标签\n", "    if '好坏标签' in df_processed.columns:\n", "        y = df_processed['好坏标签'].copy()\n", "        X = df_processed.drop('好坏标签', axis=1)\n", "    else:\n", "        raise ValueError(\"未找到'好坏标签'列\")\n", "    \n", "    # 处理-999缺失值\n", "    print(\"处理-999缺失值...\")\n", "    X_clean = X.replace(-999, np.nan)\n", "    \n", "    # 统计缺失值情况\n", "    missing_stats = X_clean.isnull().sum()\n", "    missing_cols = missing_stats[missing_stats > 0]\n", "    print(f\"✓ 有缺失值的列数: {len(missing_cols)}\")\n", "    print(f\"✓ 总缺失值数量: {missing_stats.sum():,}\")\n", "    \n", "    # 缺失值处理策略\n", "    # 1. 删除缺失值过多的列（缺失率>80%）\n", "    missing_ratio = missing_stats / len(X_clean)\n", "    high_missing_cols = missing_ratio[missing_ratio > 0.8].index\n", "    if len(high_missing_cols) > 0:\n", "        X_clean = X_clean.drop(high_missing_cols, axis=1)\n", "        print(f\"✓ 删除了{len(high_missing_cols)}个高缺失率列\")\n", "    \n", "    # 2. 对数值型特征用中位数填充\n", "    numeric_cols = X_clean.select_dtypes(include=[np.number]).columns\n", "    for col in numeric_cols:\n", "        if X_clean[col].isnull().any():\n", "            median_val = X_clean[col].median()\n", "            X_clean[col].fillna(median_val, inplace=True)\n", "    \n", "    # 3. 对分类特征用众数填充\n", "    categorical_cols = X_clean.select_dtypes(include=['object']).columns\n", "    for col in categorical_cols:\n", "        if X_clean[col].isnull().any():\n", "            mode_val = X_clean[col].mode()[0] if not X_clean[col].mode().empty else 'unknown'\n", "            X_clean[col].fillna(mode_val, inplace=True)\n", "    \n", "    # 处理分类变量编码\n", "    label_encoders = {}\n", "    for col in categorical_cols:\n", "        if col in X_clean.columns:\n", "            le = LabelEncoder()\n", "            X_clean[col] = le.fit_transform(X_clean[col].astype(str))\n", "            label_encoders[col] = le\n", "    \n", "    # 处理标签编码\n", "    if y.dtype == 'object':\n", "        label_encoder_y = LabelEncoder()\n", "        y_encoded = label_encoder_y.fit_transform(y)\n", "        print(f\"✓ 标签编码: {dict(zip(label_encoder_y.classes_, label_encoder_y.transform(label_encoder_y.classes_)))}\")\n", "    else:\n", "        y_encoded = y.values\n", "        label_encoder_y = None\n", "    \n", "    print(f\"✓ 预处理完成 - 最终特征数: {X_clean.shape[1]}, 样本数: {X_clean.shape[0]}\")\n", "    \n", "    return X_clean, y_encoded, label_encoders, label_encoder_y\n", "\n", "# 执行数据预处理\n", "X_processed, y_processed, label_encoders, label_encoder_y = preprocess_data(df)\n", "\n", "print(f\"\\n预处理后数据形状: {X_processed.shape}\")\n", "print(f\"标签分布: {np.bincount(y_processed)}\")"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 2. 数据不平衡处理\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理数据不平衡...\n", "原始数据分布: [ 173 2201]\n", "使用SMOTE进行过采样...\n", "✓ SMOTE完成 - 新数据分布: [2201 2201]\n", "\n", "平衡后数据形状: (4402, 331)\n", "平衡后标签分布: [2201 2201]\n"]}], "source": ["# 数据不平衡处理\n", "def handle_imbalanced_data(X, y, method='smote', random_state=42):\n", "    \"\"\"\n", "    处理数据不平衡问题\n", "    \n", "    Args:\n", "        X: 特征数据\n", "        y: 标签数据\n", "        method: 处理方法 ('smote', 'none')\n", "        random_state: 随机种子\n", "    \n", "    Returns:\n", "        X_resampled, y_resampled: 重采样后的数据\n", "    \"\"\"\n", "    print(f\"原始数据分布: {np.bincount(y)}\")\n", "    \n", "    if method == 'smote':\n", "        print(\"使用SMOTE进行过采样...\")\n", "        # 计算合适的k_neighbors参数\n", "        min_class_count = np.min(np.bincount(y))\n", "        k_neighbors = min(5, min_class_count - 1) if min_class_count > 1 else 1\n", "        \n", "        smote = SMOTE(random_state=random_state, k_neighbors=k_neighbors)\n", "        X_resampled, y_resampled = smote.fit_resample(X, y)\n", "        print(f\"✓ SMOTE完成 - 新数据分布: {np.bincount(y_resampled)}\")\n", "    else:\n", "        print(\"不进行重采样处理\")\n", "        X_resampled, y_resampled = X, y\n", "    \n", "    return X_resampled, y_resampled\n", "\n", "# 应用SMOTE处理数据不平衡\n", "print(\"处理数据不平衡...\")\n", "X_balanced, y_balanced = handle_imbalanced_data(X_processed, y_processed, method='smote')\n", "\n", "print(f\"\\n平衡后数据形状: {X_balanced.shape}\")\n", "print(f\"平衡后标签分布: {np.bincount(y_balanced)}\")"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 3. LightGBM特征重要性分析\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练LightGBM模型进行特征重要性分析...\n", "Training until validation scores don't improve for 50 rounds\n", "Early stopping, best iteration is:\n", "[20]\ttrain's auc: 0.999998\teval's auc: 0.999892\n", "✓ 模型训练完成\n", "  - 训练集AUC: 1.0000\n", "  - 测试集AUC: 0.9999\n", "  - 最佳迭代次数: 20\n", "\n", "特征重要性分析完成\n", "前10个最重要的特征:\n", "                         feature    importance\n", "256                  银行卡涉赌涉诈风险类型  24639.803833\n", "49       按身份证号查询距最近在非银行机构申请的间隔天数   3000.409356\n", "114       按手机号查询距最近在非银行机构申请的间隔天数    169.339610\n", "277                       近期稳定指数    139.344997\n", "3                     浙数社保经济能力评分      8.158680\n", "213   按手机号查询预测近24个月在风险偏好c的申请次数占比      4.526958\n", "267                   经济能力评估分数V2      3.329302\n", "230  按身份证号查询预测近24个月在风险偏好c的申请次数占比      2.105308\n", "314                 近1个月资金不足交易天数      1.997210\n", "116          按手机号查询近6个月在银行机构申请次数      1.885644\n"]}], "source": ["# LightGBM特征重要性分析\n", "from sklearn.model_selection import train_test_split\n", "\n", "def train_lightgbm_for_feature_importance(X, y, test_size=0.2, random_state=42):\n", "    \"\"\"\n", "    训练LightGBM模型并获取特征重要性\n", "    \n", "    Args:\n", "        X: 特征数据\n", "        y: 标签数据\n", "        test_size: 测试集比例\n", "        random_state: 随机种子\n", "    \n", "    Returns:\n", "        model: 训练好的模型\n", "        feature_importance_df: 特征重要性DataFrame\n", "        train_score: 训练集AUC\n", "        test_score: 测试集AUC\n", "    \"\"\"\n", "    print(\"训练LightGBM模型进行特征重要性分析...\")\n", "    \n", "    # 分割数据\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        X, y, test_size=test_size, random_state=random_state, stratify=y\n", "    )\n", "    \n", "    # LightGBM参数设置\n", "    lgb_params = {\n", "        'objective': 'binary',\n", "        'metric': 'auc',\n", "        'boosting_type': 'gbdt',\n", "        'num_leaves': 31,\n", "        'learning_rate': 0.05,\n", "        'feature_fraction': 0.9,\n", "        'bagging_fraction': 0.8,\n", "        'bagging_freq': 5,\n", "        'verbose': -1,\n", "        'random_state': random_state\n", "    }\n", "    \n", "    # 创建数据集\n", "    train_data = lgb.Dataset(X_train, label=y_train)\n", "    valid_data = lgb.Dataset(X_test, label=y_test, reference=train_data)\n", "    \n", "    # 训练模型\n", "    model = lgb.train(\n", "        lgb_params,\n", "        train_data,\n", "        valid_sets=[train_data, valid_data],\n", "        valid_names=['train', 'eval'],\n", "        num_boost_round=1000,\n", "        callbacks=[lgb.early_stopping(stopping_rounds=50), lgb.log_evaluation(0)]\n", "    )\n", "    \n", "    # 预测和评估\n", "    train_pred = model.predict(X_train, num_iteration=model.best_iteration)\n", "    test_pred = model.predict(X_test, num_iteration=model.best_iteration)\n", "    \n", "    train_score = roc_auc_score(y_train, train_pred)\n", "    test_score = roc_auc_score(y_test, test_pred)\n", "    \n", "    print(f\"✓ 模型训练完成\")\n", "    print(f\"  - 训练集AUC: {train_score:.4f}\")\n", "    print(f\"  - 测试集AUC: {test_score:.4f}\")\n", "    print(f\"  - 最佳迭代次数: {model.best_iteration}\")\n", "    \n", "    # 获取特征重要性\n", "    feature_importance = model.feature_importance(importance_type='gain')\n", "    feature_names = X.columns if hasattr(X, 'columns') else [f'feature_{i}' for i in range(X.shape[1])]\n", "    \n", "    feature_importance_df = pd.DataFrame({\n", "        'feature': feature_names,\n", "        'importance': feature_importance\n", "    }).sort_values('importance', ascending=False)\n", "    \n", "    return model, feature_importance_df, train_score, test_score\n", "\n", "# 训练模型并获取特征重要性\n", "lgb_model, feature_importance_df, train_auc, test_auc = train_lightgbm_for_feature_importance(\n", "    X_balanced, y_balanced\n", ")\n", "\n", "print(f\"\\n特征重要性分析完成\")\n", "print(f\"前10个最重要的特征:\")\n", "print(feature_importance_df.head(10))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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*****************************+WLdunRhovnHjBipXrixmHmYXHR0NMzMzjb8Hjx49wpUrV9CqVStYWlqqZKbJ5XJMnz4dbm5uMDIyQkBAgJgteOHCBaxduxYDBgz4qkL3jRs3BgAcOXJE46qBRFRwGJQiIiIi+klt2LABmZmZGD9+vFIwxNTUFFu2bEHNmjXFVdQ+5+rqKgakAKBYsWIYPHgwfv/9dzF7IyeKP6Y1TUM7cuQIjhw5ovbcjBkzxClcCkOHDsXQoUPVtm/btq3GldIUevXqhXnz5uHAgQNKQalXr17h6tWrmDFjhsa+mzZtwqpVq2BgYKB0fM2aNRg8eDAmT56sEthROHXqFE6fPo3+/furZIHFxcVhzJgxSqvfValSJcfnAICzZ88qBX7at2+fayBp7dq1WLt2rcrxffv2Yd++fSrHs0/DVKdZs2Yq08mArJXirl+/Lq62p46JiYnKseTkZNjZ2WHUqFFYtWqVeDwzMxNt27aFs7MzjI2NcxzTlxAEAUFBQdDW1oaDg0Ou7S9evIiwsDBxXzG97nvZuHEjtm7dCj09Pfz+++/i8aSkJABA4cKF1fZTfJOJiYkAsqb0paSkoGvXrvj99981TkvUpGrVqrh8+TL+/PNPuLu7w9LSUjx3/PhxJCUlqaycOG3aNAQGBmLz5s0afw+2bNmCK1euYMKECSorR7579w5DhgyBv78/AMDY2DjHaZ4KO3bsUFrVb+zYsSoZWABQsWJFmJmZ4c6dO4iPj/+u3xERfTsGpYiIiIh+UmfPngWQlQHxuRo1aqB69eoap4YpsguyU/yBGhsbm+u9FdO5MjMz1Z6/e/cudu7cqfbckCFDVIJSjo6OsLW1Vbr+hw8fcO3aNZw6dQqVK1fG2bNnNdb5sbOzQ40aNXDhwgVERUXBzMwMAMSATO/evXN8HsUf/7Gxsbh8+TKArECFjo6OWFfrS/Xt2xe+vr7o27cvYmJixPpBuSldurRS9lFesqjq1auHVq1aifsymQzLly9HlSpVxNpPCtkzqjQZMGAABgwYoHQsMTERTZo0QWRkJCZOnIiaNWvmeh0FxdTBz9nZ2WHXrl3YsmWL0vRJxVSuXbt2KWUs9e/fH82bN8/1fuHh4UhISIClpSX09PRyba8uS+7zIOXX2rhxI8aPHw+JRIJt27bBzs5OPKcIGsvlcrV9P89Kq1ixIu7fv/9N45FIJBg1apRKRiHw/88cEREhTv/19/eHgYEBypQp88X3UgQdHzx4gMmTJ+PcuXP4+PFjnvra2dkp/Xcqp4y38uXLIyoqCo8fP1abrUhEBYdBKSIiIqKf1OvXrwFAY9DE2tpaY1CqaNGiKscUmTGaCopnZ25uDiDrj1d1FixYgAULFigda9asGS5cuKC2vYuLi1gHKbvExET06dMHJ06cwKBBgxAUFKRxTL169cK9e/dw+PBhuLq6AsiaumdnZ5drtszjx4+xfv16/P3332L2Sv/+/bF8+XJ06NBB7diyGz9+PMaPHw/g/wMNO3fuREBAAHr27ClOkcqLypUra8xI0UQulysFCBU/Q0EQNAYO1fn06RP++OMPtecCAwNx9+5d2Nra4tChQzh06FCO1+rZs2eepyWeOXNG7fWCgoKUfuYODg55Ckop6kFlr1+Vk4MHDyoFPF1cXPDkyZM89dVEEATMmDEDK1euhEQiwcaNG1UCfYogkOKb+1xycjIAwNDQ8JvGklfXr1/HunXrcODAAaSnpwMAJk+ejNmzZ8PGxgZxcXE59lcsCmBlZYWwsDBoa2vD29sboaGhaN26NerUqZPnsTRu3DjPvweKGl4sdk7078OgFBEREdFPShH8UBQc/pyurq7Gvt9aUFqxutidO3eQnJyscfrRtzI0NMRff/2FEiVK4PLlywgLC9M49axXr16YPXs29u/fD1dXVzx79gx37tzBvHnzcrxHZGQkmjdvjg8fPsDGxgYVKlTAqVOn0KNHD5QpUwbdunVDnTp1kJGRofKuw8PD4efnh4YNG6pMzTMzM0PPnj2/6fnz6ubNm2prID1+/BiPHz/O83U+ffqEJUuW5NgmJCQk1zZA1qpreQ1KHTx4UGn/+PHj6NixIzZv3qw2oyc3KSkpAPIezLGzs1OqKfWtQaDk5GQMGDAA3t7eKFSoEDw8PNCvXz+VdorsI03B3Xfv3im1+5EeP36MFi1aICkpCdWrV4eJiQkCAwMxaNAgFCtWDIMGDUJycjIyMzNVpnY+efIEQUFBaN26NcqWLatU6N3GxuarVjL8Eoopo4ogHhH9e3D1PSIiIqKflOIP1VevXqk9/7WrWeVFpUqVYG9vj6SkJBw4cOCH3QfIyspS/JGrbnU9BVtbW9SqVUucwrd//34A0LjqXvbrnz17FmfPnsXz589VpvrNmzcPpUqVgo+PD6ytrbFp0yZs374d27dvx6RJkwAA/fr1E4/lp3LlykEQBJVNEZTp3bu32vOaiuDb2toqtZPJZGLh62HDhqlc5/Xr17CxsYGenh5u376tdO7zrKD8pCi+n1tmj0KbNm1Qrlw5cctpkYDcxMbGomXLlvD29kaxYsXg7++vNiAFQAyEaZqSpzj+pbWjvkblypXh7++Pa9eu4d69eyqrXbq7uyMjIwP+/v6oU6cOtm3bJn7zip/1+PHjsX37dixfvvyHjzc7Rc2tvEzVJKL8xUwpIiIiop9UixYtEBoaimPHjmHq1KlK5yIiInDjxo1vvsfnRY+zW7p0KTp27Ihff/0VTZs2zbH2UlRUlFgc/UtFR0fj48ePkEgkuRbo7tWrF27fvg1vb2/s378f9vb2qFy5cq73qFq1ao5/+Lu4uODFixeYM2cOTpw4gWPHjillgxSU9evXq52iqZi+d+PGDaVaTdn98ccfuRaFnjx5Mvbv3w+pVIpChQph3bp1qFixIipUqIAHDx7A1dUVnz59wq5du76oxtSPpiikndf6Rba2tkorDSYkJCAtLe2L75uamgpnZ2dcuXIFNjY28PPzyzFLqHXr1gCyFgZYt26dUgZSTEwMAgICULhwYTg5OX3xWL5Gw4YNNZ7T0tLCtGnTEBERgdGjR+PUqVPYs2fPD1l980vFxMQA+P/VCono34NBKSIiIqKf1Pjx4+Hh4YHFixfDyckJdevWBQDEx8djwIABYjHyb6HIPIiNjYUgCErT/jp06IDZs2djyZIlqFOnDtasWYPevXsrTRvMyMjAnj17MHPmTLx79w46OjowNTXN8/3T0tIwZswYAFmr8ClqWWnSq1cvzJgxA+vXr0dwcHCeppnlhZWVFXbv3g0nJyeMHj0aK1euxMqVK7/Ltb+Fn58fTpw4ofH8ixcv8OLFC7XnFi9enGNQ6tq1a7h16xYsLS3x7t07bNmyRW27/v37w9nZ+csG/oOVKlUKRYsWRWRkJBISEpQCTups3LhRafpes2bNVAqs56Ue0rx583DlyhWUKVMGFy5cyHWVufLly8PZ2RknT57ErFmzsGLFCkgkEshkMowbNw4pKSmYNGnSvyYDqGrVqjh16hTmzZuHxYsXw8PDQ/z9LEihoaEAoFREnoj+HRiUIiIiIvoXCwoKyjHjZtSoUVi8eLHac/b29li1ahUmT56MRo0aoXnz5jA1NcX58+eRlpYGc3NzREZG5pjtlJvixYujWLFiiI6ORosWLWBpaam0qt7ixYtRqlQpTJs2DYMHD8bEiRNRq1YtlChRAjExMbhy5Yo4haphw4bYtGmT2qLj27dvR0BAgLgvl8sRHR2Na9euITo6GmXLlsXWrVtzHa+1tTXq1q0rZonlturelxo5ciRq1qz5RQWbf6Tjx4+rPZ6amgp9fX307t0be/fu/apr169fH5cuXQKQNQ3Ow8MDa9euRVhYGICsgMqLFy+we/dueHt7o0uXLnB1dUXTpk2/6D5Tp05VyWhSZNXt3LlT7VS6uXPn5lqnqHHjxjh27BiuXr2qtDLh19i1a5f4b00r5X38+BEbNmwAAJQoUQKzZ8/WeD03Nzfx937Tpk2oX78+Vq1ahZMnT6JatWq4ffs2nj9/jurVq2PhwoXfNPbvTSKR4Pfff0eHDh3+FSvdvX//Hu/evUPlypX/FdmLRKSMQSkiIiKif7HMzExER0drPK+olaLJpEmTUKFCBaxcuRJXrlyBRCJB06ZNsXz5cgwcOBCRkZEoVKjQV49PS0sLnp6e+O2333D58mXcu3cPcXFxYmFhABg7dix69eqFrVu3ws/PDw8ePEBgYCAMDQ1hZWWFpk2bonfv3krLu3/u81XWtLW1YWpqChsbG4wfPx6TJ0/OdaqZQu/evXHjxg3UqVPnhxRYlkgkGD16NPT19XHx4kUA6lczVCc1NVUla+fzYuhnz55VW4h+/vz54oqGmqbkKeRl+p6Cs7MzunXrBrlcjsePH+PTp0+IjIxEaGgoQkJCcPPmTdy/fx8ymQwGBgYYMGAAfvvtN1SvXh1PnjyBl5cX9uzZAy8vL3h5eaFFixZYtGgRHB0dc3sdALKKnGuqi3b16lW1QalRo0bl+rPt3Lkzjh07hkuXLuUpKHX9+nX89ddf0NHRwb1791R+ppMmTcLHjx9x/fp1AFD5Hq9fvy7W8rp16xZu3bql8V4LFiwQAyhWVla4desW5s2bB19fX3h7e6N06dL47bffMG/evHxbee9LxcTEYMyYMShUqBB8fX0B5P334MmTJyrTahWZngo7duzAjh07VPp6eHgorYap+D66du36JcMnonwiERTLshARERHR/5SyZcsiPDwcISEhP3z1q5+Jp6cnhg4dKmb/fC45ORlly5bFp0+fYGJighYtWmD37t051tbp0aMHDh06hJSUFIwbN+6rxtWpUyd06tQJwLevnpjd9OnTxcLUQ4YMUcqE09PTQ40aNVC3bl20atUKrVu31jiV7NSpU1i5ciXOnz8PExMT3L9/H5aWlirt5syZgyVLluDBgwdKU+a+t8TERFhaWsLc3BxPnjxR22b9+vX4888/ceTIEZiamsLCwgKZmZkoV64cVq9erfTz79OnD/bt2wd9fX00adIEe/bsyXMQJj8FBASgefPmWLVqlUqtuS+xYMECLFy4EHfu3FGb3RgeHg5ra2tkZmaiaNGi6NGjB7Zs2ZLjt1mnTh1ERETg5s2bmDNnzleNa9iwYWjUqJG4P2DAAOzbtw9Pnz5F+fLlv+qaRPTjMChFRERE9JOaN28ezp8/L9aUyi4iIgIWFhbQ1dVFfHy8yhLu9G0yMzOhpaX1TVMj/41SUlJw+PBhlClTBpaWlihbtuwXfzu3bt2CTCZDvXr1ftAo804RAAsMDMwxUy8vFNlnUqn0ewztp5CRkQFtbe3vGiT9EtHR0ShTpgx69+4NT0/PAhkDEeXs5/q/kkREREQkKleuHC5duoT58+cjPT1dPJ6WloZJkyZBLpejZ8+eDEj9ANra2j9dQAoA9PX10b9/fzg5OcHa2vqrvp3atWv/KwJSADBhwgQYGRlh06ZN33wtqVTKgNRndHR0CiwgBQDr1q1DZmYmZs2aVWBjIKKcMVOKiIiI6CeVmpqKBg0a4N69eyhVqhQaNmyIzMxM3Lx5E+/evYOdnR0CAgJQsmTJgh4qUYH5888/MXr0aNy+fVvtNDT6b/r48SNsbGwwYcIE/P777wU9HCLSgEEpIiIiop9YYmIitmzZAi8vL7x48QJyuRzW1tbo0aMHJk6cmOfi4EQ/s5YtW0JLSwunT58u6KHQdzJmzBhcuHABd+7c+abFHIjox2JQioiIiIiIiIiI8t3PN9GdiIiIiIiIiIj+9VjVkoiIciWXy/Hu3TsYGRkVaMFSIiIiIiL69xMEAQkJCShdunSOC38wKEVERLl69+4dypYtW9DDICIiIiKi/5A3b96gTJkyGs8zKEVERLkyMjICALx69QqmpqYFOxj6qcjlckRFRcHMzCzH/xWN6Gvw+6Ifhd8W/Uj8vuhHyq/vKz4+HmXLlhX/jtCEQSkiIsqVYsqesbExV+qi70oulyM1NRXGxsb8f7zpu+P3RT8Kvy36kfh90Y+U399XbqU/+IUTEREREREREVG+Y1CKiIiIiIiIiIjyHYNSRERERERERESU7xiUIiIiIiIiIiKifMegFBERERERERER5TsGpYiIiIiIiIiIKN8xKEVERERERERERPmOQSkiIiIiIiIiIsp3DEoREREREREREVG+Y1CKiIiIiIiIiIjyHYNSRERERERERESU7xiUIiIiIiIiIiKifMegFBERERERERER5TsGpYiIiIiIiIiIKN8xKEVERERERERERPmOQSkiIiIiIiIiIsp3DEoREREREREREVG+Y1CKiIiIiIiIiIjyHYNSRERERERERESU7xiUIiIiIiIiIiKifMegFBERERERERER5TsGpYiIiIiIiIiIKN8xKEVERERERERERPmOQSkiIiIiIiIiIsp3DEoREREREREREVG+Y1CKiIiIiIiIiIjyHYNSRERERERERESU7xiUIiIiIiIiIiKifKdd0AMgIqL/jpcvY2FsLC/oYdBPRBAEJCQkIDFRBxKJpKCHQz8Zfl/0o/Dboh+J3xd9DWNjXZiZGRT0ML4Yg1JERJRnE6b5QyrVL+hh0E9EIgGsLaV4+VoGQSjo0dDPht8X/Sj8tuhH4vdFX8NIXwd/be3wnwtMMShFRER51rhXNZS2KV3Qw6CfiSBAXxaPmlLjrP8vnOh74vdFPwq/LfqR+H3RF4p+n4CAf+4iPj6NQSkiIvp5FSlhiJJWpgU9DPqZCHJIEmQwMTIBJCx1Sd8Zvy/6Ufht0Y/E74v+h/ALJyIiIiIiIiKifMegFBERERERERER5TsGpYj+R8jlXDGNiIiIiIiI/j0YlCL6D0lISMDWrVu/uN+DBw/QqFGjrw5Mubm5wcXFJdd2QUFBuHLlitpz2traCA8P/6L7uru753hfLy8vvHv3TuX4pUuXYGtrq7Gf8NkyJoIg4PXr1180tu/h6NGjCAkJ0Xj+5cuXmDVrFpKSkgAAxYsXR1hYGADg8OHDWLt27VfdNywsDKampl/Vl4iIiIiI6HthUIroP+Tx48eYMWOGuL9gwQJIJBKlrU+fPkp90tPTMXDgQDRu3BhaWlooWbKkSh+JRILExMRvHt+rV6/Qrl077NmzBwCQnJyM9PR08XxaWhpSU1ORlpam1C/7OOrUqaPx+idOnEBqaqq4f+PGDdSoUQOPHz8GAMTHx4uBN0EQkJqaitTUVGRkZIh9BEFA06ZNsX//fvFYREQE7Ozscn2+OXPmqH13iq1ly5YqfYYMGaLUZvTo0eK7cXFxwenTpzXez8DAAJcuXULr1q2RnJwMAJDJZFi6dCmGDBkCBwcHlT7jxo3TOD5PT080a9Ys1+ckIiIiIiLKDwxKEf2HBAYGIjU1Fc2aNUPnzp0BAKNGjUJKSgpSUlJUsqjkcjlGjhyJtLQ0LFq0CADw9u1bZGRkqGyGhoY53lsmk4lBHnXBHgDo168ffH19MX78eLx58wYjR45UGpOtrS309fVRokQJles/ePAAu3btAgAsX74cEokEkydPxo4dOyCRSLB06VL07t0b79+/F/usWbMGs2fPRp8+fSAIAqpUqYJHjx4BAF68eAF9fX3o6+ujZ8+eYh+JRIL169dj1KhRuHXrFgDg6dOnsLKyUnm+1NRUlayqSZMmie87+6YIxKmzZs0axMTEoGzZsmjdujUAYMqUKTA1NcWKFSsQFRWltp+5uTlOnDiBGjVqQCaTAQCkUinS0tJw9uxZODk5qfRZt24dwsPDoauri4yMDAQHB8PCwgIZGRkoXry4xjESERERERHlNwaliP4j5HI5PD09sWzZMuzatQtbtmwBAPj5+aFPnz7o06cPNm/eLLbPzMzEgAED4O3tjQMHDiAmJgYJCQmQSqXQ1tZW2XLj6ekpBnkU24gRI8Tznz59wtu3b9GwYUMcPHgQZcuWVbnGmzdvIAgCYmNjVc65ubnB398fADB9+nRkZGRg9erVGDZsGDIyMnD9+nUMGTIE1tbWAIDQ0FAkJydj0qRJWLt2LSQSidL1bGxsIAgCBEHAkSNHlM45ODhg27ZtMDMzAwD4+vri6dOnKs+nr6+Pp0+fKvWVSqV4/vw59PT0xM3d3R3R0dEa3522tjY2btwIW1tbdO3aFW5ubti1axd8fX0xYMAAtGnTBhEREUp9DA0NIZFIYGxsjM2bN8PY2BjR0dGwtrbGokWLUK9ePTED6smTJ2I/LS0tSKVS8b7Z/01ERERERPRvwqAU0X/EmjVrEBwcjEmTJqFs2bLo2rUrAMDR0RFubm5wc3NTmrqXkZGBlJQU+Pj4oFKlSujevTvWr1+PzMxMtZsiE0eT4cOHi0Eexebp6Sme37lzJypXrgx/f380a9YMmZmZEAQBcrkcmZmZALKyrfJyv89rX7m7u+PevXtYvHixeO7XX3+Fg4MDXr9+DScnJ6V7KK6d/fkU/V69eoUnT56gatWqKFGiBBISEuDh4YG5c+eidu3aKs9YqVIlpbEkJiaiW7duWL9+PQAgJCQEbm5uMDY21vg8CQkJWLp0KcaMGYM+ffpg1apVOHPmDGxtbbFkyRL07NkTdevWxaFDh8Q+cXFxyMjIwI0bN9CgQQO0bt0apqamcHd3x9ChQ3HixAmkp6cjIyNDaYwlS5ZEqVKlkJaWBolEgsqVK+Pt27eQSCRYvHixxjESERERERHlNwaliP4Dnj9/jmnTpuHEiROoVasWDh8+jIsXLwIATExMYGtrC1tbW5ibm4t99PX14e3tjaZNm2LChAmIi4tDzZo1oaOjo3azsrICoFyLSbEpgkjqprcppvBNnjwZ69atQ48ePXDo0CHo6OjAy8sLEydOhI6ODmQyGcqVKyfez8bGRuPz2tjYQEdHB1OmTMFff/2F3377DWFhYShSpAhmzZoFADh48CCaN2+Orl27YsWKFdDR0cHbt2/h4OCAZs2aITQ0VOn5hg0bBiBr+l2HDh1gb2+Px48fY9asWahfvz6aNm2q8uzqCsMbGhrizJkzWLVqFf755x/Mnj0bo0aNyrFwuJGREUaMGIG///4bxsbGSEpKgqOjo5ilNnfuXJiZmYn1pUJDQ7Fy5Uo0bdoUdevWRcWKFeHt7Q2pVApnZ2d06NABf/zxBxo2bIirV68q3evt27d48OABrK2tVabvzZ49O7dPjYiIiIiIKN8wKEX0H1ChQgUcP34crVu3hkQigVQqhZZW1q/vli1bxGlk2afTKcyZMwebN2+Gl5cX2rVrp5QFpMieEgRBXBlP3TS2mTNnqp2+9/kUviFDhmDJkiVwcnJSyqaSyWSQSqV48+YNUlJS8OjRI3EVuewUU/DCwsIQExODYcOGwc7ODrdu3UJGRgbkcjmWL18OANDR0cHGjRvRvXt3TJs2DYIg4N27d/D19UVgYKA4fe/NmzeIiooSs7q8vb0REhICCwsLvH79GoGBgdi2bRsA4Pbt20rP5uXlpXaMVlZWOHr0KMaNG4egoCDMmDEDurq6SkXdP7d06VLcunULo0ePxs2bN7F582YcOXIEiYmJSEhIwMCBA7Fp0yYAWVluixcvRsmSJREYGIidO3eicOHC2LJlC0qXLo1u3brhzJkzmD9/vsq0QYlEgt9//x19+vRRmb7XsWNHnD9/HikpKSrTHT+XlpaG+Ph4pY2IiIiIiOh7YlCK6D9CkQ1069YtdO7cGTo6OkhNTYWzszNWr14NS0tL+Pj4oHfv3mL9qIEDB2L16tUAAHt7+zzdp1KlSipT2FatWoXhw4fj2bNn+Pjxo8YpfAAwZswYBAUFAYAYOMkeADly5IiYtZRdQkICChUqBCArgGNjY4O//voLT58+Re3ataGjo4Pdu3cr9dHW1kafPn1w//59AMDGjRtVCo4vXrxYfAefs7S0xN27d1GyZEkAUJm+N2DAAKX2cXFxMDAwAJAVKDQzM4Oenh60tLRgbGyMhIQEje+1cOHC6N69OzZt2gQnJycULlwYo0ePRnp6Ol68eIHVq1eLAaSKFSvi3bt38PLyQp06dcTMrdmzZytlrLVq1Qpt2rQRpy5GRESgbdu2KFSoEBYsWCDet0iRIkhISIBcLoeNjQ2qVKmS6/ewbNkymJiYiJu6GmFERERERETfgkEpov+IgIAACIKA2rVrw8fHB4IgQE9PD5aWlhg2bBiioqJQrVo1zJs3D8eOHUNiYiIyMzNx5cqV7zaGXbt2oVq1ahg9ejTi4uLUtrl27RomTpyIqKgouLq6YsGCBeIqdmlpaViwYAEmTJig1MfOzg6RkZEwNzdH8eLFUaxYMRgaGsLPz09cHdDZ2Vnt/fbt2wd3d3dcvnwZGzZswPTp05GamgqJRIJnz55hz549GDlyZJ6fUV0dKoX379+jRIkSEAQBw4cPR+vWrVG+fHksWLAAVlZWePHihdprRkZG4vDhw7h06RIaN26Mrl27Ijg4GHXq1IGnpydOnz4trsoHABcvXoSpqalKVtqzZ89gYmKicnzq1KkAsupJrVmzBsOHD8fly5cREBCAFy9eQCaTYevWrbh48SL+/PNP+Pr64s8//8zxPcycORNxcXHi9ubNmzy/QyIiIiIiorzgckxEPwF9fX00bNgQ9vb2mDt3LgYPHgwA2LNnDxITE7/bfRYuXIipU6di7ty5cHBwwOXLl1GqVCmlNjt37oSjoyOaNWsGJycn9OrVC+bm5rC2tsZvv/0Ga2tr9O3bV6nPnTt3ULRoUYwZMwbjx4+Hubk5tm3bhjZt2ii1+7yfIAj4559/0KFDB7Rv3x5r167Fx48f0apVK7Rv3x6dO3fGzJkzxRX7cnPr1i3o6OiI+8OHD8f27dvFfcVUvdmzZyM0NBR///03Xr16hZo1a2L8+PGQyWR49uwZKlasqHTdwoULY8OGDejQoQMGDhyIcuXKYfDgwfD09MT79++xevVqpWyuZs2aQRAEpWsMHToUpUqVwrVr1zB+/Hh06dJF7TOUKlUKrq6uSsfCw8Ph6ekJb29v8VibNm0wd+5cje9CV1cXurq6ml8WERERERHRN2JQiug/KCoqCrNmzRKnuwHA9OnT0a9fP/Tq1euH3tvIyAju7u5wcXFRCUi9fPkSe/fuxc2bN3H79m10794dUqkU9+/fx/DhwxEVFYXjx4+rXHP79u2wtLTE/fv38ccff4jZXf7+/mjZsiUAoEOHDir99u/fj4yMDMyZMweOjo7o3LkzgKysssGDB6NTp06YMWOGxmcJDw/Hn3/+ifnz5wPImr538+ZNtW19fX0hCAKCgoKwd+9eBAUFQVdXFxUrVkSbNm2wY8cONGnSBN7e3mjfvj2qVasm9jU0NMS5c+fE/dq1a+Pvv/9G06ZNERAQAB0dHTg5Oam9r1wux5w5cxAUFIRr164hPDwcbdq0gb6+vkrQDgCKFi2KS5cuKR1r0KABFi9eLL5LIiIiIiKifwNO3yP6j4mOjsa4ceOQkZGBxMRE3Lp1C23atMHWrVsxZswY1KxZE6tXr8bHjx81XuPVq1d49+4dIiMjxTpGXyp70AUAkpOTMWDAAIwbNw62trbo1KkTLl68CBcXF9SoUQP169fHmTNnYGJiotTv4sWLmD17NtasWYPFixdDLpeLU8tatWoFiUQCiUSCEydOKPULCQnBhAkTsGnTJhgbG6Nx48Y4cOAA2rVrhx49euD333/HunXr1I49MzMTnz59wvDhw1GuXDkUK1Ysx2dNSUnBjBkzMH78eAwZMgRnz55VCsitWrUKU6ZMgaurK9zd3dG6dWuEhoaqXOfmzZuQSCQwMjKCk5MTJBIJmjdvjqtXr0IikShlP3348AE7duxArVq1EBQUhIsXL6JIkSKwt7fHoUOHMGbMGHTq1Am7du3Cp0+fAGQVvVe8r+zbtWvXlN5l9o2IiIiIiKigMFOK6D9m3bp1qF27NkqXLo2lS5fC3Nwc/fv3R8eOHaGvr49atWph27ZtGDhwoMZrTJs2Df7+/ihVqhRatGjxXcYVHh6OatWqYcGCBejSpQtOnDgBW1tbdO3aFQ8fPtRYKPuvv/7C0qVLxZpR69atg5GRETw8PHDy5Ek0b94cANCtWzelfk+ePMGUKVNQr1492NjY4PXr16hXrx769OmDAwcOwNDQUONYtbW1cfLkSTg4OOTYTuHGjRsoXLgwfv31V+jr66sE1mxsbAAAXbp0weHDh/HixQvxWHZ16tRBRkaGxvsoVlQ8fvw4Bg4ciFatWim9G4WGDRvi6dOn2Lt3L/bv348DBw7g6NGjGDFiBIYMGZLr8xAREREREf0bSITPC5cQEX2jqKgo6OjowNTUNF/u9/r1a5ibm0NPT++H3UMul4tBo/yQmZkJbe1/z/9uEB8fDxMTE6w4eh/WVS0Lejj0MxHkkCR8gmBUFJAwgZu+M35f9KPw26Ifid8XfaGIV7E4tDIQOzY6w8amaI5t5XK5uMjUj/z7RvH3Q1xcHIyNjTW2+/f8xUNEPw0zM7N8vZ+l5Y8PkuRnQArAvyogRURERERE9CMw7EpERERERERERPmOQSkiIiIiIiIiIsp3DEoREREREREREVG+Y1CKiIiIiIiIiIjyHSvpEhFRnsV8SIS+YWxBD4N+JoIAfVkSUj5JAYmkoEdDPxt+X/Sj8NuiH4nfF32h6PcJBT2Er8agFBER5dml/cGQSkMLehj0E5FIAGtLKV6+lkEQCno09LPh90U/Cr8t+pH4fdHXMNLXgbGxbkEP44sxKEVERHm2bmUrGBsbF/Qw6CciCAISEj7ByKgoJPxfg+k74/dFPwq/LfqR+H3R1zA21oWZmUFBD+OLMShFRER5Zm1tClNT04IeBv1E5HI5IiMzYG5eBFpaLHVJ3xe/L/pR+G3Rj8Tvi/6X8AsnIiIiIiIiIqJ8x6AUERERERERERHlO07fIyKiPHv5MhbGxvKCHsZP779aE4CIiIiI6EswKEVERHk2YZo/pFL9gh7GT89IXwd/be3AwBQRERER/dQYlCIiojxr3KsaStuULuhh/NSi3ycg4J+7iI9PY1CKiIiIiH5qDEoREVGeFSlhiJJWpgU9DCIiIiIi+gmw0DkREREREREREeU7BqWIiIiIiIiIiCjfMShF+U4u/zlX7srIyCjoIeSooN57ZmZmgdyXiIiIiIiI/t0YlNIgIiICenp6Ksfd3d0xYsSIPF3jyZMnCA8Pz1Pb5ORk/PrrrxAEIU/tL1y4gPT09Dy1/RGuXr0KW1vbHNvExsaiefPmeP78uXgsOjoaNjY2P3p4XyQ1NTXXNh8+fMDHjx81no+NjUXx4sURExPzRfe+evUqypUrBwDw9PRE27Ztc2x/8uRJPH78+IvuoTBo0CAcPHjwi/rMmTMH48aN++J7lSlTBsHBwQAAX19f9OrV64uv0aVLF3h6eubYJjIyEvfv31d7rnjx4ggLC8ux/+nTp5V+/nfv3sWuXbty7LNq1So8ePAgxzYF6c2bN/jnn38AAIaGhkhMTMTFixfx+PFj3Lp1Cx07dizgERIREREREWVhUCoPIiIicPfuXdy9exevX7/Ghw8fxP2QkBC1fVJSUjBgwADs27cPqampSE1NzTHgdPjwYVy/fh0SiQRz5sxBkyZNIJFIIJFIsGHDBqW2MpkMixcvhpOTE+Li4rBr1y6xrbrt0qVLSv0DAgJybJ9T8OVLmJqaonv37mjbti3i4+MBAFeuXIFEIoGnp6fSduzYMbHfqVOnlMZTp04d7N69G4aGhiqbIqCjEBERAYlEAj09PZWtUKFCqFOnjso4W7ZsqRL8SE5Oxu3bt7FlyxZ06dIFNjY2WL9+vcZnPXLkCCpVqoQiRYp8/QvLgw0bNsDf31/cV/fze/LkiUq/ffv2wcfHB40aNcKWLVvU9ps6dWqu9y9XrpxSny1btuTa59mzZ0hJSUFwcLDS9vTp0y97eA3XbtmyJW7dugUg65tTjC06OhrW1tYa30taWhq6deuGuLg48diRI0dw+vTpHO9pYWGB5s2b49GjR7h06ZLadzljxgyx/ePHj2FqaipuI0aMyPH3b/HixQCAsLCwHNtl34oXLy7eTyKRwM3NDW5ubgAAHx8fdOvWDYmJiQgPD4dMJvu2l05ERERERPSdcPW9PNiwYQM2bNgAU1NT8ViXLl2QmpoKW1tbpaBP7969sX//fnH/1q1b4h/7V65cQYMGDdTeY+3atZg6dSrev3+PXbt2ISQkBNra2ti/fz9WrlyplK0ilUrh6+uLtm3bYu/evRgxYgR69Oghnu/Xrx/atWuHgQMHAgB0dXVV7lerVi0EBQWpHDc0NFQ5tn37do3ZYRKJROXYhw8fYG5uDgAYN24coqOjkZycDGNjY3h5eaF06dI4c+aM2P79+/eIjo5Gp06dxGOOjo4ICAjAuXPnMGvWLGRkZKB+/frYt2+f0r20tFTjqgYGBkhMTFQ5HhAQoDbwsnPnTtSrVw+1a9eGvb09fvvtN6xduxYVK1ZEjRo10KJFC2zdulV8JoVy5crh1atXub4PAHjw4AGqVaum9lx2giAoTXeTSqXiNTMyMnD16lWEhYXh8OHDmDhxIgAgPDwcxYoVAwDY2dmpXPPmzZsYNWoU3N3dUbp0abi6usLFxUWlnbp3qc7NmzdRo0YN9O/fP0/tL168iHPnzqFx48biMblcDlNTU7x+/Vo8pq2trRIwOXr0KIYOHSruOzk5ISAgQNxv3LgxFi1ahMGDB+PBgwd4+PChmEnWvXt3bNq0CSVKlEDp0qVRqVIlsZ9MJsPevXtRv359FCtWDHK5HFpaWjh37hyaN2+Oq1evim3r168v/gxSU1PRr18/vH79GpMmTYKfnx9SUlKUxrxw4UKlALRMJoOBgQHu378PW1tbLFq0CI6OjkrPpeDh4YHBgwcDACwtLVUy754/f46GDRuqBI6z/+xCQ0MxZswYMQD16tUrjB49GlpaWnj69CksLS1V7ktERERERFQQGJRSo0yZMnj79i2ArCCDo6MjJk2ahAULFmD27NkYN24cSpUqhTNnzmDBggVKfWUyGYYOHYqmTZsqHc8pC2Xfvn349OkTevTogaFDh2Lw4MHQ1s760XTv3h1z5szBvn370Lt3b7GPtrY2fHx8oK+vDwBKUw21tLSgo6OjdvqhQmRkpEoGFgC12VwuLi5iEEMQBHTp0gUpKSl49uwZxo4dCy0tLYwePRqFCxdW6hcfHw+5XI6JEyfC1NQUDx48wIkTJxASEgIzMzOxnZ+fn1JmSfZnzC1Qkj1QqJCUlKSUOaKQkZGBChUqqBy3sbHBwoUL8ezZM9jb2yMlJQWLFi1SO6bsHj16BLlcDj8/PwwZMgQhISEwMDBQ2zb7uzE1NVUKmpUpU0b89+nTp6GjoyPub9u2TXz33t7eMDY2hp+fHyQSiZiVNWLECBQqVAgAEBUVpXTfy5cvo127duK39fjxY1SuXFn8vjRxcXHBjh07xP2NGzdi/vz5ALICZdra2moDcB06dMCJEycAAPb29qhWrRpCQkIQHBwMCwsL8Zt8/Pgx2rVrp9L/4cOHKF26NACgf//+aN++Pfr16wcg6zvZvHmzSp8RI0bAxsYGEokEPj4+GD16tHhuwIABAIDBgwcrZcNVrVpVzNTS0dHB4MGDsXTpUly/fh2FChVCUFAQBEHAuXPnkJmZCalUCgDQ19dHQkICpk6dir59+yItLU28pkQiga6uLqRSqUodLUWmVvny5VGqVCloa2ujW7duSkHWzp07K71XLS0tle9bW1sburq6ar97BXd3d7x8+RKPHj2CTCbD4cOHoaWlhcTERISGhsLHxwd//vmn2P78+fNo1qyZxusRERERERH9KAxKqfHq1StERETAxsYGiYmJ2LdvH6RSKSIiIrBy5UpMnz4dAGBubq4SfAKyMpM+zzhSBFcuXbqEkiVLivWYwsPDMXbsWDRu3Bjbtm3DrVu3sG3bNrGfVCrF33//jfbt28PGxgZ16tRBcHCwOBXJ2dkZhQsXRnBwMDIzM+Hg4JDr81WqVAm///672nM7duyAkZGR2nMZGRkYOnQoPn78CDc3N/Tt2xe9evXCkiVLULlyZWzevBnOzs5i+7p16+Lt27dISkpCdHQ0+vbti8TERKWMI29vbxgYGKgEOIKCgsRjtWvXBpCVcZO9jlVcXBxevnypMoWvUKFCagNKoaGhuHHjhtpn+7xukoeHh1JGjsKUKVPQqlUrAFmBJkEQsHjxYgwaNAglSpQAkFXvy8vLS+kP/+w+fPggBv8uX76MYcOGiefatGmDU6dOqfT59OkTpk2bhlevXomZLr6+vgCyMnMUAarPgwvR0dHo0KEDtmzZAh8fHwwePBhhYWEqAUQFRWbW1q1bsWXLFrRo0QL29vZYu3YttLS0cq3x5O3tjTdv3sDGxgZ3797F3r17cfPmTVhZWaFs2bJ4+PAhihYtipSUFDHQk52xsbEYcNHR0UHhwoXFfU0BP6lUilatWkEul2Po0KFiIKpcuXK4ePEiLC0tIZVKIZPJlO6pCMYsX74cT548wYYNG9CpUycx0zElJQWGhoZqx3nq1CmV2kzFihXLcerriRMnlH4/fHx8lIKnSUlJ6Nu3r8b+APDx40e12YzZ7d+/HwMGDECPHj2wfPlyODs74927d5g7dy4sLS2xceNGjBw5EiNHjkSDBg0YkCIiIiIiogLDoJQaUqlU/ENUW1sbvXr1gkwmw4EDB1CvXj0UKlQIqampqFixIubNm6fSf8uWLWpr7QQHB2Pq1KnYsGEDbG1t8e7dO7Ru3RomJiZ4/vw5rl+/DgcHB7UZTsOHD4erqysuXboEHx8fbNy4Ee/evcOLFy9Qrlw5HDx4EImJiWJQSi6XIzMzExKJRHyWIUOGYOfOnbk+v2Ja0apVq8QMr5CQEPTr1w+6uro4fvw4pFIpvL29YWFhgU2bNiEwMBD9+vVD//79sXLlSnGqUGxsLIoUKYJ3796hSJEiqFq1Ktzc3NC0aVO0bt0awP/XRVJo3bo1UlJSMGLECNSoUQMTJkyAl5cXfvnlF6WAzefvKTU1FVKpFCNGjFBb4FoqlaJnz55ITU2FlpYWChUqhJs3b6Jly5ZIT0+Hs7OzWAi8Vq1a6N69O2JjYzFixAgcOHAAAFCxYkWla+7evRt3797F9u3bxWMxMTE51ktKSkoSa2x9+vRJ6Vz26XvZf3YTJ05EmTJl8Mcff2DJkiUICgoSs6Osra3F4MbnGVAdO3ZEx44d8eTJEwwePBh//PEHOnXqhAsXLqgdmyIzS0tLCx8+fMCVK1fw/PlzNGrUKE/T9XR0dHDz5k0AwOzZs2FlZQU3NzdcvnwZ+vr6KFq0KICsgI+6bK2yZcsq7aubvqcQGBgoZls1adIEJUqUUPm+q1atKv67Ro0auHv3rtpxv3nzBocOHcKZM2fEqXzp6eniO1bH0dFRnLobFhamtl5ZdkWLFhXfu7OzMy5fvqzSpnz58kr72TPPsvs8iDt9+nQsX74cADBq1CiUL18ev/32G5YvX46pU6di/vz5mD17NgoVKoQnT55AW1sbT58+xciRI3McMxERERER0Y/EoFQOBEGAp6cn/vjjD6XVthRT5hRSUlKUAiR//vmnUvaLXC6Hra0txo0bhwkTJoiZHDExMejYsSPs7Oxw7Ngx7Nq1C3p6esjMzET16tWxZ88eVK1aFVWqVMHIkSNRr149AMDMmTMxc+bMHKfwDB8+HMOHD0fVqlXFVdD++usvbN++HRkZGTA2NsadO3eU6ux8ThEQWbJkCZYvXw49PT18/PhRDCx87uDBg5gzZw5++eUXlalZ1apVQ0BAgJj1lJ0gCEoBCi8vLzHApK2tnWNgILvPfy6azJgxQ6xNVKdOHcTGxsLT0xPHjx8HAGRmZqJu3bro0aMHIiIiAECpZpfC48ePMX78eABZWTyKVdwyMjIgl8vFfUUATMHd3R1bt24V22bPTMs+fa9EiRLi/WfMmCHWJZJIJErvK/v0v+zTyRRCQkLQtm1bNGnSBEOGDMGQIUPEc8HBwWjWrJnaDJ/Vq1dDLpfD0tISI0eORJMmTTS+0+wUATzFCpY1atSAq6ur0jtMSUlR+/MKCwuDhYUFgKz6bO3bt8egQYMAZK08+Mcff4htGzVqhIiICJw6dQobNmyAp6cnJk2apBQgVGjevDm6d++udCw9PR2pqanIzMxEREQERo4cCV9fX/z999/YuHEjEhMTNWYNAsrZfADEul6aTJgwAWvWrMHevXtzzIh6/vy5mBF47NgxyOVy8VzXrl3x4sULSKVS3L59WzyefZrrtm3boKWlhR49emDkyJHQ19fHhAkTUK1aNaxatQp79uyBTCbD48ePYW9vr3EcaWlpSt+TIpBKRERERET0vXD1PTWSkpKwa9cupKenY+fOnTh16hSCg4NRpEgRxMTEQBAE2Nvb4/DhwxAEAXp6eoiIiMDu3bsREhKCvXv3ol+/fmjatCnKly8PAwMDvHnzBkOHDsXKlSvF+1StWhUrVqwQ/6A0NDSEtra2eL2KFStCT08PycnJGqcuaeLh4QFBEMSAFJD1h6u2tjbCwsKQmZmJEiVKIDMzU2VTBD0Uf3BXqFAB58+fh5OTE9asWQNBEFS2qlWrwsLCArdv31ZbKwj4/yBXmzZtoK+vj8DAQABZQaDsdZRCQkLE6Ynz5s0Ti3crAj2K7XOCIMDJyQmTJk2Ch4cHPDw8oK+vjw0bNmDs2LHiNmHCBKVC65+Ljo5GqVKlcny/UVFRaN++PRo3bgxTU1OkpqZCX18f+vr66NWrFwIDA8X9Fi1aKPVdtGgRIiIiEBERgaNHjyqda9OmjfhOFQEpxf2sra3RuXNn3Lp1Czo6Opg1axYA4O+//0bNmjUxa9Ys+Pr64sOHD2K/06dPw9HRER8+fMgxAPG5mzdvYtu2bejduzfq1q2LXr16KQVmNQkJCUFAQADMzMywfv16vHz5EiEhIfDy8oKrqyvc3d1x+PBhxMfHqw34KAKRiu9P8c2qqy8mlUphaGioFBAOCQnB+fPnUadOHXF7//69+K1lp/gO586di7p162L16tVwdXXFkSNHsGfPHsTGxuYYlHJ0dERGRgYyMjI0rsKZXaFChVC/fn0EBweLRczLlSsHf39/lChRAmfOnFFZvTH780dHR8PPzw+bNm1CaGgonj17pvbdKP599epVjBgxAlKpFGZmZujevTtcXV3x/PlzHDt2DOXKlcvxvyvLli2DiYmJuH2exUZERERERPStGJT6TGZmJjp06AA/Pz/o6uri/PnzKF26NObPn4+xY8fC1NQU586dQ2RkJNq3by/209HRwdy5c1G4cGGULVsW9vb2GD9+PCZPngwTExNIJBJxVa3cvH//HkZGRuIfxLGxsRqzkwD1xclzolidzNzcXAycZN/c3d2V2vfq1UucmjR58mS1S9I/fPgQQN6ylc6fPy8GkICsd65uhUAA6Nmzp5gd4+/vrzROdVlBwP9nwCgCV9WqVcONGzdga2sLKysr3LhxQ2Ohb7lcjitXruQawLl9+zYaNWqEQ4cOQSqVQk9PTwwmeXt7w8nJSdzPvjojADFLZe/evdixYwfevXsnZi8ppu8pNkWWTLNmzSAIAnx8fFC7dm0IgiBO12rRogVatGgBqVSKV69ewdXVVXw3wcHBmD59OsaMGZPj83zu119/hbu7u1i/ysPDA+3bt0dsbKxSAFExZsW7Gz16NEaOHInChQvDyMgIt27dwrJly9CvXz+kpqbi3bt3mDBhAt6/f6828Fe2bFnxmzp06BCGDh0q7n9ew0mTZ8+eYdKkSeJ28uRJte0U3+GyZcvEY6VKlcKaNWswa9YsvHnzRqkgvzqKoJC6ulOfGzp0KM6fP48qVaogPT0dbdq0wcCBA9GyZUtMnToV3t7eSE9P19jf1dUVbdq0gZOTEwYNGoQJEyao/d0vV64cJBIJ3r59iypVqkAikcDAwABDhgyBRCJBhw4dMHHiRLGAvCYzZ85EXFycuL158ybXZyQiIiIiIvoSnL73GW1tbZw7dw4fPnwQC2i/fPkSx48fx6tXr2BkZIR169Zh2bJlSlOyihUrhhcvXoj7YWFhmDdvHo4dO4Z169aprT2lyYEDB8SpTp8+fUJaWprGqUEHDx7EsWPHVLJxchIZGYk+ffpgz549Kufq1KmTY5bQkCFDlIJxCr/++muu91Usb9+8eXPx2KRJk5CamqoxY8Pe3h7t27eHp6enxiLgn1NMXwKypiCVKFECEydOxPbt25GQkIDJkydr7Ovj44NChQqhWrVqOd6jTZs2aNOmTa5j+dzSpUvx+++/Q19fH7Vq1UJaWhqqVauGZcuWwc/PT2X1vfbt24vTCnPSuXNntGjRApUqVcKpU6fEIJ/i55LT6o/q+Pn5QV9fX6lg/LVr1xAfHy9Or1M4evQogoODMWnSJFSqVAnz5s3D7t27AWQFCP38/PDu3TscOnQIDg4OSE9Ph4+PDypXrqx0nfbt22PDhg1iIKhfv35o3bq1GLALCQnBtWvXch27k5OTUibc4sWLcyxA/rlevXqhevXqCAwMVHnW7L50+l7Hjh3h4uICAwMD6OrqomLFili6dCmWLl0KIGvKrbopuXK5HNOmTcPNmzfFel3Lli1D5cqVMWPGDKxYsUKpfVhYGBo2bIhOnTph5syZaNSoEcaMGSMWNO/cuTO2bdsm1nTTRFdXV2OwmIiIiIiI6HtgppQanxcRtra2RlxcHJydnTF9+nR8/PgRFy9eVMkcEAQB58+fR8+ePVGlShXo6enhyZMnYk2cvEhMTMSff/4JFxcXAMCLFy9QokQJlbpKKSkpyMjIwJEjR5SmBObFqFGj1AakAOD169ewsbFRe27AgAGws7NDSEiIyjZmzBgxqyY7mUwGIOsP8h49eqB9+/a4e/cuUlJSsGjRIpQtWxYxMTEqf4y/fv0aL1++xLZt21ClShWkpKTk+fl27tyJ69evY/fu3WLmVt++ffHu3TtER0ejd+/eSu2vXbuGVatW4cKFC9i5c6dSIEYx5QrIyuj69OnTF2emZTdixAg8fPgQnz59EqexDRo0SAwEZp++JwiCxoDUyZMn4e3tLe7XrVsX9evXR7t27VRWI/wan2e8yWQyTJkyBY0aNVL6WclkMgQFBSEpKQmGhoZYv369UiCjUKFCOHfuHG7evImPHz/izJkzWLJkCRISEpRWUgSygltly5aFnp4e9PT0xKlriv1q1aph+PDhGsf86dMnhIeH48KFCzA1NRW3JUuWqG3fvHlzSCQSzJw5UzyWmJgIIGuFytu3b6sEzhS0tbXRsmVLcfpeaGhorqvidevWDY6OjuL+zp07kZqaCplMhri4OGzevFmlz/Pnz+Hs7Ixjx44hMDAQJUuWBACYmprCz88Pnp6eGDhwoBjwBbKmH3fs2BF79uyBtbU13r9/jz59+gAAkpOTsXz5cpQoUQLTpk1DRkZGjmMmIiIiIiL6kRiUUiM9PR3p6eliHZfdu3ejQ4cO8PT0hI+PD16+fAkdHR1UrVoVGzduhCAImDFjBiwtLdG3b1/Y2tri+fPn2Lp1K2JiYvDs2TMkJibmOsUnNTUVw4cPx+DBgxETE4O3b9/ixo0bqF69ukpbfX193L9/H8uXL0fDhg2xcOFCrF69Wu20p+yFrdW5cuUKnj59Cn9/f8THx6tMXRs3bhwkEgm6du0qFllXt1lYWEAikSAgIEDsGxUVBSCrEPjRo0exZMkS1KhRA3p6eujevTssLCxw6dIlMRB2/vx5uLm5ISAgALa2tlixYgVu374NfX19+Pn5qZ06eOnSJaSkpKBRo0a4evUqJkyYgLFjx+LUqVOQy+WIi4vDgAEDYG5ujvLly8PZ2RnPnz8HkDUtrV27dli0aBGCgoKgra2NadOmoUiRIrCwsIC9vT2eP38OPT096OjooEGDBipBSyCreLliPF27dsWFCxeUxrhgwQIAgJmZGUxNTbF7927Uq1dPXF0wr6Kjo3Hr1i3MnTtXrD80e/ZsWFpaYvr06di9ezc6duyo9DNQJz4+Hm/evEFoaGiepp65uLggKCgICxcuFI/Z2dlh/Pjx2LZtW44r81WsWBG1atUCAFy4cAEtW7ZEZGSk2qL3XyMlJQUPHjyAtbU1Hj58CCcnJ8TGxiImJgaxsbGYOXOm2p/ZmTNnkJGRoRS0cnNzw9y5c/Hs2TMcOXIEv/zyi9p7tm3bFv7+/uL0vfLly4srPgqCoPZ+n1ME3QwMDPDx40eEhYUhKSlJ7Ltu3TpUqVIFRYoUQVBQEKytrZX6V69eHUFBQXj79i0qVKiAO3fuAMgKmFWvXh26urqwsLCAXC7HpEmTEBISgubNm8PQ0BCPHz9GVFQUOnfujISEhDy9ZyIiIiIiou+N0/fU2LBhA6ZPn44hQ4bg5MmT8PDwwODBg9GzZ08ULlwYALB161Z0794dnp6ecHFxQaNGjdCsWTO0atVK6Y/8wYMH48aNG6hRowaqVKmi8Z4ymQz9+vWDs7MzXFxcsGnTJnTr1g1yuVycCvU5GxsblC9fPtcsotyCDr6+vli9ejUKFSqEpUuXis+Y/X1s2LAhx2toUqlSJfj5+amdKnTr1i2MGDECZcuWxe+//w4gq3i0r68vHB0dVQpbt27dGidOnFC5jqI+1M6dO1GsWDGl+lsHDhzA2rVrYWBgAH9/f0ilUixZsgTz58+Hl5cXevbsCScnJ5QvXx4AsH//fgBZgcmMjAzIZDLI5XIx4+vzd6Mwfvx4lVpc6sYIZAWR3r17hzVr1mgsCq9Jx44dcfDgQXTr1g0xMTGoXbs2zMzMcPr0aVSuXBm3bt3CkiVLcPfuXXG6ljrPnj1Du3btIJFIMHTo0FzvW6ZMGcycOVMpSLNw4UKlIFVehIeHo0uXLqhVq5a4muS3qlOnDpydnTFt2jQ8ffoUW7ZsAZCVFVSmTBmkpaVh586dKv0URdVbtGiBGjVqAMha3W7+/PmoU6cO2rVrpzTVNC+aN2+O+/fvw8PDI899EhMTsWXLFvz666+oV6+emHE4ZMgQNGzYEHXr1tXY19bWFufOncO5c+fEqZHVqlWDmZkZpk2bhp49eyIqKgpnz55FkyZNMHToUCxatEicpty7d28EBQWhbdu2X/ScRERERERE34NE+Ja5SET/AXnNXCHNZDJZnjKqClpMTAyioqJQsWLFgh5Kjn7k+0xOTlYbPE1KSvriVTyzi4+Ph4mJCVYcvQ/rqqpTden7iXgVi0MrA7FjozNsbDQvcvGzkMvliIyMhLm5ucr/GEH0rfh90Y/Cb4t+JH5f9CPl1/el+PshLi4OxsbGGtsxU4p+egxIfbv/QkAKAIoUKSJOa/w3+5HvU1M237cEpIiIiIiIiH4Ehl2JiIiIiIiIiCjfMShFRERERERERET5jkEpIiIiIiIiIiLKdwxKERERERERERFRvmOhcyIiyrOYD4nQN4wt6GH81KLfJxT0EIiIiIiI8gWDUkRElGeX9gdDKg0t6GH89Iz0dWBsrFvQwyAiIiIi+qEYlCIiojxbt7IVjI2NC3oYPz1jY12YmRkU9DCIiIiIiH4oBqWIiCjPrK1NYWpqWtDDICIiIiKinwALnRMRERERERERUb5jUIqIiIiIiIiIiPIdp+8REVGevXwZC2NjubjP2kdERERERPS1GJQiIqI8mzDNH1KpvrhvpK+Dv7Z2YGCKiIiIiIi+GINSRESUZ417VUNpm9IAgOj3CQj45y7i49MYlCIiIiIioi/GoBQREeVZkRKGKGllWtDDICIiIiKinwALnRMRERERERERUb5jUIqIiIiIiIiIiPIdg1KU7+Ryee6N/oMyMjIKegg5Kqj3LpfLIQhCgdybiIiIiIiI/r0YlPrOIiIioKenp3Lc3d0dI0aM+KprxsfHIyIiQuV4ZmYmJBIJPn78+FXX/RYRERHQ1s65JFlmZibatm2La9euiccEQUDZsmWRlJT0o4eYZ6mpqbm2iYuLQ3h4uMbzgiDAysoKwcHBX3TviIgISCQSAEBAQAAqVaqUY/vAwECl9/kl5s6diz/++OOL+mzfvh0dOnT44ns1btwYx48fBwA8fPgQ9evX/+JrTJo0CQsWLMixTUJCgsb3UadOHQQEBOTYPyAgAHFxceL+69evsW7duhz7/PXXXzh//nyObQpSXFwc/vzzT8jlclSrVg3BwcEIDg7G5cuXER0dDXt7+4IeIhEREREREQAGpX6oiIgI3L17F3fv3sXr16/x4cMHcT8kJAQAkJKSgk+fPiEqKgrh4eG4e/cuTp8+jT///BMTJkxA48aNUb58eWzdujXX+3l6ekIikYhbyZIl1bYLCwtTavf5dvPmze/y/Nra2hg9ejQ6dOiAN2/eAMgKUCQkJODAgQPw9PQUNy8vL7HfkydPlMZTvHhxBAYGwtDQUO32eYBLT09P7aarq4vixYurjHPYsGFYtGiR0rG0tDQEBwfD09MT/fr1g6WlJebNm6fxWS9cuABBEFClSpVveWW52rNnD/bt2yfulytXTuXnd+rUKZV+ly9fhpubG1q0aIFTp06p/bn36NEj1/s3a9ZMqc+MGTNy7fPs2TPIZDIxOJJ9+1bv3r1Dly5dcOLECQCAg4ODOLZbt26hefPmOb6XYcOG4dmzZ+L+uXPncPDgwRzvWa5cOfTs2RPnz59HeHi42nfZp08fsX1SUhJMTU3FzdXVNcffPxcXF7FvTu0+3xITEwFkBUj37duHCRMmAACuXr2K1q1bIzIyEuHh4ZDJZF//womIiIiIiL4jrr73A23YsAEbNmyAqampeKxLly5ITU2Fra0tLl26hKCgIHTo0AF6enowNDSEqakpPnz4gBo1aqBr167o06cP6tatCx0dnTzds1OnTjh06BCePn2KX375RWO7YsWKqc38qVq1qsqxM2fOoFWrVmqvo8jyye7atWuoV68eAKBz5854+fIlUlJSAABeXl4oX748zpw5I7aPj4/HxYsX0a9fP/GYhYUFwsLCEBoaCkdHR8hkMpQqVQpXrlxRuZ+BgepS9OHh4SoBqLCwMNSpU0el7bp161C7dm3Ur18fbdq0wYYNGzBp0iSUL18eNWrUQJ06dbBs2TJYWVkp9WvWrBkuXLigdEwqlapcHwB8fHzynHGUmZkp/ltLSwtaWv8fOw4MDERSUhJu376N3r17AwCCgoJQq1YtAEDr1q1VrvfixQv07dsXM2bMgIODAwRBUDvVMPt9cnLo0CF06tQJs2fPzlP7ixcv4tGjR2jcuLHS8bi4OMhkMvG+tra2CA0NVem/cOFC8d9WVlYICwsT9+3s7ODh4YHBgwcjNDQUZ8+exZ07dwAAY8eOxciRI1GjRg0UKVIEtWvXFvvJ5XIEBgZCKpXCwcEBMpkMUqkU586dQ6VKlXD16lWxrYODg5j9mJaWhubNm2PDhg0YPXo0Hj16JH7bCp6enkoZWoIgIC4uDh8/fkTTpk0xdOhQ9O3bF82bN1d51vnz5ysFP2NiYpTOp6WloWTJknj48CFKly6tdM7Q0BBAVhDQ1dUVQUFBSExMxNu3b9G9e3cUL14cT58+haWlpcp9iYiIiIiICgKDUt9RmTJl8PbtWwBZwRpHR0dxCtLs2bMxbtw4lCpVCmfOnBGnJbVs2VJl+lifPn3QsmVLpYyJ7MLDw1G2bFlx38zMDADg4eEBiUQCbW1tjcERhZSUFGzYsEHlePapTAotW7ZUqgk0fvx4PH36FP7+/ti5cydCQkIwadIkFC1aVKlfYmIiMjMzMWTIEBgbGyMiIgKbN29GYGAgqlWrJrZ7+vQpGjRooHLfvDyHiYmJ2uN2dnYqATO5XK428FK8eHH88ccfeP78Odq0aYPU1FS4uLhgy5YtOd7b19cXMpkMjx49QqNGjXD//n2Nf/Dr6+uL/3ZwcNCYJfT06VOlAOTs2bOxePFiAMD169cRFhaGmzdvwsDAAEZGRlixYgVmzZolBj4fPXqkcr1ffvkFVatWxbx58/D48WNUrlw516mXixcvxty5c8V9iUSCwYMHA8gKXmlra6sNSI4bNw4bN24EAHTs2BEWFhaQy+U4ceIEGjVqJAZ3UlJSULhwYZWfh6+vr/gtzJw5EyYmJmI21uPHj9G3b1+Ve7Zt2xY7d+6EoaEhLl26hHbt2onnpk6dCgBwcnJSChQ5OzvDz88PAFCoUCE4OTnh1KlTOH78OGrUqIE5c+YAAC5duoSHDx/CxsYGQNZ3dfz4cfTu3Rv169dHenq60lj09PQ0vtuEhARERUWhXr16uHz5MmrXrq0U/Jo4cSK0tbWV3kn2gDbw/0EqMzMzlXMKnp6euHnzJm7evIlChQrh8OHD0NfXR1RUFMzNzeHn56f0s/Pw8MCQIUPUXouIiIiIiOhH4vS97+jVq1cIDw+Hrq4uMjIyMHr0aFSqVAkRERFYuXKlmNFjbm6Opk2biv0qVaqkNA1n3759GDFihNKxXbt2ie3LlCmDjIwM+Pv7A8iaJvglRbaLFy+OjRs3onjx4iqbm5sbrK2t1fYTBAHTp0+Hn58fNm/eDCArIJCeno6qVavC09NTqX23bt1QpkwZFClSBM+fP0e/fv0QHx8Pe3t78bnc3d2hpaWlEuB4+/YtJBIJKlSoIB578eIFbG1txa1EiRIICgpSO9aJEydixowZStvo0aM1vpPu3btj3Lhx4v7JkyfRtm1blS37z0FfXx+GhoZYvHgx2rRpgypVqsDQ0BDv3r1Dv379lKYYZg+uXb9+HYmJiUhMTMTTp0+VxmFnZwdBEMRNEZBKTU3FuHHjkJiYiEqVKqFs2bLYs2cPgKzaS25ubnBzcxODJwpxcXGoWbMmDh48iHv37qFu3bp4+fIlMjMz1W6KYuizZ89GRkYGBg4cCGdnZ2RkZOCvv/7S+P4U3N3dkZCQAH19fRw9ehRz5syBkZERmjdvjrp16+Lhw4cAsoJS6gKEimxBU1NT6OrqQk9PT9w3MjLSeN+2bdtCLpejRYsWSEhIQEJCAmrWrImTJ08iISEBJ0+eVJm25uHhAUEQxPf4999/w9bWFufPn8eZM2dw5swZmJiYqM1SfPjwIcqXLw99fX2lTTGFTp2TJ0+iTZs24nPfvXtX6Xdv+/btub5fRf04RVaUOhs3bkSTJk0wbNgw2NraokuXLjA3N8e2bdtw7Ngx/Pbbb8jIyMDChQsxc+ZMBqSIiIiIiKjAMFPqO5JKpWLwQVtbG7169YJMJsOBAwdQr149FCpUCKmpqahYsaLSFJ2HDx8qZSL1798fv/zyC4YNG6Z07ey0tbXh7e0NABg8eDAOHTqU6/gWLFigNBUqN2PHjhWzqT58+IAhQ4YgLCwMZ8+ehZmZGa5cuYKiRYti8eLF6Nu3L3r27Al/f39s374d+vr6OH36NICsbI/o6GjEx8ejefPm6N27NwYOHCgWflcEqBTs7OyQkpKCxYsXIy4uDm5ubrhy5QoqVKiAJ0+eiO0+LwqelpYGQRAwevRoREZGqn2m4cOHIzU1FRKJBLq6uoiMjETFihWRmZmJSpUqifW0KlasKGaq9evXD2vXroWZmZlKzajAwED4+Pgo1SFKTk7G/fv3Nb7X1NRUfPr0CQAQFRWlcj779D1F1s2SJUvw6dMnXLp0CZ07d0Z4eDh0dHSwfPlylClTBra2tgCUM7IAoF69evDx8cGHDx/QvXt3TJ06FQsXLsTOnTvVjk2RmSWRSJCZmYmTJ08CAFavXo3p06drfKbs41VMaVu2bBmcnJzg7u6ON2/e4MWLF2LQLCUlRW1GUZMmTVSOfT59T+H169fiz8PKygo9e/ZU+b6dnZ3Ff5uYmCA2NlbtuBMTE7Fw4UK4u7uLU/kAID09Hbq6umr7WFhYKE2BVZc5ll3RokVx//59yOVyODg4KGVJZb9mdtkzz7IrXLiw0n7v3r2xd+9eAMCiRYsQExMDd3d3NGrUCD179oSenh4WLVqE+Ph4PHnyBNra2nj69Ck6duyY45iJiIiIiIh+JAalfgBBEODp6Yk//vgDDx48EI9/HjBISUmBnp6eSsBJIpGIU6Q0iYiIwD///AMgqybTb7/9JtZx0mTevHnitCRbW1ts2LABbdu21dhekdHx119/YcqUKdDT00NERITGKWpbtmzBrl274OnpqZKVVLp0aVy7dg3du3dX6ScIgtKznjlzBtevXxen72kKCnzOzs4Or169yrXdypUrxdpE5ubmiI2NRUBAgDjVKzMzE1WqVBELfw8YMADt2rVDuXLllK7z4cMHDBw4EEBWXStFUCw9PR2CIIjTMhUBMIV9+/aJU+MUmUkKn0/fU3wjY8eORdeuXZWCnoogiKOjo/jv9PR0leLjkZGRaNu2LYoWLYq5c+dCKpWKWW2JiYkwMjJCQkKCSvbN9u3bERMTg6pVq2Lx4sVwcnLK9d0CwIEDBwAAGRkZOH/+PJYuXYoVK1agffv2StP3Pv99ALJWw3N0dAQATJ8+HQYGBmIA9+HDh+jcubPYtmzZsoiIiMDjx4/Rs2dPLFiwAMOGDcPKlStVrlujRg2V1S8zMjKQmpqKjIwMfPz4ES1btkRiYiK6du2KI0eOQEtLC0lJSRoztBTZfHnVo0cPrF69GgcOHFAqhP45f39/tGzZEkBWvTN3d3fx3KRJk3D+/HlERETg7du34u9N9qyzuXPnQktLC1OmTEHNmjVhaWmJsWPHokqVKli4cCHWrl0LALh375743wN10tLSkJaWJu7Hx8fn+VmJiIiIiIjygtP3vqOkpCTs2rUL6enp2LlzJ06dOoXg4GAUKVIEMTExEAQB9vb2OHz4MARBEOvPfL6KlrrpexKJRKxDJQgCxowZg549ewIAli9fjkePHillW6mjCHSlp6fj9evXKFmypNopXACUattYWlpi37596N+/PyZOnKg0vUyxtW/fHiYmJvD398eoUaPU3l8RUBk5ciT09fXFqXCZmZlKgZi3b9+Kxaq3bduGkiVLIj4+Xgz0KLbPnzcsLAyDBw/GgAED4OHhAQ8PD5QtWxbLli3D2LFjlbYbN25ofE/R0dEoVapUju8yJSUFHTt2hLm5uZixZWVlBX19fdSvXx+vX78Wp3R9PqVuxIgRiIiIQEREBG7fvq107vPpe4ogTmpqKmrXrg0HBwdER0dDR0dHLAzv5uaGtm3bYvjw4WJmk8LNmzfRqFEjPHnyBJUrV861RpfCq1evMG/ePAwePBiWlpaYMGFCjtlfCjExMfD09IS1tTXmzZuHQoUK4dmzZ1i3bh3GjBmD/fv3Y8OGDYiPj1cb7JFKpdDW1hZ/LxTfrLr6YhKJBIaGhkrBrcjISBw4cAB16tQRt4yMDHF1vuwU3+GgQYNQrlw57Ny5Ez179sTLly+xYsUKJCUlQS6Xq2QlKVhYWCAjI0Pc8qJJkyZioDopKQkNGzbE4cOHYWxsjNOnT6Ny5cpK7bM/v0wmw/79+7FgwQIYGxvj3Llz4rnsQSnFv69du4Z+/frB2NgYhoaG+OWXXzBy5EgUKVIEx44dE7MENVm2bBlMTEzELXsdOyIiIiIiou+BQanvJDMzEx06dICfnx90dXVx/vx5lC5dGvPnz8fYsWNhamqKc+fOITIyEu3btxf7JSUlISUlRWnr2bMnNm/erHJckdVw7tw5hISEYNmyZQCAKlWqICAgAHFxcWrr33wevHny5AkEQUDdunVVauLo6+tj8uTJSu1btmwpruq2du1atcvRK/7o19PTyzV7RFHLR1E4OzMzU2M2VIsWLbBlyxbo6+vj2bNnSuN89uyZ2j6KDBhF4MrKygrh4eEwNDRE/fr14e3trbIyX3ZBQUGwt7fP8RmePn2KEiVKwN/fX6wVFhERAUEQcOfOHVhZWYmBpc9XORQEAaGhoTh8+DDWrFkDQHnVvOwBQkUdpHLlykEQBDx48ADFihWDIAjidK2GDRuiffv2SE9Ph0wmw7Bhw8Tpi6GhoejRoweWLFmS4/N8bu7cuRg/fryYfbdkyRKMHDkSsbGxKt9Y9u9r6tSpaN26NSwtLaGlpYXAwEAcOHAADg4OKF26NMLDwzFjxgw8fvxYbeCvSZMm4je1evVqLFy4UNzP7Wei8PHjR0yaNEncstcBy+7zmlJAVq2m7du3Y8WKFXj06FGO3wkAMSiUW+F4AJg2bRr27t0rFvnv2bMnqlatis6dO2PNmjU4evSoxumFiv4WFhbo1q0bRo0ahalTpyplMik0a9YMEokEQUFBcHZ2hkQigVQqRfPmzaGrq4uuXbti3Lhx6NWrV45BypkzZyIuLk7c3rx5k+szEhERERERfQlO3/tOtLW1ce7cOXz48EGc5vXy5UscP34cr169gpGREdatW4dly5ahUKFCYj91wRhFdoQiS+Zzv/zyC65du6YS/PHz81OpswQAXbp0waZNm8R6Ne/fv4ejoyMuXbqk0rZHjx45Zgm1b99ebWHk5cuXa+yjoFg5bOjQoRg6dCiArJXoUlNTxcDO52xsbNClSxcEBATAzs5OqaaUJocOHcKRI0cAZE1nK1KkCH777TeMGDECjx49wtixYzUGzu7evYsHDx6gRYsWOd7DwcEBPj4+uY7lc3///TfGjh0rXsPAwAAlS5bE1q1bERYWpjJ9r2rVqhpX6suuXbt2+O2333DhwgWcPHkS5ubmALJqDfXu3VvtSos52bp1K/T09JRWIHz79i0ePnyIMmXKKLW9e/cuNm3ahC1btsDIyAgrV65UmqZ57Ngx3Lp1C3Xr1kWNGjVgZmaGf/75R6V+UosWLbBv3z5UrVoVQFZQRE9PD/PnzweQVX9r//79uY7d2toaISEh4v6uXbuUan7lpn79+rhy5QrCw8NVxpjdl07fa9WqFdq0aYP69esDAGrVqoVly5bBw8MDQNbvdfny5dX2Xbt2LTw9PXHlyhVIpVL8+uuv2LVrF4YNG4adO3cqBcUCAgLQp08fmJqaYvPmzejfvz+qVKki/t527twZs2fPVgqEqqOrq5vnqbNERERERERfg5lS39Hnf6BaW1sjLi4Ozs7OmD59Oj5+/IiLFy9+l4yDz+vxnDp1CqdOnVLKwpLL5fj06ZO4iphC+/bt1QakgKzi0Z9PN8ver3HjxggJCVHZevTogRo1aqjtJ5fLMWbMGDRo0ADNmjXD2bNnkZKSgk2bNqFy5cqIiYlRWd4+KioKDx8+hI+PD6pVq6YUZMjN0qVLcefOHezfv18MKjg6OqJs2bI4f/680ip7QFbm2OzZs/H8+XMsWLAA06ZNU5qyVb9+fejq6kImkyE2NlapEPmX6tq1K27cuIG4uDicO3cOcrkcvXv3FgOZn0/f0xSQunz5stJqeGXKlEG3bt3QsGFDVK9e/avHp6AuIDp16lSULFlS6foymQyXL19GYmIipFIp3N3dUaxYMaV+R44cwcWLF/Hp0ydcvHgRy5Ytg46OjlicXWHr1q2oXbs29PT0xFprUqlU3C9btiymTJmicczJyckICQnBy5cvxRX7TE1N4erqqrb90KFDIZFI0LdvX/GYYvW8ypUr4/bt2yrT6RSkUikqVqyoNH3PwMAgxyCVIiilsHDhQqSmpqJMmTK4d+8efH19Vfq8e/cOAwcOxNKlS3Hu3DmxsLuOjg5OnjyJmzdvon379nj79q3YRy6Xo379+njw4AHKly+P48ePi9+8TCbDwoULUaJECcyfPx8JCQkax0tERERERPSjMSj1HaWnpyM9PR3a2tqIjo7G7t270aFDB3h6esLHxwcvX76Ejo4Oqlatio0bN6pMq3v79i3i4uLw4cOHL8rAuHfvHvr06YMmTZqIBZKLFy8OQRBQrFgx9OnTJ8cl5O/cuYOHDx/i7t27uH//PurUqaN03s3NDRKJBC1atMDMmTM1blWqVIFEIhGLaANAXFycWDA5MDAQkyZNQosWLaCnp4fWrVujSpUquHjxohgIe/bsGSZPnox79+5BW1sby5Ytw5UrV2Bra4unT5+qnTq4a9cuCIKATp064dChQ1i6dCmGDh2Kw4cPIz09HWlpaZgyZQrCw8Ph7OyMX375Bbdu3QKQVVS6fv366N+/P548eYLixYtj/fr1MDExgYWFBSwsLPDo0SPY2NhAW1sbVlZWausHHTlyRBxPzZo18erVK6UxKrJUjIyMUKZMGfj4+IgFvRXTMPMiOjoa0dHRGDJkCEqVKoXk5GSsW7cOdnZ2aN26NR4+fIimTZvCx8cnxxpjycnJeP36NZ4+fQpAdXXHzy1cuBB79+7FggULxG+zQoUKWLNmDaZOnaq0UuTnLCws0LhxY0ilUly/fh01a9ZEYmIiateunefnzklKSgqioqJQrlw5nD9/HtbW1oiNjUVsbCxiYmKwefNmtb9PO3bsQEZGBnbv3i0e27t3L0aPHo3nz59jz549+OWXX9Tes3Llynj69KnS9L3ExEQYGBhAEIQ8/f4qMiINDAwQHx+PV69eISYmRux76NAh2NjYIDIyEleuXFF5X2XLlkVgYCCMjIxgZ2cnBrUEQUCtWrVQuHBhGBkZwcrKCkOGDMHbt2/RsWNHhIaG4uHDhyhdujRatGiBiIiIPL9rIiIiIiKi74nT976jDRs2YPr06RgyZAhOnjwJDw8PDB48GD179hQzb7Zu3Yru3bvD09MTLi4uStNjJk2ahIMHD6Jw4cJwc3PL831LlCgBKysrcTU+ICsoFRUVlaf+N2/exJQpU8SMps8zpaZOnSquTPelTExMcPLkSTRr1kwl8PHmzRuxQLpiKlzFihXh7e0NR0dHldpFdnZ2ajOHpFIpJBIJ1q1bBz09PZQsWVI85+DggH379iExMRFBQUEwNTXFxo0bMXv2bPj4+KB58+a4cuWKmIGyfft2AFl1qdLT0yGXyyGTySCXyyEIAnR1ddWuGtepUyelVdLUjVFh7dq1OH/+PCZPnqyUpZMXjRo1wt9//42+fftCEARUqlQJxYoVw+7du1GnTh107twZbm5uCAoKQseOHTVeJz4+HnXq1IFcLke/fv3UPlN2ZcqUQb9+/ZSCT8OHD8fw4cO/aPxJSUno0KEDSpYsqZTV9y0qVKiALl26YMyYMdDV1cXZs2cBZAVn7Ozs8Pr1ayxdulSlnyIoVLNmTYwcORJAVv208+fPo379+rC3txeLyefVoEGDcPz4cSxevDjPfR4+fAhfX1+0aNECdnZ2qFmzJoCsb8rPzw9NmzbV2Nfc3BwHDx5EUFCQGLRq3rw5YmNjMW7cOAwfPhxpaWnw8fFBq1at0KhRI/G/Md7e3nBxccGxY8fE5yciIiIiIspPEiG3JdvoPyEzMzNPxZaJvtZ/4RtLTk7GixcvxGLiBSEvmVI/8l0mJyerXTEwKSlJY+22vIiPj4eJiQlWHL0P66qWAICIV7E4tDIQOzY6w8am6Fdfm/63yeVyREZGwtzcXGklSaLvgd8X/Sj8tuhH4vdFP1J+fV+Kvx/i4uJgbGyssR2/8J/Evz1YQP99/4VvrHDhwgUakAJUa8up8yPfpbqAFIBvCkgRERERERH9CAxKERERERERERFRvmNQioiIiIiIiIiI8h2DUkRERERERERElO8YlCIiIiIiIiIionz3769cTERE/xoxHxKhbxgLAIh+n1CwgyEiIiIiov80BqWIiCjPLu0PhlQaKu4b6evA2Fi3AEdERERERET/VQxKERFRnq1b2QrGxsbivrGxLszMDApwRERERERE9F/FoBQREeWZtbUpTE1NC3oYRERERET0E2ChcyIiIiIiIiIiyncMShERERERERERUb7j9D0iIsqzly9jYWwsB8B6UkRERERE9G0YlCIiojybMM0fUqk+gKyV9/7a2oGBKSIiIiIi+ioMShERUZ417lUNpW1KI/p9AgL+uYv4+DQGpYiIiIiI6KswKEVERHlWpIQhSlqZFvQwiIiIiIjoJ8BC50RERERERERElO8YlCIiIiIiIiIionzHoBQREREREREREeU7BqWI6H+aXC5HfHz8V/dPTk7+jqMhIiIiIiL638GgFBH9Z6WmpubaJi4uDuHh4UrHGjdujFOnTgEAnj17hipVquR6jbCwMJw9exZz587FzJkzAQCJiYmwtLTM0ziy09PTQ0RERK7tevToAU9PTwBApUqVEBAQoHQ+MTER9+/fVxrnn3/+CblcjmrVqiE4OBjBwcG4fPkyoqOjYW9v/0XjJCIiIiIi+pEYlCKi/6xhw4Zh0aJFSsfS0tIQHBwMT09P9OvXD5aWlpg3b554XiaTAcjKkMrMzBT3MzMz0axZM0gkEnFTBIRGjhyJihUrwtnZGffv30enTp0AAB4eHoiOjoa+vj4kEonaoE9aWhpSU1OVNk3HBUHI03MnJydj8+bNqFy5MmbNmiUeFwQB+/btw4QJEwAAV69eRevWrREZGYnw8HDxWYmIiIiIiP4NGJQiov+sdevWYceOHfDz8wMAbNiwAQYGBujWrRtOnDiBGjVq4P79+/jrr7/EPiYmJggKCkL79u2ho6ODatWq4e3bt9DR0QEA7N+/HykpKejatavYx8vLC2lpaWjVqhX69u2Lhg0b4urVq5g3bx4ePnyIjIwMLFy4EDVr1lQZY4kSJaCvr6+0paWloVy5cirHX716lePzenl5oUePHjA3N4ebmxtmzZqFI0eOiOefPXsGV1dXaGlpITExEW/fvkX37t1RvHhxPH36FJaWlt/yuomIiIiIiL4rBqWI6D+rePHi+OOPP/D8+XMAWdP5XFxc8OzZMxw4cADTp0+HlZWVUp/ExEQ4OjrixIkTyMjIQHBwMCwsLMQsJalUCm1tbUgkErGPlpYWZDIZBEGAXC7Hhw8f0KxZM9jZ2eHy5cvw9PTE9u3bUahQIXh5eamM8+XLlxAEIcfNwMBAbL9nzx7UqVMH586dw8KFC8XMrPv376Ny5co4ffo0QkJCMHr0aPz6669iUM7T0xOrV6/Ghg0bEBERgcOHD+PGjRvYsGEDLl26BD8/P7WZYERERERERAVBu6AHQET0Lbp37660f/LkSbRt21al3YABAzBgwABxv3379uK/LSws1F6vY8eOAICdO3di6NCh4vWdnJzg5eWFdevWYdeuXQCA8uXL4/79+zh+/Dj69eundD19fX1kZmZqfAYtLS00adIEenp6AIBq1arBxcUFW7duhb29PVq3bg03NzeMHTsWffv2BZA1DTE9PR3Hjh0Tx7xx40ZMnToV1atXx9WrV9GlSxfcuXMH27Ztg729PX777TcsXboUS5cuRWpqKoYMGZLDmyUiIiIiIvqxmClFRP9JkZGRMDU1haGhIerUqSMer1ixIlxcXODi4oJz586hc+fOcHFxQa1atZT6Z8+Uys7b2xuCICgFp4YMGYKoqChIpVL07NkTR48eRWJiIhwcHJS2ypUrq4xzx44d2Lx5M3R0dDRunTp1gq+vL0qWLAkAsLe3x6hRo1C+fHk0b94c/fv3x+jRozFq1CilfkZGRrC3t0eTJk0AAIsWLUJMTAz++OMPAEDPnj3RsGFDLFq0CPHx8Xjy5Am0tbXx9OlTVK9ePcf3m5aWhvj4eKWNiIiIiIjoe2KmFBH9J5mbmyM2NhYBAQGYOnUqgKxi5VWqVEGPHj0AZGVHtWvXDuXKlVPprylTSpMdO3YAACIiItCjRw+sWLECRYsWVWoTHh4uTqXLbtq0aZgxY4aYCVWpUiXs2rVLKZiWk5SUFAwdOhSDBg1SOSeVSqGllfW/L8ydOxdaWlqYMmUKatasCUtLS4wdOxZVqlTBwoULsXbtWgDAvXv3MGfOnBzvuWzZMixcuDBP4yMiIiIiIvoazJQiop9GdHQ0SpUqlae2vr6+EAQBjx8/Vjo+btw4VKpUCadPnxaPvX37Fn/++SeaNm2KMWPGwMjICD4+PujYsaPSNnr0aLX3On36tFgXSiE9PV1p5T11K+PJ5XJs374dlpaWGDRokEphdH19fXFaIQAxOHXt2jX069cPxsbGMDQ0xC+//IKRI0eiSJEiOHbsGCIjI1GxYsUc38/MmTMRFxcnbm/evMn5hRIREREREX0hBqWI6KcRFBQEe3v7HNtIJBIEBQWhXbt2kEgkqFy5Mt6+fQuJRILk5GTs3bsXT548QevWrcU+w4cPx5gxY1C4cGEAwOHDh9GrVy+MGjUKx44dg4uLC5KTkxEUFITQ0FCVe0ZERIhT8xQcHR2VgkubN28Wz+3cuRMODg44e/Ys7O3t8ejRI0ilUqxduxYZGRnitnHjRqVrNmvWTHw+Z2dnSCQSSKVSNG/eHLq6uujatSvGjRuHXr16QSqV5viedHV1YWxsrLQRERERERF9TwxKEdFP4e7du3jw4AFatGiRY7uUlBTExcVhzJgxiIuLw/z58xEVFYWUlBS8f/8epUuXVumzefNm/Prrr+K+n58f+vXrB19fX5QuXRp37tzB6dOn4e7uju7duyM1NVWpv6+vL96+fYu0tDTx2I0bN5RW3xs3bpx4ztnZGadPn0arVq1Qv359mJmZAQAmTpyoVFNq7NixSvcJCAhA79694erqCrlcjr59++L3338XC5p37twZb968UQq4ERERERERFRQGpYjoP+vJkyeYPXs2nj9/jgULFmDatGliNhMA1K9fH7q6upDJZIiNjUVmZib09PSwadMmvHjxAjKZDP7+/ujUqRM+fvyIuLg4WFtbq9zH2tpanBr3/v17uLq6ikEkuVyOgwcPonr16nj9+jUKFy4s1rgCsrK3Ll68CEtLSzRo0AAeHh5IS0tTO11PwczMDObm5irH169frxTIyp5dpRhL/fr18eDBA5QvXx7Hjx8XxymTybBw4UKUKFEC8+fPR0JCwhe8aSIiIiIiou+PQSki+k/y9/dH/fr10b9/fzx58gTFixfH+vXrYWJiAgsLC1hYWODRo0ewsbGBtrY2rKyskJGRgWPHjsHNzQ3r169HkSJFcObMGSQkJGD8+PFo2bIlZDIZEhMTkZqaqnaKW2pqKvr374/hw4ejYcOGqFevHqytrVG+fHm0b98eGzduFFfDO3v2LLp3745Vq1bBw8MD8+fPh5eXF6KiotCgQQPo6OhAV1cXOjo6kEql6NOnDwAgIyMDMTExiI6OhkQiEe89fvx4SCQScfu8hpUgCKhVqxYKFy4MIyMjWFlZYciQIXj79i06duyI0NBQPHz4EKVLl0aLFi0QERHxA39CREREREREOePqe0T0n9S8eXNcuXIFVapUAQBs374dQFZAJz09HXK5HDKZDHK5HIIgQFdXF6mpqXB1dcWhQ4dga2sLANDX18eFCxfQrl07zJw5E9HR0ShTpgwsLCzE1eqys7a2FoNHe/bswc6dOyGXyyGVSqGjowMA6N27tziW2bNnw8XFBQDQpUsXdOnSBQCQmJiI+Ph4pKWlQS6XA4CYHfX+/XtYWVmhVKlSaNiwoXjvtWvXYsyYMeL+1q1bcfnyZaV3Ehsbi3HjxmH48OFIS0uDj48PWrVqhUaNGuHgwYMoXLgwvL294eLigmPHjmHkyJHf+JMgIiIiIiL6OhJBEISCHgQRUX6Jj49XW7Q7OjoaRYoUEafpFTS5XK40luTkZGhra6NQoUIa+yQnJytNX1RISkqCgYHBN40nPj4eJiYmWHH0PqyrWiLiVSwOrQzEjo3OsLEp+k3Xpv9tcrkckZGRMDc3/9f8/tHPg98X/Sj8tuhH4vdFP1J+fV+Kvx/i4uJyXDSJmVJE9D9F038QixUrls8jydnn/wdCXbDpc5rafGtAioiIiIiI6Edg2JWIiIiIiIiIiPIdg1JERERERERERJTvGJQiIiIiIiIiIqJ8x5pSRESUZzEfEqFvGIvo9wkFPRQiIiIiIvqPY1CKiIjy7NL+YEiloQAAI30dGBvrFvCIiIiIiIjov4pBKSIiyrN1K1uJKxgaG+vCzIwr+xERERER0ddhUIqIiPLM2toUpqamBT0MIiIiIiL6CbDQORERERERERER5TsGpYiIiIiIiIiIKN8xKEVERERERERERPmOQSkiIsqz2NiUgh4CERERERH9JBiUIiKiPIuJSSvoIRARERER0U+CQSkiIiIiIiIiIsp3DEoREREREREREVG+Y1CKiIiIiIiIiIjyHYNSRERERERERESU7xiUIiL6Au/evUN4eHhBD4OIiIiIiOg/j0Epop9cnz598M8//4j7Li4ucHNzE/ffv3+Po0ePolevXujVqxcAwNvbG0+ePMHBgwfRo0cPAMDOnTuRlpaGX3/9FXv37tV4v7t376J48eIqxzt06IDDhw/nONYtW7YgKSnpi54vMzMTO3bsyHP76OhoPHjwQOX4lClTcOfOnVz7z549G7NmzRL3ExMTIZFIxH25XI6IiIgcrzF16lQsWLAgxzZpaWlYvHgxEhISch0TERERERHRfxGDUkQ/uUmTJmHatGk4ceKEyjkHBwfY2NjAzc0NdevWxbZt2wAAMpkMTZo0QWhoKORyOYYNG4bNmzdDS0sLL1++hJbWl/+nIzExEenp6RrPZ2RkYNKkSThy5AgkEonaLSwsTKXf7t27sWPHDiQlJUFPT09p09HRgb6+vlL7p0+folOnTti+fbvS8X79+qFnz545BoEUgbp58+ahZ8+euHXrlnhOLpfD398fDRo0wLhx4/L4VjTT0dHB+/fv0bp1a2RkZMDd3V3je5k0aZLG8aalpYn7J0+eRGho6DePjYiIiIiI6HvQLugBENGP1aBBA3h5eeHkyZOQSqUIDw+HRCJBUFAQUlNTcfLkSTRr1kxs/+nTJxgaGqJXr1549+4doqKiULRoUUyePBnR0dF4+vQpLC0tVe4jk8mQkpKClJQUAFlBKD09PWhr5+0/M0FBQShTpgxKlCiBevXq4cKFC0rnS5YsqdInIyMDixYtwo4dO2BgYIDU1FSl8x06dEDRokWVjjVq1AhXrlxB48aNAUApgCSTyWBmZibu29nZ4d69ewCyAjyjR49Gx44d8fbtW0ilUnTp0kUMTJUrVw6CIGDixIkYP358np45J1paWli/fj369u2Lly9fYvjw4bC2tkb79u2xdetWBAUFYefOnThy5Ij4LNnJ5XJ069YNS5YsQdeuXQEAO3bsQO3atZUyvYjo/9i777Aorvfx+++lo4Ag2Hs3do0dDRbsvffeW9SoscWu0RiNJvbEgjUm9l5QsfeCBcWKBQUFBASpC+f5g4f5se6CJLHkk+/9uq5zJXP2nJkzs7Mkc1/n3COEEEIIIT4XCUoJ8R/24MEDOnfuzLp166hXrx5z587lzp07PHr0KNU+jx8/ZurUqTx79ozXr1+TIUMG4uPjuXXrFkopfH19qVatmtY+X758PH78mFOnTlG7dm2t3t7eHg8PD3r06JGusXp4eNCoUSMAdDodNjY27+3z66+/UqxYMWrVqsWaNWsoVKgQX331FQA7d+7k9OnT+Pj4GF2TwoULc+vWLWxsbOjbty9TpkzB19eXzZs3ExQURPny5Vm9ejUNGjTQ+s2YMYOnT58SGhrKpEmTcHBwwNbWluXLlwOwefNmqlatipmZGW3atGHZsmVkzZpV658yYJaQkIBerzcKor17zmZmZvzxxx8AREREMHjwYE6dOoWZmRk6nQ4LCwtmzZrF+vXrjYJ233//PTY2NrRo0UKrGzVqFK1bt2bgwIFGwTohhBBCCCGE+NQkKCXEf1jhwoXp1KkTjRo14vr16xw5coS+fftSvHhxRo8eTfHixQ0CSQCenp6sX7+eevXqMXnyZC5cuMCbN28YMWIEr169AiA8PBxra2ucnJy4c+cOAK6urgQFBQFJgZ969eoZ7Ts1jx49YvPmzVruK6WUUcDmXWFhYcyaNYsjR47w4sULxowZw4EDBwC4evUq3bp145dffiFXrlwG/YYNG4atrS2bNm3S6qZOnUrLli3p2rUrN2/epF+/fgYBKYDVq1cb5Zx68uQJ1apVY8qUKRQtWhQzMzMCAgLw9PTEyclJa6fX642WEQLMmjXLYPvly5daIMvFxYWQkBAAoqOjsbe3p1u3bqxYsYICBQoAEBwczKNHjyhevLjBftatW8dPP/3EmTNnDJZaVq9enXbt2tGsWTP27t1rMEYhhBBCCCGE+NQkp5QQ/3EjR46kfv36Rm+MS072feTIEVasWEH79u2Jj4+nfPnytG7dmtWrV1OwYEEsLCxYt24dBw4cYNGiRTg6OvLgwQOePn1K3rx5tWCLpaUlLi4u2NnZMXz4cL7++mtat26t5T06ceIEnTp10rZTJvoeOXIksbGxWFpaAnDx4kVsbW0NSnh4uMH4586di16vZ+DAgZQpU4avv/6aSpUqsWXLFmrXrk2WLFmYMGECGzZsMOi3e/duLC0t6d+/v1YXExND9erV2bRpE/7+/lStWpXExESDfgEBAdSsWZOhQ4cydOhQ+vbty9ixY8mbNy9169YlS5Ys6HQ6cufOzciRI7VzAbCwsEAppZUePXowe/ZsbTs5AJcySPT8+XMiIyMNxtCkSRM8PT217cWLF9O0aVNtiaRer2fChAkMHz6cnTt3UqRIEfR6vUGZP38+JUqUoEyZMtosLFNiY2N58+aNQRFCCCGEEEKID0lmSgnxf8DMmTMNAhzLli1j4cKF2NnZYW5ubrAczNnZmevXrxMREUH27Nk5duwYuXPnpnDhwuzfv5+BAwdy/fp17O3tKVu2rMFx9Ho9Xbt25d69e8yePZvp06ejlAKgbt26DBgwQHvDX/IMnh9//JEzZ85QuHBhlFLUqlWL+Ph4AMaOHYuNjQ3Tpk0zOqf69etjaWnJ5cuXKV68OO7u7jRu3JgjR44wf/58hg0bxpYtWxgxYgTbt29n/fr1ZMyYEUtLSzw8PDh58iQeHh7s3r2bo0ePUqlSJS5evIiPjw9Dhw4lMjKSevXqMXz4cCpWrAhAxowZWbhwIQB+fn5MmjQJgK5du7Jnzx4sLS0xMzN7byL4R48eUa9ePW07ODgYOzs7g0CWtbU15ubm2nZCQgLlypVj6tSp+Pv7o5SiXr16tGzZEr1ej4WFBXq9nvDwcC5cuED37t25cOGCyeMnL+dM/m5MmT17tsnrLoQQQgghhBAfisyUEuL/gHPnztG+fXv69u3LunXrKFGihJbA2xQzMzMuXbpEmTJltPxR5cuXZ9y4cbi6urJjxw4OHTqEq6ur1iciIoJWrVpx/PhxEhIS6NGjB1OnTsXCwgILCwt0Oh1mZmbadnLgxtzcnN27d5MrVy5OnDiBpaWlVn766Se+//57g7oHDx4AUKtWLWrVqsWVK1fYtGkTwcHB5M6dmzt37jBo0CD0ej2tWrXi4sWLvH79mkuXLmljtbW1JTExEWtra1xdXdm6dSsODg5UqlSJnj17Urt2bebNm0fevHkpWrSo1i88PFw7bq9evbT6Xr16ERsbi4WFBUeOHKFPnz6pBnzCw8O5dOmSQV6uoKAgnJ2d0/wOCxUqhIODAy1atGDIkCFs2rSJGjVqUL58eSwtLQkLC8PGxoYlS5ZQtGhRzp8/j1KKoUOH8u2332qzsqpVq4azszPt27enQ4cOqR5v/PjxhIeHa+XZs2dpjk8IIYQQQggh/iqZKSXE/wHXr1+nRIkSWFhY4O7ujpubG87OzsTExJAhQwaDtsePHzfIBaXT6YD/l9A8JiaGnj17YmVlxcyZMw2OERsby4YNG+jbty+nT5+mfv369OnTh/z586c6tm+++Ub791q1amkzkRo2bMjly5fJkiULWbJk4eTJkwb9Hjx4QPv27Rk4cCCbN2/m2rVrBAQEULhwYaNjeHp6GrxhMD4+nu7du3P+/HkKFSqEr68v58+fN2g/fPhwunbtarAfZ2dnAgMDAfDx8TFIIn748GE2b97M0aNHmTFjBkop7dql9PPPP1OhQgUKFiyo1QUEBJA7d+5UrxEkJaBPtnz5ck6fPm20NNGUEydO8P3332vbwcHBuLi4vLeftbU11tbW720nhBBCCCGEEH+XzJQS4v+Aw4cP06dPH5YvX07OnDmBpOTZAQEBRgGjWrVqcevWLTJkyICfnx+3b9/G0dFRe4udjY0NtWrVIlu2bGTLlk3rV6NGDQ4fPqwFPPLly8ft27fTDEilZvny5Tx48IBOnTrRunVrEhIS+OGHHwzarF27loiICI4fP05YWBg9e/bEy8sLpRSenp58+eWX2uwgd3d3g76///47pUqVolChQgCEhoYanEtqQkJCtBlb5cqVM/hs9uzZVK5cmTt37tCzZ0+TS/j27t3LDz/8wLx58wzq/fz8DGZkfShnz54lICAANzc3rS4wMPC9s7KEEEIIIYQQ4lOQoJQQ/3H37t3j9u3bVK1a1aB+9+7dlClTRnvbW0pxcXF0796dKlWq4O7uzpAhQ8iYMSMAFy5c4PDhwzx58kR7W15qUuZESq+lS5cyefJktm7dirW1NTqdjj///JMVK1YwZMgQIiIigP+3vOzw4cOUKlWKH374gd9///29+4+NjWXmzJmMGTNGq9uyZQs1atR4b19nZ2ct0JWcKD6Zl5cX48aNI2fOnEybNo3FixdrnwUHB/PNN9/Qvn17fv31V6pWrcqyZcvw8fEhKiqK3bt3U6lSpVSP++rVK4YPH/7e8aUUFRXFsGHDmDRpEvb29kDSMsHIyEgtMCmEEEIIIYQQn5MEpYT4j5s+fTrDhw/H0tKSqKgo3r59S0JCAlOmTGHs2LEm++TIkYP8+fOj0+moVKkSy5Yt49ixY2zcuJGGDRvy888/s337dgYOHMiKFStM7mP58uXam/ZMvX1Pp9Oh1+sN+uzatYtVq1Zx/Phxg5lIuXLl4vz587x69Upb7nfjxg0GDBhAtmzZWLhwIZ07d6Zly5bvvR5Tpkwhb968NG7cGKUUCxcuZMOGDYwePTp9F5Sk/FnBwcFaYnJzc3MePnxIYGAgL1684MSJE9ryvBkzZpAnTx4uXLjA6dOn6dKlCzqdDm9vb+rVq4eDgwMhISFaAviUkhO+lylThoSEhHSP78WLF9SuXZuiRYvSqVMn1q9fz71799i4cSPFihXT3pgohBBCCCGEEJ+T5JQS4j8sOjoaS0tLhg8fjlKKggULkpiYSJkyZShfvjxt27Y16nP06FFatGhBmzZtOH36NIULF8bT05Nr167x888/s3XrVurWrQvAwYMHGTNmDN26dTPKTdW7d286duyY5vgsLAz/BLVo0YLVq1druZhS5jXKmjUrW7ZsMTi3DBkycOrUKcqWLYubmxtDhw4FIDExEb1ej42Njdb+u+++Y9y4cTx79ow1a9YAcOjQITw8PDh8+DD58uVL1zUFGDRoENu3b+fbb78FYOjQoTRq1IjExER0Oh01a9akfv36QNKb+WrVqkXNmjUN9rFixQpWrFiRau4pSErI/ssvv1CvXj2KFy+errENGzaM9evXM3DgQL7//nt0Oh2rV6/Wrs3PP/+c7vMUQgghhBBCiI9Jp9J6J7gQ4v+kqKgooyBTWvX/y9IKCqWUmJhIWFgYmTNn/gSj+vtOnz5NwYIFP/gSvTdv3pApUyauXvWjfPn8H3Tf4v+2xMREXr16RdasWU3mYhPin5D7S3wscm+Jj0nuL/Exfar7K/n5ITw8HAcHh1TbyUwpIYSR1AJP/7WAFJCugBSAmZnZvz4gBaQrN5YQQgghhBBC/BtI2FUIIYQQQgghhBBCfHISlBJCCCGEEEIIIYQQn5wEpYQQQgghhBBCCCHEJydBKSGEEEIIIYQQQgjxyUlQSgghRLo5OVl/7iEIIYQQQggh/iMkKCWEECLdHB1tP/cQhBBCCCGEEP8REpQSQgghhBBCCCGEEJ+cBKWEEEIIIYQQQgghxCcnQSkhhBBCCCGEEEII8clJUEoIIYQQQgghhBBCfHISlBJCCJFuYWHRn3sIQgghhBBCiP8ICUoJIYRIt9DQ2M89BCGEEEIIIcR/hASlhBBCCCGEEEIIIcQnJ0EpIYQQQgghhBBCCPHJSVBKCCGEEEIIIYQQQnxyEpQSQgghhBBCCCGEEJ+cBKX+D0hMTPzcQ/g/Lz4+/nMPIVWJiYncv3//cw9DCCGEEEIIIcT/Mf/zQanAwEBsbGyM6hcuXEi/fv3+1j7fvHlDYGCgUb1er0en0xEcHPy39vtPBAYGYmFhkWYbvV5Pw4YNuXDhglanlCJPnjy8ffv2g49p3rx5nDlz5r3tatWqxd69e7XtmJiY9/YJDw/H39//H43vfZ48ecLq1as/6jGSVa9enYMHD36SY71r5cqVhIeHp/r5zZs3qV+/PhYWFuh0ulSLh4eHQb+YmBi2bdv2Qcf66NEjbt68aVC3aNEinjx5ku59jBgxgqlTp6bZJiIiwuB3klLFihU5fvy4yc/u3r1rVNe/f3/+/PPPNI+3du1aXrx4wbx58xg9ejR6vR4PDw+UUnTo0IHTp0+n2T9Z8eLFUx3buxwdHXn16pVBXf78+Tl//ny6+gshhBBCCCHEx/Y/H5RKKTAwEG9vb7y9vXn69CkvX77Uth88eABAdHQ0r1+/JigoCH9/f7y9vTl8+DArVqzg66+/pkaNGhQsWJBff/31vcfz8PAweGjPnj27yXaPHz9O82H/8uXLH+T8LSwsGDRoEE2bNuXZs2cA+Pj4EBERwZYtW/Dw8NDKpk2btH4NGjSgePHiWhk6dCjFixdPdbxhYWEAFCtWjKZNm3Ly5EkKFy5MxowZsbOzw8rKipkzZ6Y6zt69ezN9+nSDutjYWG7duoWHhwedO3cmb968TJ482aCNt7c3tWrVwt7enqJFi6YaUHr06BEZMmR478P7qFGjGD9+PBEREWm2S5bWNdHr9an28/Pz4+bNm1SrVi1dx6lRo0aa94upa5s/f36DNn/88QcAly5dol+/fjx//jzV4505c4ayZcsSGxtLfHx8qqVHjx4G/W7dusXAgQMB8PX1NTnWZs2apeuck23YsIHly5dr23FxcYwePZqEhIS/tJ/3efHiBS1btmTfvn0AlCtXThvzlStXqF27tradHEyMiYmha9eutGzZ0uCeOXLkCPnz50/zeGFhYVSrVo3Xr18TFRVFixYt2L17Nzqdjnv37mFlZfVBzw+SArsyS1IIIYQQQgjxb/afCkotXryYWrVq0bJlS7Zv386NGzdo2bIlDRs2pGfPnkDSA3jOnDkpUqQIVatWpWvXrnTp0oUtW7ZQrFgx5s6dS0BAgFFAJDXNmzcnPj6eW7dupdnO2dmZ6Ohoo1KwYEGjtkeOHDF6uM+RIwcJCQkmH/wvXryo9W3RogUTJ04kOjoagE2bNlGwYEGOHDmile3btzN48GCD67Z+/Xr69+/PgwcPqFu3Lj4+PsTHx+Pr64uVlRXh4eFacMLR0RGAZs2a8dtvvzF79mwgKQAWGRnJhAkTALh8+bLJGWe//PILq1at4tChQ9rxM2bMSOvWrdm3bx9ly5blxo0bBkGn169f07BhQ6pUqcKNGzeYOXMmQ4YM4cCBA0b7HzhwoHb+qdm2bRsnT57E1dWVMWPGpNk2pUOHDhl8f76+vibb9ezZU/t+ChYsSGxsLI6Ojia/v8WLFxv13759u8n7ZdCgQamO7cqVK1y+fBk7Oztq1KhBbGws/fr1o2jRoowZMybVAMXu3bupWbMmCQkJ6PV6kyX53kvJ29ubypUrA0kBypTjDAoKokCBAnTs2DFd11UpRXx8POvXr6d9+/ZakO/06dO4uLjw6tUrzp8/z/nz57WAa7LChQsbXM+ff/6ZadOmGdS9GzQqVqwYa9asoXfv3kRGRnL06FE8PT3x9PSkaNGizJs3D09PTy5fvkzDhg0BsLGx4eTJk0RGRtK/f38Arl+/jp+fH1WqVDH6Xnft2gXA8+fPKVasGO7u7rx48YJXr17h5OREz549CQkJ4eHDh+TNmzdd1wmSgk3BwcFG5e8EoJRSf7mPEEIIIYQQQnwoaa8H+5fLnTu3NgNEp9Ph6uqqLd2ZOHEiQ4cOJUeOHBw5ckRbzuPu7m60fKxjx464u7vTt29fk8fx9/cnT5482naWLFkAWLNmDTqdDgsLC8zNzdMca3R0tMngg6llVe7u7gYPi8OGDePu3bt4enqydu1aHjx4wIgRI8icObNBv8jISPR6PT179sTBwYHAwECWLVvGqVOnKFWqlNbu7t27VK1a1aBvvXr1iImJYfHixbRq1YrHjx9z8OBBDh48SL58+bSZVTVr1qRkyZIABAcH07ZtWxo1akTZsmV5+PAh27dv1/a5ePFi2rZta3R+Li4u/PTTT9y/f58GDRoQExND3759DWbIvGvfvn3Y2toyZ84cdDodBQoU4MiRI2zcuJFGjRpp7davX09sbGyq+4GkYFmPHj3YuHEjbm5ulC9fnjlz5jBu3Lg0+wFYWVkZLBe1trY22W7FihUsXryYoKAgSpcuzbZt23B1dTXZNrV9PHnyhGLFigFJM8mmTJmCmVnqcWQrKytGjhzJmDFjyJIlC506dQLg2rVrNGrUiF69erFy5UosLS21PiEhIRw9epQ5c+ZQpkwZk8vTAPLly8fjx48BOH/+vMGsL51OR5kyZbh+/ToACQkJDBgwgIoVK9KlS5dUx5vSH3/8oY23Vq1aQNIMsy1btmBnZ8d3330HwMOHD+natSszZsww6H/gwAHtnh4/fjyZMmXSvs87d+5o+06pYcOGrF27Fjs7O06fPm1wH40ePRoANzc3gxl3tra2bNiwgR9//BG9Xs/SpUsZOnQoP/74o9YmIiKCHDlyUK5cOSApeDdjxgwePHjA27dvcXJyIm/evHz//fcMHjxYa5/s3WO+q2XLlibr/fz83jtjK6XHjx/Tpk0bFixYwFdffZXufkIIIYQQQgjxofxPz5R68uQJ/v7+WFtbEx8fz6BBgyhevDiBgYHMnTuXjBkzApA1a1aDh653l2H98ccf9OvXz6Buw4YNWvvcuXMTHx+Pp6cnkLRM8K8krnZxcWHJkiW4uLgYlXnz5lGgQAGT/ZRSjB07lkOHDrFs2TIg6UE6Li6OkiVLGuX4ad26Nblz58bJyYn79+/TuXNn3rx5Q+nSpbXzWrhwIWZmZgazXvLkycOFCxf46quvWLduHTExMVy9epXJkydjY2NDhQoVOH78OHPmzMHLywtICpI0atSIyZMna9c5ICCA3bt3p+uatGnThqFDh2rb+/fvp2HDhkYl+Xtwc3Nj3bp1BuPW6XQGubJCQkIYN26cdq1MOXLkCPXr12f27NlYWFiwatUqPD09+fnnn/nmm2/eu0ws5bKu5OCYKdbW1tjZ2bFgwQLy589PgwYNsLOzA5KCLnZ2dlpJGSRKdvPmTdzc3LScXRs3buTKlStp5hU7duwYFy5coFatWri6uuLv78+hQ4fIkCED+/fv5+3bt9SsWRNvb2+tz+LFi9Hr9ZQqVQqdTselS5dQShmUd3M8Va1alfj4eCpUqMDu3btZt26dQdB21qxZvHjxgnXr1hn0i4+PZ8KECWTPnp1MmTLRoUMHQkJCtM/d3Ny0Y1pbWxMaGsqGDRvYsWOHNsuvffv2Jq+XnZ0djo6OODo6Ym1tjY2NjbZtb2+f6jVr2LAhiYmJ1KlTh4iICCIiIihfvjz79+8nIiKC/fv3G90T2bNnZ/78+dy9e5eNGzcycuRIfHx8OHr0KDY2Nnh6elKiRAny5csHQJMmTViwYAEODg6MGDGCJk2aALBgwQLu3btHtmzZiI+Px8/Pj5w5c2q/sdR4eXkZfUdKKZMBqRw5cmj3avLvTa/Xs2LFCsqXL4+bm1uqwVIhhBBCCCGE+Nj+p4NS5ubm2gwlCwsL2rdvT8uWLfH09KRy5cpYWVkRExND0aJFDZbjJS9NSy7t27dnxYoVBnXvzvCwsLBgx44dAPTo0eO9s3EApk6dik6nw97enl69eqVaXFxcDB4aAV6+fEnjxo3ZvXs3R48eJVeuXJw7d47MmTMzc+ZMDh48yPfff0+XLl20pWqHDx8mMjKSTJkyERISwps3b6hduza//vor0dHRdO3aFUB7SIWkB1RbW1uKFy+Op6cn586dw9bWllOnTlGiRAk2b95MREQE69evp0aNGtr4rK2tOX36NGfPnk13kmaAV69e4ejoiJ2dHRUrVtTqixYtSt++fenbty/Hjh2jRYsW9O3blwoVKgCQN29eatasqbUPCgpiy5Yt1KlTR6v75ptv6NmzJyVKlDA6bmxsLDNnzqRFixb88MMPDBs2DD8/P65du0bhwoU5e/YsR48exd3dHT8/v1THv2fPnnQt34Ok3FYrVqygV69eWp1er+fKlSvvvU4VKlTAw8ODVq1acenSJaZPn87YsWPTnJFXs2ZNihcvzrZt28iZMyeXL18mV65cWFhYkClTJnbu3ImDgwMnT54Ekma6LVq0SOtvZmaGq6srNjY2BqVixYpGs7ni4uK4c+cOtWvX5sGDB3zxxRfaZ69evaJu3bpGLyCYOnUqGzdu5Pfff+fy5csEBQWlOjsRYMqUKXz11VfabLHk45qaWVazZs1Ul++VLl3aoO3Tp0+1gGDJkiWZPn061tbW2NvbY29vz7Vr12jcuDH29vZkzJgRZ2dno+OFh4czZ84cRo0aRcGCBbl06ZKWxytPnjzMmTNHa3vv3j0GDhzItm3bcHJyIlOmTKxYsYJt27axcuVKYmNjefPmDffv39cCyB/K48ePtXt14cKFADRq1IgVK1awfft2fvrpp1TvqeRxpSxCCCGEEEII8SH9Ty/fS6aUwsPDg59++slgVoetra1Bu+joaGxsbIwewnQ6HWZmZmnOQgkMDGT9+vVA0tv5xowZo+XTSc3kyZO1ZUeFCxdm8eLFWn4aU5KXZq1evZpRo0ZhY2NDYGBgqvlmli9fzoYNG/Dw8DDKNZQzZ04uXLhAmzZtjPoppbRztbCwQCmFXq/H0tKS+/fvU7FiRcqUKcO1a9cA8PT0NDmDyNramkOHDr136WJKWbNmJSwsjOPHj2tLpPR6PSVKlNCW+nXt2pVGjRqluhTp7du3tGrVCicnJ/r06QPA0aNHOX36tMncXiEhIVSrVg17e3uOHz9OpUqVjNoUKFCAc+fOMXHiRMqVK8eJEye05Vcp2drapmv53tu3b2nXrh1xcXFkzJiRuLg4EhMTtWBmyiWkpt4eqdPpaNiwIdOnT8fV1ZWGDRvi7u7OsWPHiIuLM3lMS0tLVqxYQcOGDbl37x4hISEsWbJEW8b68OFD7t27R6tWrQAYN24c7u7uWjAFknKupQwWpub06dNUqFABOzs7rl+/TnR0tFEwZdasWQAUKlSI+/fvs2LFCubPn0/t2rWBpFlaKd/el5iYaHBd3r59y8yZMylevDj79++nSJEiREZGmpz5dPz4cW3Gz9ixY8mYMaMWiPbx8aFFixZa2zx58hAYGMidO3do164dU6dOpXfv3sydO9dov2XLljV6i+fLly+pVKkSjx8/1vI4+fv7azPhUgZPISngeu3aNXx8fFi0aBH79++ndOnSrFy5ksKFC1OgQAGuX7/O9evXKVu2rNEYoqKitOMopYiOjiYyMtKgTcaMGU0Gs5JnjaW0ZMkSunfvbtT2XbNnz2batGnvbSeEEEIIIYQQf9f/9Eypt2/fsmHDBuLi4li7di0HDx7k1q1bODk5ERoailKK0qVLs337dpRS2NjYmHztvanlezqdTstDpZRi8ODBtGvXDoA5c+Zw+/bt9yYJTg50xcXF8fTpU7Jnz24yiTQkBYeSg1J58+bljz/+oEuXLgwfPtzkUp0mTZqQKVMmPD09tTegvSs5WNS/f38tFw6gBaBMef36NVOmTMHJyUmrSyuBsl6v14JJer3+LwWokoWEhBjk1ElLTEwMLVu2xMfHh507d5IhQwZiYmIYOHAgS5YsMQpEQlKS+f3793P58mWTAalkGTJkYMGCBdy8edNkQCr5+343Afi7EhMT6dy5M2FhYVqg5KuvvsLW1lZ7Q6Otra1W3n1zX3h4uLYk0tXVFaWUtu3g4JDm2wLLly9PtmzZmDhxIt26dcPGxobhw4djY2PD8ePH2bp1q9b2m2++0ZLUQ1IgbM2aNZibm2NhYYGlpSVmZmZYWVmh0+kMlv1t374dNzc3wsPDOXz4MB4eHtqMnIEDBzJu3Dht+/bt24SEhBASEsKXX36p7aNEiRJMmjRJ2z516pR2TWJjY1m1ahXVqlXT3nin1+sJCwszGZRKHnPy7zv5t2cq35tOp8POzs7gXnn16hVbtmyhYsWKWomPj9fezpfS8ePHKVOmjEEg+8KFCxw9ejTV34qZmRkXLlygevXq2sytihUrMn78eKpWrcqOHTs4dOiQyaV0FSpU0GZx3bt3T5vFlbI8efLE5HFNKVq0aLrajR8/nvDwcK28m2BeCCGEEEIIIf6p/9mglF6vp2nTphw6dAhra2u8vLzImTMnU6ZMYciQITg6OnLs2DFevXql5XCBpEDWu280a9euHcuWLTOqT57ldOzYMR48eKA9wJcoUYLjx48THh5uMrjzbrDK19cXpRSVKlUyCEYkl5EjRxq0d3d3p379+gD8/PPPJt/YlvywbGNj897lPmvWrEEpRY8ePbRrl9oMnxIlShiM5+XLl1hYWKT6yvpTp05peZ0CAgJwcXFJcyymnDlzxmiJlSlRUVE0atSIq1evcvjwYa3PhQsXePDgAR07dtTyCAE0bdpUe8tg8hva0sPUzDSlFM+fP8fd3R1LS0utFC5c2KjtixcviImJ4cSJE1oQ6vz58yilCA0N1faXXN6doRcQEEC2bNkICwujbdu2LF68mGPHjrF//37y5cvHw4cPTY7bx8eH33//HX9/f0aMGEFoaCiVKlXi0qVL3Lhxg8OHD9OgQQOtfYkSJbSk/ZA0k9DW1pa2bdvSo0cPZs2aRfXq1dm6dauWHymZpaUlv/zyC82bN+err74iR44c2nK/5GW1ydtWVlbaOaYVyH03p1Sy7777DjMzM9auXUtgYKDBmJOlXL43f/78NJfvpSY4OJgRI0ZoJWVeuZQ2btxIs2bNtO1r165x7tw5smTJos2mTMnDwwOdTke/fv34888/tXGtWbOGJk2a0KJFC7Zs2cKtW7dSnUmZWh6pd6/VX/X69WujWVfJrK2tcXBwMChCCCGEEEII8SH9zy7fs7Cw4NixY7x8+VJb5uXn58fevXt58uQJ9vb2/PLLL8yePdsgoGLqAS55xoOpZVQAdevW5cKFC0ZBjUOHDlG8eHGj9i1btmTp0qXkypULSAoyuLq6msy91LZt2zRnCTVp0oSePXsa1afMWZOa5ABIcu4qgHLlyhETE6PNvHmfM2fOUKJEiVSDCatXr6ZRo0asX7+eiRMnUrRoUcqWLUv9+vUZMmTIe/fv7e3NzZs3DXJDmaKUokOHDty6dYvjx48bBBqqVKlilAeqQIECrFy5End393Sc5ft5eXkRGxtLSEiIwVsPHz9+bJTsPHfu3Bw6dOhvHcfX1xe9Xk/evHlp1qwZ9evXZ8CAAVhaWvLtt9+yevXqVHNShYeH8+eff7J06VIKFy5Mhw4d2LlzJ1OnTuXly5ecP3/eZNAkWUhICM7OzsyfP9+gPuXSt2SLFi2ie/fuVKlShbx583Lt2jXKly+f6r4dHR3Jli0b165d0767mzdv0qhRI+7du5fmNTE3N2fv3r1kzZqVyZMna7+rZHXq1OGPP/7Q3go5fvx4bGxsmDJlCpCUf+zPP/9M8xiQdM88ePBA296wYYPBzLLkMXt5ebF27Vog6Zp17NiRESNG0LJlS5o0aYKbm5vB0tOePXuSPXt2evTowd27d7l48SJDhgzREprnzp2b3LlzkydPnlSDv/+UXq/X/h6kdPjwYS3ZvxBCCCGEEEJ8av+zQSnAKEhUoEABwsPD+f777xk7dizW1tacPHkSd3d3g7eD/R22trYG+W4OHjzIwYMHGTVqlFaXmJjI69evOXLkCJkyZdLqmzRpYjBbK6WnT59qywLf1aRJE1xcXAwelJO1bdvWZP6Z5HEMHjyYhw8fMmjQIL7++muqV6/OmjVrKFiwIKGhodpsotTUrl2bXLly0b17d7p27UrBggX5+uuvDZJ2+/j4sHv3bhYuXEh4eDibNm1i+/btdOzYkR9++EF7O+C8efMM9u3r68vEiRO5f/8+U6dO5dtvvyVDhgza51WqVMHa2pqEhAQiIiKws7Nj27Zt7Nu3jx07dpAjRw6Cg4OBpBk7mTJlMpl/Knv27H9r5ta7EhISmDRpEu3atTMISH0Ma9asoV69evTo0YOMGTOyYMECICnP1oQJEwgPDycyMpJz587h4OCgBWIAqlevTv/+/bXtjh07YmFhQaFChbS8SanNdnnx4gWJiYlkyZKFDh06YGdnR/Hixdm5cyfjxo0zSMIPScsoJ0+eTPPmzXF1daVBgwY8ePAgzdk0w4YNY8qUKRQoUIDs2bMzZswYqlevrn33J06cMPpNx8fHo5QiX758vHz5kpcvX1KkSBGDNr/++qvBdsqZWpCUQyrl7/RdUVFRPHjwAD8/P4PfRXx8PPXq1dO2ExMTGTZsGEOGDMHJyQkfHx9atWpFxYoVmT59OmZmZkycOJEaNWqwdu1a6tatq/W1sbGhQYMG2tK577//XjvXXbt2afm+jh07ZjJAGxkZSVhYWKrnkFJCQoKWgH/06NHcu3ePGzduMHDgQMzMzHj9+rV2PocOHTK5VFUIIYQQQgghPoX/6aBUXFwccXFxWFhYEBISwsGDB/Hw8ODevXvs2bOHL7/8kilTplCyZElmz57N4MGDDR56nz9/jp2dHS9fvvxLb7y6fv06HTt2pGbNmtpMHBcXF5RSODs707t3by3psSnXrl3DysqK+Ph4bty4YZRYet68eYwZM+a94xg/fjyQFMhInk0VHh5ObGwssbGxnDp1iqxZs2rt69evj5WVFStWrKBQoUJafVhYmDaLQqfT8fbtW/bu3cukSZMoXbo048aNo0GDBnTv3h0fHx9+++03APr168fgwYOJiIigcePGDB8+nHr16nH48GHq1q1L1apVKVSokEHOJE9PT9q2bcvs2bNp1aoVkyZNYtGiRcybN0+7ZnFxcRQqVIjo6GgcHBwIDAxk69atKKVo2bKlwTVwc3Pj+PHj771W/8SYMWO4d+8emzdv1uri4uIICwvTgmNp3T/e3t5Gn6fcTj6Hu3fvsnTpUg4ePIiLiwt58+bVlr1ZWVlx+PBhSpUqRa9evRgyZAhBQUEm8/ykdf/MmTOHBQsWMGLECIP6ffv2GbxdMaWYmBji4+O1MT9//px27dqh0+k4dOgQdnZ29OrVi5iYGCIjIwkODjb5xrqxY8cSEhJCq1at0Ov1NGvWzODtf1999RVHjx4F0O4FpRRVq1Zl/vz5nD17Vns74IcQHR1NUFAQ+fPnp1WrVgYzpZRSrF+/XnvjJiQFU2NiYpg0aRJz5szhhx9+YOLEiVrCfoCvv/6arFmz0r59e7788kv279+PhYUF+fLlI2fOnOh0OipXrszkyZOpVKkSx44dY+rUqWzZsoWQkBCaN2/Opk2baN68ucFYUy4XfJ/ff/+d/v37U61aNZydnRk+fDiVKlWiSJEiKKVo2rSpNvMxX758nDhx4p9cRiGEEEIIIYT4+9T/sPnz5ysLCwvVt29ftW7dOlW7dm3l4eGh3r59a9Du4MGDqmPHjiomJsagvm3btgpQGTJkUJcvX37v8aKjoxWgbt68qcqUKaMeP378t8b966+/Knt7e5UxY0Y1cuTIv7WPtBw5ckTp9Xqjei8vL5UhQwaVI0cOg/PduHGjAlSJEiVUYmKiOnXqlCpSpIj65ZdfDPYTGhqqmjZtqoKDg1VERIQaNGiQioqKUmPGjFGTJk1SiYmJWtuFCxeqdevWqcyZMys7Ozt1584dpZRS8fHxysfHx2hscXFxKjIyUr1580aFhoaqkJAQ7Tgfy6JFi1SXLl3SbPPzzz8rBwcHdfr0aYP6sLAwZWNjowDl5uaWav82bdqopUuXqujo6FRLbGysUkqpOXPmqEGDBr133KGhoaps2bJq/PjxWl2+fPnUzZs3lVJKJSYmqvj4+FRLyu8pIiJCAeqnn35Su3fvVqtWrVKdO3dW27ZtUydPnlTPnz9XBQsWVCVLllRv3rxRSik1d+5c1aFDB6Pf2aJFixSgnJ2d1blz5957Hin9/vvvBtfxxx9/VGFhYUoppWbMmKGKFSumXFxcjL4HU0aNGqUmTpz43nahoaGqc+fO6vTp0+rSpUuqUKFCSqmk61ekSBFlbW2t5s+fb9An+dpdvXpVPXnyJNV9R0REqLNnzyqllFq1apVycnJSQ4YMUS9evFBKKbV9+3Y1duxYVbJkSXXt2jWt39atW5W7u7vBd1SsWDHl5eWV6rGsra2Vn5+fwRjj4+NTbR8XF6fde39FeHi4AtTVq37vbSvEX5GQkKACAgJUQkLC5x6K+A+S+0t8LHJviY9J7i/xMX2q+yv5+SE8PDzNdjql3vMKOWGSXq83SlAt/nvevn3LkydPKFGixCc5XmJiovYWRvHpREVF8ejRI0qVKvXB952QkGDy5QLx8fEAqb4JM1lERAS2traf/e/NmzdvyJQpE1ev+lG+fP7POhbx35KYmMirV6/ImjWr/P0TH5zcX+JjkXtLfExyf4mP6VPdX8nPD+Hh4WmmeZGoyt/0uR8QxaeRMWPGTxaQAuQ/Op9JhgwZPkpACv5fjqt3vS8Ylcze3v5DD0kIIYQQQggh/hXkCVgIIYQQQgghhBBCfHISlBJCCCGEEEIIIYQQn5wEpYQQQgghhBBCCCHEJydBKSGEEEIIIYQQQgjxyUlQSgghRLo5OVm/v5EQQgghhBBCpIMEpYQQQqSbo6Pt5x6CEEIIIYQQ4j9CglJCCCGEEEIIIYQQ4pOToJQQQgghhBBCCCGE+OQkKCWEEEIIIYQQQgghPjkJSgkhhBBCCCGEEEKIT06CUkIIIdItLCz6cw9BCCGEEEII8R8hQSkhhBDpFhoa+7mHIIQQQgghhPiPkKCUEEIIIYQQQgghhPjkJCglhBBCCCGEEEIIIT45CUoJIYQQQgghhBBCiE9OglJCCCGEEEIIIYQQ4pOToJQQ4rO6fPkyiYmJn3sYQgghhBBCCCE+MQlKCSGMeHp68vDhw3S1PX36NHq93qDu8uXLjB8/noSEhDT7Pnr0iBo1avDgwQOtbvTo0UydOlXbfvHiRZr7iIyMRKfTvXecXl5e7Nq1673tUjN27FjWrVunbS9fvpyWLVv+7f2ZcuPGDfbu3UtwcDAuLi4A7N27lxcvXrB9+3aGDRv2QY8nhBBCCCGEEJ+TBKWEEAYSEhIYPHgwDx8+ZPz48djY2BgUnU7HDz/8AEBiYiKrVq2iZs2aBAUFafsoX748T58+ZcGCBWkea/LkyXTv3h0/Pz/69+9v8FlgYCBTpkyhWLFiXLt27R+fV+bMmRk0aJAWmHJ0dESn05ksYWFhBn31ej0bNmwgf/78f/m4ERERPHr0SNt+8OABBw4cMNnWzMyMIUOGsGXLFgB+++03+vTpQ1xcHM+ePTMZ5KtduzYdOnQwub8//viDTJkyERMTw9u3bxk2bBg5cuQga9as9OrVi/Dw8L98PkIIIYQQQgjxoUhQSghhwMPDg1y5clG/fn1mz55NTEyMVk6dOoWVlZU2Q8jMzIw1a9bQrFkzRo8eTf369bGxsSFjxoxs3bqViRMnGgS0koMtYWFhbN68mW3bttGsWTMiIyPZuHEjv/zyCwC//vor+fPnx9vbm5MnT1K+fPl/fF5ly5ZlzZo1nDlzBoB9+/bx8OFD4uPjyZYtG76+vvj7+7Nt2zYcHR0N+q5duxYbGxtcXV3/8nEXL17MyJEjte07d+4wYsQIk239/f0ZOHAgQUFBxMfH4+/vz9ixY4mKiuLu3bvkzZvXqE+nTp3Yv38/MTExRp/98ccftG7dGhsbG0aMGMGZM2fYt28fR44c4fbt20aBQCGEEEIIIYT4pJQQQvz/oqOjVd68edX58+dVZGSkGjlypIqLi1NKKRUXF6cqVaqkRo8erbWPiYlRT5480f5dKaWeP3+uChUqpC5evKiUUmrGjBnK1dVVJSQkaP2uXLmicuXKpb744gtVuXJl5e7urpo2baqyZMmihgwZojp16qRevnyplFJq27Zt6qeffjIYZ3x8vIqOjlbR0dEqODhYAdp2comPj0/zXOfMmaN69eqllFIqW7Zs6v79+2rnzp2qTZs2Bu1CQ0NV7ty51aZNm1S+fPkUkGoZNWqUQV9fX1/l4OCgLl26pNUlJCSoL774Qq1fv95oTFWqVFHFixdXNjY2ysLCQlWpUkVVqVJF/fDDD6p06dJGx/Pz81MhISHK0tJS7dq1y2BfERERysbGRh0+fFjFxcUpGxsbdezYMe3zM2fOKHNzc+17e5/w8HAFqKtX/dLVXoj0SkhIUAEBAQZ/I4T4UOT+Eh+L3FviY5L7S3xMn+r+Sn5+CA8PT7OdzJQSQmjmz5+Pq6srVapUYerUqTx69AhLS0uUUvTq1YuwsDBmzpyptT9//jxVqlTh0KFDWFtbA5AzZ07Wr19PixYtGDNmDKtWrWLjxo2Ymf2/PzcVKlTgxIkTrF69mp9//pkZM2YwceJEVq1aha2tLc7OzmTJkgWAI0eOEB0dbTDOqVOnYmtri62trZZ7KXk7uXz99dda+8WLF2tL88aNGwfAwIED+fPPP3nz5o3WztSsrP79++Pv70/p0qW5ceMGoaGhhIaG8tNPP9G4cWNtOzQ01ODaPH36lMaNG/Ptt99SsWJFrd7MzIyVK1cyYsQIjhw5YnCsI0eO4OLiwuzZs8mUKRNVqlShYcOGtGrVilu3brF3717i4+OpU6cOhw4dIn/+/GTOnJmGDRuyfft2g33t3r0bR0dH6tSpg1KK7du3U6lSJe1znU5HQkKC0bUVQgghhBBCiE9FglJCCCBpSd3333/P48ePKVmyJBs3buS3337j9evXNG/enMOHDxMQEEC3bt205ONubm5s376dbt26cfPmTW1fdnZ2ODs7M2/ePGrVqqUFrFKaMWMGHTp0YOjQoQwdOpTGjRsTEhJCnTp18PDwwMzMDJ1Ox+7du+nRo4dB35kzZ6KUQimFn58f1tbW2rZSinHjxuHk5KS1HzRoENHR0VpACtCCPsePHwfgzZs3rF69mnbt2mltRo8ejbe3N87OzgA4ODjg6OiIo6Mjtra2WFpaatuOjo7Y2NgAcPToUapWrUqLFi0YO3Yser3eoFSuXBkPDw/at2/P0KFDiYqKAqBdu3a0bduWjh07AjBv3jwCAgKYMmUKjo6O3L17FwsLC3x9fSlTpow2zs6dO7N7927i4+O1uj///JMOHTpgbm6OlZUVjRo1ws7OTvt86dKlVKhQwWipYrLY2FjevHljUIQQQgghhBDiQ5KglBACSEr8PWzYMJo3b87jx49ZvHgxK1eupHjx4gQGBnL16lXu37+Pra0tJUuWZP/+/QBUq1aNzZs3AzBy5EjKlClD7dq16dixI76+vsTHx1OgQAFq1KjBjBkzUEppx2zTpg0LFy5k4cKFlC1bFoAcOXJw5MgR4uPjtbxKuXLlSnXcjx49Ik+ePAZ1wcHBZM6cWds2NzfHxsYGc3NzAJRS6PV6vvvuO7788kutzcKFCylYsCCJiYkkJibi4ODAkSNHDII56fH06VNmzJhB0aJFsbS0NFkWLFjAlStXtJldkJTnavjw4TRt2pTx48djaWlJnz592LJlC7NmzeL69esEBwej1+vJnj27drzmzZsTFxeHl5cXkBRgO3jwIF26dDE5voULF7JhwwaDmV3vSp6tlVzevcZCCCGEEEII8U9JUEoIoZk1axZeXl4MHjyY5s2bc+vWLebPn8+ZM2fInj07Li4urFq1irlz57JkyRKtX8mSJXn27Bn29vZMmzaNfv36Ubp0aYoVK0bv3r0ZMmQI3bp1o1ixYuh0Oq3fzz//TK1atahVqxYnTpwAYOfOnfz+++9YWFgQHR1N3759teTkpnh6elKtWjWDuqCgIG12kylr167F0tKSOnXqkDt3bl6+fEm5cuXo2bMnlpaWfPPNN5iZmTF58mSj5OLJb+1LfpNf8rJADw8PrU2vXr3o06cPAwcORCnF5cuXyZo1qzaTa8WKFTg7O1OgQAF+/PFH7ZqYmZkRHR2Nj48PAwYMACBv3rwMHDiQLl26cODAAfbu3WuUcD1Dhgy0aNFCW8K3a9cu8ubNa7BcL9lvv/3GN998w4wZM2jUqFGq12j8+PGEh4dr5dmzZ6m2FUIIIYQQQoi/Q4JSQgjNN998w5MnTyhVqhTffPMNDg4OdO/eHWtra4NZPmvXrmXfvn1av40bN7J582amT59Oq1at8PPzIzAwEEiaNfT48WMGDBhA+/btDY63YMEC7c1+rVq10uqDgoKYNWsWJUqU4NGjR2TLls3keF+/fs3KlSvp1q2bQX1AQAC5c+dO9Tx79uxpsNwvW7Zs3L9/X9teuHBhmtfJ19eX6OhoraQcuyknTpygZs2a2nZwcLCWCyslnU5HhgwZiImJwd7eHp1OR44cORg1ahQODg6UK1eOyZMn07lzZ6O+nTt3ZufOnSQmJrJlyxaTbZYsWcKAAQOYMGEC3333XZpjtra2xsHBwaAIIYQQQgghxIckQSkhBJC05GvlypVER0fj6elJyZIlmTBhghaocXV1Zc+ePSilOH36tNYvMTGRZcuW0adPH60uNDQ01UBSSsOHD9cCXSkTde/ZswdfX19WrFjBsWPHyJo1q1HfqKgoOnToQOXKlalXr57BZ35+fhQtWvTvXIZ0sba2xsbGRispk7i/S6/Xs2rVKtq0aaPVBQYGmpzJFRMTQ/78+dm0aROxsbHky5ePU6dOkT9/fgBatGjB8+fPcXd3N+pbv3599Ho9hw4dwtPT0ygotWXLFoYNG8asWbPSXLYnhBBCCCGEEJ+KBKWEEEBSEm9/f3+ePHnChAkTuH//Ph06dHhvv7Vr1+Lk5ISbmxsAL1684NKlS1SuXPm9fRctWqQFvVIGbXr37s369etp3LgxERERFClShMTERO3zgwcPUqFCBaKioti0aROnTp1i//79hISEcPLkSczMzIyW3aW0e/duLQ/W3xEbG6vN8IqJiTEY27vmz5+PjY2Nlrwc4OHDhybzZIWFhdG1a1e+++47ihUrRu7cualRowaQNLtq0aJFZMmShdGjRxv1tbS0pF27dkyYMIFSpUoZBOWioqIYNGgQHTt2pF+/fgQHB2slrbELIYQQQgghxMckQSkhBJA0S2flypV8+eWXVKtWjbdv37J06dI0+/j7+zN27FjmzZsHwJMnT2jcuDF9+vQhR44c6TpubGwsUVFRhIeHY2lpiYWFBS9fviQwMJDAwED27NlDvnz5MDMz49mzZ3zxxRe0bduWLl26cPz4cTJlyoSZmRlTpkwhV65ctGjRItWlafHx8SxevJhvv/2WAgUK/LULlELx4sW1BOW2trbs2LHDqI1er2fq1KksXryYTZs2sX37di5cuMDdu3c5deoU5cuXN+qTMWNGvvjiCywsLChatCh3797lxx9/5PLly1SvXp0aNWrg7e3NkSNH6NOnj8Hb9iBpCZ+3t7fRLCkvLy9CQkL4/fffyZIli0F5+vTp374OQgghhBBCCPFPWHzuAQgh/h1sbGx4+PAhgwcPpmPHjpw9e5aqVatqn8fFxdG6dWuDpWo7duzg66+/5quvvgKge/fuNGnShBkzZqT7uMk5rPLkyYObmxvly5dn/fr1lCpVCgA7OztWrFgBQJ48eZg7dy41atTAyclJ24erqyuXLl1CKWWQSP1dffr0IW/evAwYMAALi7//58/Pz09bUgfQrVs3LC0tte0DBw4wYMAAChQowMmTJylQoAD79+9n+PDhBAcH4+7ubpSEPDg4mOLFi1OhQgWWLl1K3bp1efDgAefPn6d+/fpMmTKF4cOHA3DmzBnatWvH7du3tbcWAtSsWdPg7YbJmjRpYrJeCCGEEEIIIT4nnZInFSHEB/K+oFBKkZGRWFhYYGNj85FH9ekFBwdz/fp16tat+5f6RUVFkSFDBqP6t2/fkjFjxg81vL/lzZs3ZMqUiatX/ShfPv9nHYv4b0lMTOTVq1dkzZo1zfxsQvwdcn+Jj0XuLfExyf0lPqZPdX8lPz+Eh4en+dIkmSklhPhg0huQgqQZUP9VLi4ufzkgBZgMSAGfPSAlhBBCCCGEEB+DhF2FEEIIIYQQQgghxCcnQSkhhBBCCCGEEEII8clJUEoIIYQQQgghhBBCfHISlBJCCCGEEEIIIYQQn5wEpYQQQqSbk5P15x6CEEIIIYQQ4j9CglJCCCHSzdHR9nMPQQghhBBCCPEfIUEpIYQQQgghhBBCCPHJSVBKCCGEEEIIIYQQQnxyEpQSQgghhBBCCCGEEJ+cBKWEEEIIIYQQQgghxCcnQSkhhBDpFhYW/bmHIIQQQgghhPiPkKCUEEKIdAsNjf3cQxBCCCGEEEL8R0hQSgghhBBCCCGEEEJ8chKUEkIIIYQQQgghhBCfnASlhBBCCCGEEEIIIcQnJ0EpIYQQQgghhBBCCPHJSVDq/4DExMTPPYT/8+Lj4z/3EFIVERHBixcvPvcwhBBCCCGEEEL8H/OvCUoFBgZiY2NjVL9w4UL69euXrn34+vri7++frrZRUVF88803KKXS1f7EiRPExcWlq+3HcP78eQoXLpxmm7CwMGrXrs39+/e1upCQEAoVKvRRxjRhwgTu3Lnz3nb58+fn1q1b2nZMTMx7+7x8+ZLg4OB/NL73uXr1Krt27fqoxwBQSpEvXz6Da/CpJCQksHz5cmJjU39j2rFjx3Bzc0On06VZjh8/btDv5cuXHD58+IOO9+bNm/j5+RnUff/997x+/Trd+2jZsiUeHh5ptnn16hU3btww+ZmLiwuPHz82+Zmvr69RXZMmTTh79myqx1JKsXz5csLCwhg6dCiLFy8mIiKCTZs2AVCjRg2jc06NjY1NqmNL6enTp2TPnt2oXqfTERgYmK5jCSGEEEIIIcTH9q8JSqUUGBiIt7c33t7ePH36lJcvX2rbDx48MNknOjqarl278scffxATE0NMTEyaAaft27dz8eJFdDod3333HTVr1tQevhcvXmzQNiEhgZkzZ+Lm5kZ4eDgbNmxI8+H99OnTBv2PHz+eZvsPFXxxdHSkTZs2NGzYkDdv3gBw7tw5dDodHh4eBmX37t1av1KlSlG8eHGtzJ49GxsbG5NjdXR01Prlz5+fmjVr4uvri4WFBXZ2dtjZ2WFpacmGDRtSHae7u7tR0CAqKoqrV6+yfPlyWrZsSaFChVi0aJFBGy8vLypVqoSdnR1lypRh7969Jvd/9uxZzMzM0nx4V0oxcOBAvvvuOxISElJtl1Jq1yR37txp9jtx4gRKKUqUKJGu4+TOnTvN+8XUtX23zYULF4Ck+3zYsGFpBqXOnDlD48aNiY+PT7O4ubkZ9Dt16hQTJ04E4ODBgybHOmzYsHSdc7IFCxawdetWbfvx48dMnz7dZMD6n7h37x7u7u5cuXIFSPrtJI85JCSEAgUKaNvJgaigoCAaN27MgAED0Ov1AMTGxuLl5ZVm4FcpxYMHD6hTpw5xcXG8evUKd3d3Ll++DICPjw8ZM2b8oOeXmJhIWFjYB92nEEIIIYQQQnxo/8qg1OLFi6lVqxYtW7Zk+/bt3Lhxg5YtW9KwYUN69uxp0LZDhw7odDoyZMjAlStXGD16NLa2ttja2moP5qb8/PPPDBs2jICAADZs2ICXlxdKKf744w+jgIm5uTkHDhwgY8aMbN68mc6dOxMdHa2VVq1a8euvv2rbrq6uRserUKGCQZ/kYm5ubtR25cqVRg/31apV4+HDhyYf/F+9eqX1HTp0KN27dycqKgqATZs2kTNnTo4cOaKV9evXM3nyZK3PH3/8wfr162natClPnz6ldu3avH37lvj4eA4ePEiuXLm0wERISIjWr3///owbN46ff/4ZgMjISCIjI+nSpQuQFIxJDo6ltHbtWkaNGsXNmzcBGDNmDI6OjnTv3p1Tp05Rp04dHj16xLRp07Q+Dx48oGnTpnTp0oVbt24xaNAgWrdure0jWXx8PP3793/vDLiffvqJiIgIMmfOzI8//phm25R8fX0Nvr9Dhw6ZbFerVi3t+6lduzaBgYGYm5ub/P5MBdeuXLli8n5p3LhxqmMLDg5mx44d5MiRg1KlSvH69WtGjhxJwYIF+fbbb1Ptt3v3br766isSEhLQ6/UmS2JiIjqdzqCft7c3lStXBqB+/foG43zw4AGZM2emQ4cO6bmsKKUIDw9n165dtG7dWgv6HDt2jFKlSnHjxg3Onz/P+fPnDe53AAsLC4PruWvXLnr16mVQV6tWLYM+NWrUYPr06fTo0QOlFD4+Pnh6euLp6YmDgwMbNmzA09MTHx8fihcvDkCWLFm4fPkyZ8+e5bvvvgOSgnHR0dFkz57d6Hu9fv06AI8ePcLd3Z3SpUsTEBDAy5cvKVy4MPXq1ePZs2fExMSQJUuWdF0ngNDQUIKDg43K35HemaJCCCGEEEII8VGof4FcuXIpQCuurq5qypQpSimlJkyYoF68eKGUUsrT01O5uroa9G3Tpo3q1auXWrNmjUFxdnZW586dM3m8zZs3q4IFCyq9Xq+6deumJk+erH2m1+tVkSJF1ObNm436RUVFmdxfmzZt1Jo1a1I9Py8vL5U7d271448/GhUzMzMVFBSUat/ExETVvHlzVa9ePZUvXz41d+5cNW/ePPX27VujtuHh4So0NFSFhoYqpZS6ceOGcnBwUK9evTJod/DgQVWuXDlt+8SJE8rBwUGZm5ur/fv3a32XLVumatSooapWraqWLVumli1bpp48eaL1CwoKUomJiSoqKkqZm5urCxcuKA8PD9WjRw+1fv165ebmpm7evKny5cunbt68aTCGRYsWqa1btyqllBoyZIiaPXt2qtdAKaWmT5+uvvrqK4O6evXqqYkTJ5psByg/Pz+T+9qzZ4+ytbVVly5dUn5+fsrZ2Vlt3LgxzeMrpZS1tbXRPr28vFSuXLmM2kZFRamIiAh14cIFZW5urnx8fFRERITJotfrDfrmypVLnT17Vj1+/Fire/nypZo5c6Zq1qyZWr9+vdHxAPX69Wv1xRdfqLVr16qwsDDl6uqqGjVqpN68eaOKFSumJkyYoBITEw36eXt7KzMzMxUSEqKsra0Nfocpi5ubm9bn999/N/q8efPm2ueRkZGqWrVqasyYMe+9pslmz55ttE+llGrYsKEqW7asqlu3rqpbt67Knj27WrlypUHf5OubfO83btxYLVmyRNvevHmzwfiT6fV6dfjwYaWUUsuWLTN53j169DDqd+vWLTVt2jSllFL169dXP/74o4qOjtbK1atXlZWVlYqLi1NKKfXbb7+pKlWqKDs7O2VjY6Py58+vqlSpoqpUqaJ+/fXXdB0zWVrfUUp+fn7K2traqD+gAgIClFJKXbt2TZUoUUL5+vqmeryUwsPDFaCuXvVLV3sh0ishIUEFBASohISEzz0U8R8k95f4WOTeEh+T3F/iY/pU91fy80N4eHia7f4VM6WePHmCv78/1tbWxMfHM2jQIIoXL05gYCBz587VlrZkzZqVr776yqi/tbW1tnQsuZiZJZ3a6dOnDZb8+fv7M2TIEEqXLs1vv/3GlStXmDBhgva5ubk569atY/Dgwdrymlu3brF161b27dunzUC6desW3t7e6Tq/4sWLM2PGDFxcXIzKqlWrsLe3N9kvPj6ebt26ERwcrM0aat++PXfv3uWLL75g//79Bu0rVapE7ty5cXJy4vXr13Tq1InIyEiyZs2qzd7YuXMnZmZm2qwXpRSVK1fm2LFjlC1blnXr1hETE8ORI0f44YcfyJUrF/ny5eP48eNMmjRJy8MTFBREzZo1WbFiBba2tkDSkigvL690XZOhQ4fSpk0bbXvNmjU0bNjQqHh6egJJM+Lmz59vsA+dTsfbt2+17Xv37rFs2TLmzZuX6nE3btxIp06d2LhxIzdu3OD06dPs3buXwYMHG+3flJTLupJnQZlia2uLnZ0dM2fOpEGDBpQoUQI7OztevHhB586dDe5VU7PlTp48Sc2aNbl79y6QNHvw8ePH2n1tyvr169Hr9WTNmpXKlSvj6OjIn3/+ib29PSdPnuT06dM0adKEhw8fan0WLlxIjhw5yJw5MzqdjqCgIJRSBmXPnj0Gx+nQoQPx8fE4Oztz48YNpk+fTp48ebTPhw0bRo4cOfjhhx8M+kVGRjJw4EAyZ86Ms7MzAwYMMMgvljxrKSAgAIC7d+9y8uRJjh07ps3y++qrr7C0tDQ6dwcHBxwdHXF0dMTS0pIMGTJo26ktjTM3N6devXokJibSq1cvIiIiiIiIwNnZGR8fHyIiIli2bJnR8s6SJUsyefJkDh8+zKVLl+jfvz9nzpzhypUr2NjYcODAARo0aKCNs2/fvnz77bcUKFCArl270qZNGxITE1m3bh0nTpygcuXKxMfHc/z4capXr86aNWtS/Y4B/Pz8jL4jZWLGU2xsrMG9mvy7iIqK4vvvv6dmzZr07NmTYsWKpXk8IYQQQgghhPhYLD73ACDp4TD5wdzCwoL27duTkJDAli1bqFy5MlZWVsTExFC0aFGDZWfJli9fzvLly43qb926xejRo1m8eDGFCxfmxYsX1K9fn0yZMnH//n0uXrxIuXLlTOar6dOnDwMGDOD06dPs2bOHJUuW8OLFCx49ekT+/PnZunUrkZGRlCtXDkjK4aLX69HpdNq59OzZk7Vr1773/Hv16gXAjz/+yOjRo4Gk5WqdO3fG2tqavXv3Ym5uzo4dO8iVKxdLly7l1KlTdO7cmS5dujB37lzMzMy4e/cuYWFhODk58eLFC5ycnChZsiTz5s3jq6++on79+sD/yz8E8PDhQ4oUKaKN5erVq2zevJkRI0bg5ubG/PnzGTZsGJs2baJGjRpauyxZsnD27Fnc3d21/abH5cuXcXd3Jy4ujsaNG2v5gypUqECbNm0ICwujX79+bNmyBYCiRYsa/DPZnTt38PLyYujQoVrdgAEDmDZtmsmlUG/evGHSpEmsXbuWTZs20axZM0aPHo2NjQ1du3bFy8uLli1bcv78eZYuXZrqcqobN24YXK+TJ0/Su3dvk21PnTrFnj17DHIkRUVFpZpgO6UmTZrg4uJC/fr12blzJ4sXL+bcuXMGAdR3tW7dmgULFuDl5UW+fPk4ePCgQQ4wgEaNGnH16lUKFSrEvXv32LhxI1mzZgXAzMws1fxYKYPBOp2OR48eYWVlRenSpZk3b562jA+Skoi3bNnSaLnfgAEDuHXrFgcPHsTW1pbOnTszfvx4FixYYPKYY8aMoWfPnmTKlEmri4uLw9ra2qhtyqAYoC3hS5YyH9apU6do1KgRADVr1iRbtmxGv9OSJUtq/162bFmDALReryc2NpYZM2Ywf/58HBwc8PT0xMrKCldXV0qVKmUQrDx+/Djz5s1jx44dLFiwgPz58+Pu7s769evZs2cPtra2WFhYcO/ePUqXLm103f4ua2trg7xSlpaWjBkzhrJly1KxYkVOnjxJ+fLlU+0fGxtrkIvM1FJcIYQQQgghhPgn/hUzpZIppfDw8ODLL7/E1taW7t27c/bsWS1HVHJ59+1tK1asMEjIHBsbS548eRg6dCj9+/ena9euQFIulmbNmjFx4kSKFCnCvXv32L17N9HR0RQpUoTLly8THR1NgQIF6N+/P1euXMHW1pbx48fj7++Pg4NDqmPv06cPlpaWlC1bVqtbvXo18fHxREVFYWFhwc2bN9NMJD1q1CgAZs2aRfny5fHz8+P06dNkzpyZTJkyUaFCBSwtLbG0tKROnTosXryYffv2mcxrVKpUKY4fP25yZo1SCguLpHhk4cKFUUpx//59nJ2duXr1Kjlz5tSCE7GxsRw7dszkOTs5OXHx4kUKFiyY1tdqoGLFioSFhbF06VKtTq/XU6lSJdq2bUvTpk0BaNu2LW3btiVfvnxG+0gOenz55Zda+1WrVhEXF0ffvn2N2j948ICiRYty8+ZNLl26RLNmzYzalC9fnqtXr2JtbU2pUqV4/vy5yfFnyJABGxsbrVhZWZls9/LlS7p16wZAxowZiY2NJSYmhri4OJRSWjL+1BKQ63Q6+vTpQ6dOnahSpQpdu3alWLFiWFtbp/oWSEdHRxYsWMDhw4f5448/8PX1ZeDAgbx8+ZLIyEiOHz/OqFGjaNeuHQCDBw+mdevWBvvw9/fXxpayvPuWvePHj1OnTh0Arl+/zu7du7Vg5759++jXr5+27e7uzuvXr/njjz/45ZdfqFy5MqVLl2bp0qUGAb6EhASDa2JmZkanTp0oXbo0QUFBQNJsK1MzCx8/fqz9jlq3bs2qVau07XffsFi9enUCAwPx8PAgOjoaDw8Prl27xpAhQ4zK1q1bjWZEXrlyhUaNGnH06FEt8OXv74+dnR0AzZs3p1q1alr7WrVqcfr0aW7fvs3Zs2dp1KgRDRs2xNvbm2bNmlGkSBH8/Py4fv26wd+PZG/fvtXytUFSYDN5O2W9KSnv1eRg+b59+/Dy8kozIAUwe/ZsMmXKpJV3A39CCCGEEEII8U/9K4JSb9++ZcOGDcTFxbF27VoOHjzIrVu3cHJyIjQ0FKUUpUuXZvv27SilsLGxITAwkI0bN/LgwQMt+fhXX31FwYIFyZgxI8+ePaNXr17MnTtXO07JkiX54YcftECNnZ0dFhYW2v6KFi2KjY0NUVFRf/ltWGvWrEEpxa1bt7Q6MzMzLCwsePz4MXq9nmzZsplMIq3T6bRkzQBFihTBy8sLNzc3FixYYHKpTsmSJcmVKxdXr17VZn28K/khtEGDBtja2nLq1CkgKQhkaglU8ncxa9YsrW9iYmKa5/38+XOGDBmi7dfUUrT3CQkJIUeOHOlq+/r1a+rVq0dMTAxbt27VEr1PmDCB5cuXm5xlUrhwYW0ZWMogyLucnZ3ZsGEDly5dIleuXEafJ1/7lN+dqTf3RUdH06xZM7Jmzaolyc6XLx+2trZUqVKFp0+fagFWU29tCw8P1+6/WrVqER8fr207ODgQERGR6jk0b94cPz8/Jk2axOTJkwkLC2PatGnY2Niwa9cubTkkwMyZMxkzZoy2rdPpmDJlChYWFpibm2NlZYVOp8Pa2hqdTmcw62b79u24ublx9+5dHj9+zK5du7Qk540aNWLZsmXa9oEDB3j06BEJCQl8+eWX2j5q1qzJ4MGDte0NGzZga2tL/vz5Adi5cyc1atTQgnIAYWFhJoNS5ubmWFhYaL+j5N+ehYWFUWDW3NwcOzs7gxmSDx48wMvLi4oVK2olICBA+82kdPz4ccqUKWMQkLx48aJR4C4lMzMzLly4QL169bTvvHLlyowdO5aqVauyY8cODh06ZPIlCc7Oztjb22Nvb09sbCwlS5bUtpPLX/HurMPUjB8/nvDwcK08e/bsLx1HCCGEEEIIId7nswel9Ho9TZs25dChQ1hbW+Pl5UXOnDmZMmUKQ4YMwdHRkWPHjvHq1SuaNGmi9bO0tGTSpElkyJCBPHnyULp0aYYNG8bIkSPJlCkTOp2OHj16pGsMAQEBBg93YWFhZM6cOdX2pvK3pOXOnTtAUk6sd2d92drasnDhQoP27du3p2LFigCMHDnS5BvbfHx8ALR8TmlJfrNg8hImvV5vcgkUJL2VLOUbDp8/f57mQ+/hw4e1wFVAQAAuLi7vHU9KiYmJnDt3jtKlS7+37atXr3BzcyM8PJxjx45pgaMDBw4QHBxMzZo1cXR0pEyZMgCUKVOGOXPmAOl/EAfImzevyWPHxcVRpEgRbbaapaUl7u7uRm3v3r1LtmzZ8PT01IJJgYGBKKW4du0a+fLl0wJc/v7+Bn0jIiJ4+/YtWbNm5cmTJ/Tu3Zu1a9eyePFibty4Qb58+QxyQqV06dIlVq9ejVKKyZMn4+npSZ8+fVi7di3BwcEcPnyYBg0aaO2rVq1qcB/ExMRga2vLqFGjqFWrFhs2bCBXrlxcvHjR6Fg2Njba8rqOHTsazMhJDgglb1taWmoz89L67bybUyrZL7/8wvXr1zl69CiBgYEml1bmyZNH+21s27bN4O17pmbGmXLv3j1GjBihlXdztiXbuHGjwT537dpFVFQUAQEBnDhxwqj91KlT0el0zJo1i7lz52rjevjwIaVLl6ZVq1YsXLgQa2tr7d59V2p5pN69Vn9VYGBgqjPvrK2tcXBwMChCCCGEEEII8SF99qCUhYUFx44dY/369Vqdn58fe/fu5eDBg8ydO5fu3bsze/Zsg5kJzs7OPHr0iLNnz7J27Vq6devGgQMHmDZtGj/99JPJwEJqtmzZQtu2bYGkmTixsbE4OzubbLt161aD/DTp8erVKzp27GjyofLLL79Mc5ZQz5492bJli1FJz1Ka0NBQAGrXro1Op9MemGNiYtI9E+zMmTOULl3a5IwgSFqi2KhRI54/f86pU6coUKAArVq10q7n++zZswcrKytKlSqVZruYmBgaN25MdHQ0p0+fNphh1KZNGx4+fIi3tzfe3t5aMGH//v0MHDgwXeN4n71795IlSxb0er3B92cqsXu5cuXYs2ePQS6k9Dpz5gwlSpQgJiaGJk2aMGrUKLp3787IkSMZN24chQsX1hLwv+vZs2ds2bKFbdu2kTVrVpo2bcqJEycYP348586dIyoqyuRMHEgKxFpbW2NjY8PcuXM5duwYHTp04Pnz51retJR27tzJqlWrOH/+POfPn8fPzy/N8ypUqBCWlpZcu3ZNq9u7d+97l5AB2Nvbc/r0aapUqcKLFy+MZrE1adKEp0+fajOzWrVqZTBT6+bNm9pSyrS4ubkRFhamlYkTJxq12bdvH69fv9aCkQ8fPqRfv35MnTqVmTNn0rdvX+13l2zq1KksW7aMMmXKEBMTw4oVK6hbty4eHh5A0nLCmJiYv5Sb7a+KiYnh9evXRvUeHh7aTEchhBBCCCGE+NQ+e1AKMFpyVaBAAcLDw2ncuDFjx44lODiYkydPGi0fSQ4KtGvXjhIlSmBjY4Ovry/du3dP97EjIyNZsWKFlovo0aNHZMuWzShXUHR0NPHx8ezcudNgSWB6DBw4kN9//93kZ0+fPjW5hAvQ8gg9ePDAqAwePNhk4C05eNSsWTPatm1LkyZN8Pb2Jjo6WntLWmhoqFEC7Hd16dKFvn37Mnv2bFxdXSlatCi9evWiXr16WpuDBw/i5+dHw4YNefToESdPnqRIkSJ069YNLy8vjh8/bjLYdOHCBX788UdOnDjB2rVrGTdunPaZlZUVVapUAZJmdL1+/RqlFD///DO3bt1iw4YN2NjYEBwcTHBwMJGRkdjZ2ZE/f36tJOfDyp0793vPMz0iIyP5/vvv6du3799anvhXrFmzhvr169OoUSNq1qyp5RkbNmwYx44dI3fu3Fy8eBFfX1+ePn1q0Ld169YcOnRIC5h8/fXXDBgwgHHjxrFt2zZGjx6d6vhv376t3Ydjx46lbt26bNmyhVy5cnHz5k2j9qGhocyaNYvBgwdTp04dGjZsmGrgEpICSz169GDEiBFcvXqVmzdvMmXKFIP7ae3ateh0OoMgbVRUFEopChcujI+PDzly5DCaubdr1y7y5MmT6kytUqVK0adPn1TH9vr1a/z9/Tlx4oT2xj5HR0dmzZpl0O7t27eMGjWK7777DnNzc06dOoWrqyu9evWib9++tGrVimbNmlGjRg2uXr1q0Dd79uwULlyYfPnyMWbMGIN7ftGiRUBSgCjl8t+U3rx5YxAwSy6mko/HxcXh6+uLXq+nd+/elC1bFnt7e9atW4dOp9OCU3q9niNHjpgMOgohhBBCCCHEJ6H+BWJjY9WTJ09UxowZVXBwsNqwYYNyd3dXefPmVXv27FEvXrxQ/fr1U/b29mrx4sUqMTFRjR07VuXOnVtly5ZNjRs3Tvn7+yullLp9+7a6e/eucnZ2VhcvXjR5vDVr1qgWLVqo6Oho1b59e/XDDz+oEydOKH9/f7V06VJVr149k/0ePHigTp06pYA0S48ePdI837NnzypfX191+PBhZW1trd6+fWvw+ZAhQ957jJTFy8tL63vnzh0FqA4dOqiIiAiD/fr4+KiAgADVrVs3NXXqVK0+ODhYnThxQrm4uCillAoJCVHLli1TLi4uasiQIUoppQ4dOqRy5cqlJk2apJRSKiIiQhUsWFAtWbJEXb58WWXLlk399ttvSiml9uzZo7JkyaKePXumYmJiVO7cudXNmzeVUkqtXr1aOTk5qa1bt6q7d++qdu3aqUyZMilHR0eVM2dOlTNnTpU5c2ZlbW2tAFWkSBGllFIVK1ZM97X28/NTgPLz80vzexg1apSaOHFimm3i4+NVu3btVJEiRdSbN2+0+qioKPXq1Su1bds2lTt37lT7f/nll2rAgAHpul9OnjyprKys1OPHj9X169dVQkKCwb6uXbumlFKqRYsWqnz58qpGjRraZ4D2fb/v/tmxY4fW7+bNmypXrlxqwoQJatCgQWrixIlq7Nixqm7dumrLli0qR44c6ty5cwpQYWFhSqmk+6hYsWKqVatWKj4+XimlVFBQkHrx4oV6/vy5ql69ulq9erXRtYiIiFA9evRQ9vb2ysXFRQ0bNkzFxsYqpZSaPXu26t69u4qPj1f+/v4q+U/TvXv3VK1atdSlS5dU//79Vffu3dP8vpRSqk2bNtq9mJY///xTZc6cWTk4OKi+ffuqunXrKqWUSkxMVEopNW3aNDVixAit/dGjR1X9+vWVXq9XQ4cOVS4uLmrdunVG+12wYIGyt7c3GKuPj4/q37+/ypEjh3J3d1fFihVTDx8+VGPHjlUuLi7q2rVras6cOcrFxUWdO3fOYH/Jv4W0SkozZsxQ9vb2qlatWmrMmDHqzz//VI8fP1ZKJd07KfuVLVtW+17fJzw8XAHq6lW/dLUXIr0SEhJUQECA0d88IT4Eub/ExyL3lviY5P4SH9Onur+Snx/Cw8PTbPevCErNnz9fWVhYqL59+6p169ap2rVrKw8PD6NgzcGDB1XHjh1VTEyM2rVrlzpw4IDS6/UGbSpVqqQ9bEVGRpo83po1a1TTpk1Vq1attIfXJUuWKGdnZ+Xk5KT279+f6lgTExNVdHR0miUuLi7N8500aZLKkCGDcnR0VPPnz0/PJfpLDh06ZLJ+3bp1ytraWhUuXFh7SFVKqVmzZilANWrUSCml1KZNm1SZMmXUhg0bDPo/ffpUNW3aVMXGxqqAgAD19ddfq4SEBNWpUye1fPlyg7bjx49XmzZtUlZWVipr1qwqJCREKZUUmHj48KHR2GJjY1VkZKQKDw9XoaGhKjg4WAUHB6uoqKh/dC3Skp6g1MiRI1XOnDmVr6+vQb2vr68ClE6nSzNQ8uWXX6p9+/al634ZNGiQmjNnznvH/ejRI5U/f36Da54yKJWQkKDi4+NTLclBF6X+X1Bq7Nix6vLly2ry5MlqwoQJauXKlermzZvq9u3bytnZWdWsWVPrN2TIEDVixAij396oUaMUoPLkyWPyO07L7NmzteBcZGSkmj17tlIq6b745ptvVL58+VTevHnV3bt337uv9AalHj16pLp27apu3LihtmzZogWlIiMjlaOjo7K1tVV//vmnQZ/ka3Dq1CkVHByc6r6Dg4PV1atXlVJJv/ccOXKoCRMmaAGg33//XfXo0UO5urqqR48eaf1++eUX1bVrV4N9WVtbpxpgDQgIMApK6fX6NP8DExsbq6Kjo1VMTEyqbUyRoJT4WOR/vMXHJPeX+Fjk3hIfk9xf4mP6twWldEr9xazdQvwf8vLlS2JjY/9SjrK/Syll8u2B/zb/K+P8K0JDQwkKCvpLCfHTKzY2VnujYUrR0dHY2Ni891qGh4djb29v9BbBT+3NmzdkypSJq1f9KF8+/2cdi/hvSUxM5NWrV2TNmvWz3+fiv0fuL/GxyL0lPia5v8TH9Knur+Tnh/Dw8DRfmmTx0UYgxH9AtmzZPtmx/lcCPf8r4/wrnJyccHJy+ij7Tu1Nl+l5cybwtxLmCyGEEEIIIcT/Agm7CiGEEEIIIYQQQohPToJSQgghhBBCCCGEEOKTk6CUEEIIIYQQQgghhPjkJCglhBBCCCGEEEIIIT45CUoJIYRINycn04nbhRBCCCGEEOKvkqCUEEKIdHN0TN9bA4UQQgghhBDifSQoJYQQQgghhBBCCCE+OQlKCSGEEEIIIYQQQohPToJSQgghhBBCCCGEEOKTk6CUEEIIIYQQQgghhPjkJCglhBAi3cLCoj/3EIQQQgghhBD/ERKUEkIIkW6hobGfewhCCCGEEEKI/wgJSgkhhBBCCCGEEEKIT06CUkIIIYQQQgghhBDik5OglBBCCCGEEEIIIYT45CQoJYQQQgghhBBCCCE+OQlKCSH+lUJDQz/3EIQQQgghhBBCfEQSlBLiXyImJua9bcLDw/H39/9Hx/nll18ICQn5R/swJSgoiN69e/P48WOt7tixYwwePPgv72vnzp0UK1YMvV7/AUf44TVt2pQNGzZ87mFo4uLiWLJkCbGxsTRt2pS9e/fy4sUL9u7dC0DBggXTdZ8JIYQQQgghxKcgQSkh/iV69+7N9OnTDepiY2O5desWHh4edO7cmbx58zJ58mSjvq9fvyZr1qwGASFT9u7dy/Dhwzl69Gi6xjRnzhx0Op3JsnLlSoO2WbJkIV++fNSoUYPw8HCt/7Jly4z67t69O9Vj3rhxg/79+5M5c2ZGjRqVaruGDRumOrZ3S/78+Q36Pn78OM32bdu2Tdf1+Sv+6vHy58+f7vO7desWAEopTp8+TZs2bQC4d+8etWrV4tGjR4SHhxMYGIiNjc0HPzchhBBCCCGE+DskKCXEv8Qvv/zCqlWrOHToEACLFy8mY8aMtG7dmn379lG2bFlu3LjB6tWrDfqFhYXRsmVLgoKC0tx/SEgIw4YNo1+/fowePZrg4OB0jatr165ER0cblJo1a5psO2XKFH799VcyZcrEggULePToEW/evGH+/PkEBATw5s0bMmfOTJEiRUz2v3DhAnXq1GHq1KmcOnWKAwcOMHHiRJRSRm23bdtGaGioQdm9ezfW1tZcu3bNoP7GjRtG/a2trY3OKzo6mmXLlpkcW8+ePY2CQfv27aNbt25G9R4eHib3ERwcbHS8X3/91WTbGzduGJ1f1qxZOXDggFF9iRIlgKQgVOfOncmePTtBQUEEBgbi6urKF198wd27d8mbN6/JYwkhhBBCCCHE52DxuQcghEji4uLCTz/9xP3792nQoAExMTH07duX5cuXp9rn9OnT9OjRAwcHhzT3HRcXR6tWrShfvjy//vorI0eOpHHjxhw9ehR7e/s0+5qbmxvNrjEzSz2efffuXQIDA5k5cyaenp7cuXOHUaNGUaJECXLmzEmDBg344osvDPokJCQwd+5cZs2axS+//ELv3r2182vWrBn37t1j6dKlZMmSReuTMWNGg31ERUUxcuRI+vTpQ7ly5dI8p2SmZg1ZWJj+s+jh4aEFm/bu3Uu2bNmoVKkSUVFRzJw5k/r161OrVq00j5crVy6juoSEBFq0aGFUb+o7jYmJwdnZGUdHR5P737lzJ/v27ePq1auYm5sTHh6Oo6MjM2fOpEWLFty9exedTqe1nzJlClOnTk1zzEIIIYQQQgjxschMKSH+Rdq0acPQoUO17f3799OwYUOjkpzH6PDhw/Tq1YstW7akus+QkBAaNGgAwMqVK2natClz586lQIEC1KhR471L/tauXWs0E+jEiRMm2964cYOpU6fSpEkTzp8/j62tLe3ataNDhw706dOH6OhoNm3aZNBn27ZtlCpVivXr1+Po6Ej//v2xsLDAwsKCnDlzcvnyZfz9/SlRogTTp08nICDA6LgJCQl06tQJPz8/nJyc+O2337h9+3aa5wVJs8zeLdHR0Wn22bNnDz169CAxMREAKysr8ufPT5s2bRg2bFiaebCCg4OJiYkxKL/99tt7xwlJgcU3b95gZ2eXaptJkybRqVMn3NzcqFu3Lm3btsXS0pItW7awfft22rdvT3x8vLYcVAJSQgghhBBCiM9JglJC/Au8evUKR0dH7OzsqFixolZftGhR+vbtS9++fTl27BgtWrSgb9++VKhQAYDp06fz3XffpTq7x8vLi0qVKuHg4MC+ffuwsrJi37596HQ6Nm3aRJ06dahUqVKaQa327duna/leSEgIbdq0YdKkSTx//pwFCxZQoUIFOnbsyO+//87y5ctp1KgRvXv35ty5c9qSvD179tCpUye8vb158uQJMTEx7N+/n4oVKxITE0NsbCxnz55l586d3Lp1i/j4eIPjRkdH061bNwIDA2nbti2JiYmEhYVRo0YN5s2bl+p5xcbG4uTkZFS+/vprk+2DgoIYNGgQffr0YcyYMdosKwsLC6ytrdm4cSMnT56kW7duqR7zyy+/pHjx4gZl/PjxJtvOmzfPIBBobW0NQIkSJQzqq1atqvVZu3Ytnp6eWtCyatWq9OrVi0WLFnH79m18fX2xsLDg3r17lC5dOtVxJl+fN2/eGBQhhBBCCCGE+JAkKCXEv0DWrFkJCwvT3pIGoNfrKVGiBG3btqVt27aYmZnRqFEj2rZtq+UQSsuoUaNo1aoVQ4cOZefOnUbL9MzNzVmwYAG//vorI0aMYMKECSb3Y2VlhY2NjUExtXzvxIkTlClThmHDhjF06FB27drF119/TenSpdm4cSPh4eF8//33mJmZ0bJlS/z8/ICkZXGTJ0/GysoKc3NzLCwstP0nz5jS6XS4urry559/GuRF8vHxwdXVlVevXuHp6YmtrS1mZmaMGTOGc+fO8dtvv9G/f39tVlOy/Pnzo5TSyoABA5gyZYq2vXXrVqPzO3z4MBEREVy/fh1bW1tGjhwJQGRkJN988w1Zs2bF09OTx48f8/LlS6P+N2/eJH/+/BQtWpStW7dqxdPTkwULFpj8/uLj47Xy448/UqxYMaysrHjx4oVWf+bMGa1P9+7d2bt3L3/++Sd6vZ6KFSvSo0cP9uzZw+DBg3n79i0xMTFcv36dsmXLmvy+k82ePZtMmTJpJU+ePGm2F0IIIYQQQoi/SnJKCfEvFRISQo4cOf52/wkTJjBhwgScnZ3TbNeqVSvq1q1rFLiBpLe56XQ6oyVpphKPt27dmlatWhEeHs65c+fo0KEDT5484cmTJ0DSbLDXr1/j7e1NYmKiQWArMDDQ5LmmzH80fPhwFi5cqG1PnDiRBQsW8O233zJp0iTMzc0N+hYrVoxTp05Rq1Ythg8fzqJFiww+79+/P9WrV6dnz55AUhAwJiZG+9zc3BxLS0ttu0uXLnTp0gWAfv368cMPP7Bt2zaOHTtG9erVtdlr586dMzqPV69eMW3aNA4fPgwkzQ57V6dOnQyWNup0Om0GnFKKDRs20KdPH/bt28eOHTsYPHhwqtfrwoULtGjRguzZswNQo0YNvv76a+7evcuOHTs4c+YMa9euNeqf0vjx4/nmm2+07Tdv3khgSgghhBBCCPFByUwpIf6lzpw5894lVmlxdnZ+b0AqmYODg8nk2c+ePWP9+vVYWloalJMnT5rcT8og0ubNm5k5cyZPnz5l8+bNTJ48Wfvs3ZlW2bNn5/Dhw0yfPh2lFJ6enlSpUgWlFK9evaJz585GS/Hq1KnDxYsXmTp1qlFAKlny2+rc3NyMPrty5Qpv377VtmfNmoWtra1WOnXqZHKfABkyZGDt2rX07duXzZs3p5qMPj4+nuHDh1O4cGGyZctGWFgY1atXZ8qUKURGRpIjRw7Onj3LxIkTKVSoUKrHW7hwIUFBQQwcOJBBgwYxbdo0k29PTH5D4MaNGxk8eLC2xM/FxYWsWbPSqlUrxo4di6ur63vvDWtraxwcHAyKEEIIIYQQQnxIEpQS4l/I29ubmzdvUqdOnc82hoSEBA4cOMDPP/9ssNRNKWUyyGPKvXv3Us139a5q1aqxbds2gwDP27dv6dSpE5kzZzbaT926dSlVqtR795svXz7atm1rUBcQEIC3t7fBUsCUy/dSW8KXko+PD7GxsSilUg3SWVpaUrx4cY4dO8aPP/5I79690el0TJgwgZ9++olChQpRrVo17t+/T8GCBU3uY8uWLUyaNIkNGzZgb29Phw4dqFChAh06dCAyMtKgrYeHB2PHjqVRo0bo9XrGjx9Pnz59tITmTZs25cWLF9SvX/99l00IIYQQQgghPjoJSgnxL+Hr68vEiRO5f/8+U6dO5dtvvyVDhgza51WqVMHa2pqEhATCwsLSfMvbh/Dbb7/x+vXrNGcMpUUpxerVq3Fzc+P69esml/ylZGdnx5IlS3j+/LlWt2vXLnLmzMn8+fP/1hhSM3bsWEqVKsXw4cPp2LEjt2/fJjIyksjISOLi4khISCAhIQG9Xk9CQoLWLzExkQMHDuDq6srixYs5ceIEW7du5dtvv6VGjRrs3LnT6HsZOHAg/v7+lC5dmrdv33Lw4EEOHDjAjz/+yKpVqwC4evUqX3zxhUG/0NBQRo0aRe/evdm6dSu1a9fWPvv999+Jjo6mVq1a+Pr6GvQrVqwYCQkJ5M+fX1vemGzatGm4uLgwb948g+sshBBCCCGEEJ+DBKWE+BdIXq7WpUsXfH19cXFxYdGiRWTKlIlcuXKRK1cubt++TaFChbCwsCBfvnxGb6H7kM6fP88333zDDz/8QJYsWbT6oKAgXr9+TXR0tMFSvWRxcXFa4KVBgwY8fPiQGjVq0L17dzp16kTlypW5d++eQZ/Tp09ry8xq1KjBzJkzqVevHhcuXKBLly6sX78ea2trdDodtWrV+kfnFRMTQ9euXfH09GTPnj3cvn2bxo0b4+LiwtatW8mSJQvW1tZagvVs2bLx9OlT9Ho9/fr1I3fu3AwcOJA2bdrg4+NDpUqVqFOnDnfv3qVx48YMGjSI7NmzM3z4cCApCXrJkiUZMWIE48eP58CBA6xbt47u3buzcOFCQkJCuHr1Kk+fPjVIPH727FkKFCjA6dOnOXnyJA0bNjQ4D0dHRzw9Pfnyyy8pV66cFtxSSlG6dGkyZcqEpaUl5cuXp2fPnjx//pxevXqxY8cOLl68SJMmTXBzczP6LoQQQgghhBDiU9Kp901fEEJ8dHq9nnv37hm9VS8+Pp64uDgSExNJSEggMTERpRTW1tbY2dn95eNERkZib29PfHx8qsvqXrx4QdGiRRk4cKBRHqdOnTqxefNmHB0d8fLyoly5cgb9ChQoQMaMGfnyyy9p27YtPXv2xNraGoDHjx/z22+/sXz5cm7evEnOnDkNzj89dDpdqvmjICmnUu7cuZk5c2aqbRYuXEi7du3IlStXuo6Z7Pfff8fFxYU6deqkOob4+HiOHTuGjY2NtsTx9u3bFCtWDHNzc7799lt27NjBpk2byJIlC82bN+f+/fuMGDGC2bNnG+zLy8uLWrVqmQz+peTt7U2ePHlwdname/funDlzhgEDBvD1119jaWnJn3/+ycqVK7Gzs2PVqlW4uLiglGL8+PGYmZnx/fffp+v837x5Q6ZMmbh61Y/y5fOnq48Q6ZGYmMirV6/ImjWryTd7CvFPyP0lPha5t8THJPeX+Jg+1f2V/PwQHh6eZn5aCUoJIYxcvHiRypUr/+V+8fHxBm+sM+Xt27dkzJjx7w7tf1poaChWVlYf7fyjo6OxtbU1qv8Q11yCUuJjkf/xFh+T3F/iY5F7S3xMcn+Jj+nfFpRKXwZiIcT/KX8nIAW8NyAF/J8NSAE4OTl91P2bCkjB/+1rLoQQQgghhPj3krCrEEIIIYQQQgghhPjkJCglhBBCCCGEEEIIIT45CUoJIYQQQgghhBBCiE9OglJCCCGEEEIIIYQQ4pOToJQQQoh0c3Ky/txDEEIIIYQQQvxHSFBKCCFEujk6mn7DnxBCCCGEEEL8VRKUEkIIIYQQQgghhBCfnASlhBBCCCGEEEIIIcQnl+6gVGBgINu2bUv18zNnztC/f3/Wrl37QQYmhBBCCCGEEEIIIf670h2U8vHxYeTIkUb1p06donLlytSuXZtXr15hb2//QQcohBBCCCGEEEIIIf57/tHyvZ9//pmGDRvi5ubG06dP2blzJ61bt/5QYxNCCPEvExYW/bmHIIQQQgghhPiPsPgnnWvUqIG3tzdFihT5UOMRQgjxLxYaGkv+/J97FEIIIYQQQoj/gr8clPLx8aFp06bodLpU2+h0OmrXrs3KlSv/0eCEEEIIIYQQQgghxH/Te4NSZ86coXjx4tp23rx5WbRoUZpBqUePHjFixAgJSgkhhBBCCCGEEEIIk9IMSr1584ZWrVoREhJCnjx5iI2NBaBp06YA/Pnnnxw8eJBRo0ZRsmRJrd/p06dRSn3EYQshhBBCCCGEEEKI/2VpJjp3cHDg5cuXnD9/nk6dOmFra0vu3LkZN24cUVFRFCxYkODgYMqVK0fv3r0JCwsDoGDBgvj5+X2K8QshhBBCCCGEEEKI/0HvffueTqejUqVKzJ49m0ePHjFq1Cg2bNhA7dq1qVixIj/99BOenp6cPn2a0qVLc/XqVXLmzEm+fPk+xfiF+KQSExM/9xA+OaUUer3+kxzrc13fT3V+QgghhBBCCCH+nzSDUs+ePaNq1arasj0zMzOmTZvGzp07WbRoEV9//TUlSpQgMTGRy5cvU6xYMZYsWfJJBi4+vMDAQGxsbIzqFy5cSL9+/f7WPt+8eUNgYKBRvV6vR6fTERwc/Lf2+08EBgZiYZF2OjW9Xk/Dhg25cOGCVqeUIk+ePLx9+zbVfkop4uLi3rvvu3fv/rVBm7B161Zu3rz5j/fzPjdv3qRgwYLpDhjt37+fO3fu/K1jde/ena1bt/6lPt999x1Dhw79y8fKnTs3t27dAuDAgQO0b9/+L++jZcuWeHh4pNnm1atX3Lhxw+RnLi4uPH782ORnvr6+RnVNmjTh7NmzqR5LKcXy5csJCwtj6NChLF68mIiICDZt2gQkvTFVZrEKIYQQQggh/i3SDErlzp2bt2/fsmLFCq1OKYWtrS3Dhw/n7NmznD9/njp16uDg4MDevXv59ddfP/qgxccXGBiIt7c33t7ePH36lJcvX2rbDx48ACA6OprXr18TFBSEv78/3t7eHD58mBUrVvD1119To0YNChYsmK57wsPDA51Op5Xs2bObbPf48WODdu+Wy5cvf5Dzt7CwYNCgQTRt2pRnz54BSW+ejIiIYMuWLXh4eGgl+YEf4LfffqNFixYGgamEhAQeP37M9u3bGTZsGPnz56dz5868efPG4JhLliwhf/78ODg40L59e16/fp3q+AICAujduzfLli1L1/mcP38+1WvWtWvXNPtu376datWqYWb23omVACxevBhPT09t29QxTQVc/vjjD/bs2UP16tVZvny5yX6jR49+7/Hz589v0Gf58uXv7XPv3j2io6O5deuWQfkQwcN79+7h7u7OlStXAHB0dNTGFhISQoECBYyuS1BQEI0bN2bAgAHaLK7Y2Fi8vLwoVKhQqsdSSvHgwQPq1KlDXFwcr169wt3dXftd+Pj4kDFjxn98TkIIIYQQQgjxIaQ5XUSn09GvXz/Gjx/PvHnztLp69eoRFBSEi4sLLVu2TLX/06dPP+hgxaezePFiFi9ejKOjo1bXsmVLYmJiKFy4MKdPn+bMmTM0bdoUGxsb7OzscHR05OXLl5QtW5ZWrVrRsWNHKlWqhKWlZbqO2bx5c7Zt28bdu3epW7duqu2cnZ3x9/c3qk+ZbD/ZkSNHqFevnsn9mHqD5IULF6hcuTIALVq0wM/Pj+joaAA2bdpEwYIFOXLkiNb+zZs3nDx5ks6dOwPQt29f9uzZw5QpU5g9ezbXr1+nYsWKZM2albJly1KuXDm2b9+uHSPZli1b+Pbbb9m4cSMlSpSgV69eDB48mM2bNxuNUSnFoEGDqFevHtu3b6dLly64urqmdrk0efPmNQqyzJw50+RMHVPX5s8//zS534iICOzs7ACIj4/n/PnzWhBu+PDhAPj7++Ps7AxAsWLFjPZx+fJlBg4cyMKFC8mZMycDBgygb9++Ru3SGxi7fPkyZcuWpUuXLulqf/LkSY4dO0aNGjW0usTERBwdHQ3+jllYWJCQkGDQd9euXfTq1UvbdnNz4/jx49p2jRo1mD59Oj169ODmzZv4+PhoM8natGnD0qVLyZYtGzlz5tTedJolSxYuX76Mm5sb3333HXPmzOHgwYNER0ebDNh6e3tTtmxZHj16hLu7O0FBQQQEBKDT6ShcuDD16tXj2bNnxMTEkCVLlnRdEyGEEEIIIYT42NJew0TSQ9OIESP47rvvyJkzJ82bN6du3bocPXqUwMBA3N3d6dGjh/ZQKv435c6dm+fPnwNJAQlXV1dGjBjB1KlTmThxIkOHDiVHjhwcOXKEqVOnAuDu7k5MTIzBfjp27Ii7u7vJgAIkBSfy5MmjbSc/IK9ZswadToeFhQXm5uZpjjU6OprFixcb1YeHhxvVubu7G7wJctiwYdy9exdPT0/Wrl3LgwcPGDFiBJkzZzboFxkZiV6vp2fPnjg4OBAYGMiyZcs4deoUpUqV0trdvXuXqlWrattmZmYsXryYKVOmAElBmkKFCpmcGZTSnDlzmDBhghbknTNnDv379ychIcHoeowbN47r169z/fp1jh49SsuWLTl+/LjJoFxKOp3OaHlmassYIyIigKSZX4sWLeL69esmA1WAwW9/x44dODg4cOjQIXQ6HU5OTgD069cPKysrIGkWUEpnz56lUaNGtG3bll69enHnzh2++OKL9y6x7Nu3L6tWrdK2lyxZol13c3NzLCwsTI65adOm7Nu3D4DSpUtTqlQpHjx4wK1bt8iVK5d2je7cuUOjRo2M+vv4+JAzZ04AunTpQpMmTbSg5KFDh0zOXuvXrx+FChVCp9OxZ88eBg0apH2WPFOtR48eBksBM2fOzObNm9m2bRsAS5cu5ccffzRYqnjnzh2qVq1KiRIlADh+/DgrV67Ex8cHvV5P9uzZyZYtG9OmTaNPnz7ExMQYBPbePaYQQgghhBBCfErvnXaQK1cuChQoQObMmWnatCkAkyZN4unTp6xevZqHDx8yZMgQnj9/TosWLQyK+N/x5MkT/P39sba2Jj4+nkGDBlG8eHECAwOZO3eutuQna9asfPXVV1q/4sWLGyyV+uOPP+jXr59B3YYNG7T2uXPnJj4+XlveFRgYSHx8fLrH6eLiwpIlS3BxcTEq8+bNo0CBAib7KaUYO3asQdCgYcOGxMXFUbJkSaMH89atW5M7d26cnJy4f/++ttyudOnS2nktXLgQMzMzo8BHvnz5DPbn7+9Pw4YNjcq3334LJAXTrl27ZjDrsGbNmty5c8cgIJWQkMDIkSNZv349hw8fZsqUKRQsWJApU6bw1VdfcfTo0TSv3ZMnT4yWw02bNs1kWzs7O8zMzJg7dy6DBg3C3t4eOzs79uzZw/z587Gzs9NKstevX/Ptt9/y5MkT8ubNS548eTh16hQA06ZNY968ecybN88oABgSEkLTpk1Zvnw5e/bswdXVlTdv3qDX602W5CDjr7/+Snx8PDVr1mTw4MHEx8czefLkNK8BJAXOHj58CCTNMGratCk1atQg42GMlgAA9oRJREFUX758FChQQFs2GR0dbTJA6uDggKOjI46OjlhaWpIhQwZtO7Wlcebm5tSrV4/ExER69epFREQEERERODs7a8tCly1bZjQLq2TJkkyePJnDhw9z6dIl+vfvz5kzZ7hy5Qo2NjYcOHCABg0aaLMR+/bty7fffkuBAgXo2rUrbdq0ITExkXXr1nHixAkqV65MfHw8x48fp3r16qxZs+a910sIIYQQQgghPpb3zpSCpAejV69eAUkP3JaWllhYWNCzZ086d+7M3Llz+eabbwgODk7XQ6H49zE3N9cewC0sLGjfvj0JCQls2bKFypUrY2VlRUxMDEWLFjX4jn18fAxmInXp0oW6devSu3dvg32nZGFhwY4dO4CkmRrJM0HSMnXq1FQDKKYMGTJEm0318uVLevbsyePHjzl69ChZsmTh3LlzZM6cmZkzZ9KpUyfatWuHp6cnK1euxNbWlsOHDwNJ+X9CQkJ48+YNtWvXpkOHDnTr1k1L/J4c3ElWuXJl7t27R3h4uDbbyN7eXps5NmvWLFxdXalVq5Y2S8zPzw+lFEFBQVSvXh1/f3/c3d1ZuHAhDg4OADx8+JABAwbw/PlzvLy8KFKkCKdOnaJJkyYMHToUJycnWrVqxahRo5gwYYLJJZPZs2c3SnKd2vI9gJ9++onAwECD7/Lly5epJsoePnw4uXPn5qeffmLWrFmcOXNGmx1VoEABXFxcAOPZWc2aNaNZs2b4+vrSo0cPfvrpJ5o3b86JEydMHue3336jb9++mJmZ8fLlS86dO8f9+/epXr16upbrWVpaajmWJk6cSL58+Zg3bx5nz57F1tZWC5pFR0ebnK2VcqYfmF6+l+zUqVPabKuaNWuSLVs21q5da9A/5Qy3smXL4u3trW3r9XpiY2OZMWMG8+fPx8HBAU9PT6ysrHB1daVUqVLUrl1ba3/8+HHmzZvHjh07WLBgAfnz58fd3Z3169ezZ88ebG1tsbCw4N69e1qANTWxsbHaSy4AoxxoQgghhBBCCPFPpStBy9q1axk8eDCQ9ACdcjaKlZUV3333HdevX2fcuHEfZ5Tik1FK4eHhwZdffomtrS3du3fXHtZTluRle8nLpJKLTqfDzMzMqC6lwMBA1q9fDyQ96I4ZM+a945o8eTLx8fHEx8eTL18+9uzZo22bKr/88gsAq1evpnjx4nh7e+Pr60vevHmxtbWlWrVqWFpaYmlpSbly5Rg1ahRPnz41uZQpZ86cXLhwweQSVaWUQeDi4sWLhIWFadt6vZ4sWbLQtm1b2rZtS7Zs2ahYsSJt27bVgheRkZFAUiBtwoQJbN26lVu3btG9e3cg6S17ZcqUoWDBgly8eNFkTqYuXbpw5swZ9u/fT926dY1m3EBSMMjGxsagpLZE7sSJE8yYMQOADBkyEBMTQ0xMDHq9noSEBG075Sy3cePGsWHDBu07T7nv3Llza8d88uSJ0fEePHhAw4YNqVmzJj179uT48eMopVBKcfPmTZydnbXtlEtD58+fT2JiInnz5qV///7pzmO3ZcsWIOleTM6DtnHjRtq2bau1iY6OxtbW1qjv48ePtfusdevWrFq1StvetWuXQdvq1asTGBiIh4cH0dHReHh4cO3aNYYMGWJUtm7dahCQArhy5QqNGjXi6NGjWuDL399fuxebN29OtWrVtPa1atXi9OnT3L59W1sW2bBhQ7y9vWnWrBlFihTBz8+P69evU7Zs2TSv0ezZs8mUKZNW3g3GCSGEEEIIIcQ/la6gVHJemLQUK1ZMmxUh/ve8ffuWDRs2EBcXx9q1azl48CC3bt3CycmJ0NBQlFKULl2a7du3o5TSAhrvLgcztXxPp9NpeaiUUgwePJh27dr9f+zdd1RTSfsH8G8gVEFAEBsIdlwriq6KBZW1LOwKFnStWNaKYu+KurqIva6CDewdK4KiYkHFjmABURBRirTQEkjI/P7g5P64JBR3RXd9n8859xzvZObeyeSG982zM88AKMqd9OLFC95sK1UUga6CggLEx8ejZs2aKpd2AUXBF0XenLp16+LYsWMYNmwY3N3ducBG8cPBwQEGBga4cuUKJk6cqPL+itle48ePh46ODrckUSaTlZnIPS0tDbVq1SrzvSmCN3PmzIGjoyPat28Pb29vnD17FomJiXB2dkZERAR8fHygr69f6nVatGiBu3fv4vDhw0qz0xhjUFNTUxovuVyudJ3o6Gj0798fgwcPBlCUN0sRjJwzZw4OHjzInRefIfTp0yfUq1cP/fr1w6NHj6ChoYGFCxcCAPbv3w9ra2ssXLgQly5dQnJyMtfu8uXLsLW1RXJyMlq0aFHmWBX38OFD7Nq1C4MHD0a7du3g4uKCiIiIctvFxMQgJCQE1atXx9atWxEbG4uYmBgcPnwYEyZMwKZNm3D69GlkZWWpHO/igdiSQdiSidjV1dWhp6fHy+UVExOD69evw8bGhjsSExO5pY7FhYSEoGXLlry/rffv3+dm8qmipqaGsLAw/PTTT9xOfe3bt8e8efPQoUMH+Pv7IygoqNzk+AsWLIBIJOIOxS6UhBBCCCGEEPKlVGwrK/Jdk8lkcHR0RFBQELS0tHD9+nXUrl0bHh4emDJlCgwNDXHt2jWkpKTAwcGBa5ebmwuxWMw7Bg0ahB07diiVL168GABw7do1xMTEwNPTEwDwww8/ICQkBCKRSGVwp2Sw6tWrV2CMoV27dkqzt3R0dDBjxgxefXt7e/Tq1QsAsHnzZqVgmUAg4JJea2trl7mcCShKyM4Yw6hRo7ix09LSKrV+aGhouYEWxW5qxWdAKXZhS0hIgLq6OurXr1/mNRTU1NRgZmamVP7+/XvEx8dzs8MUh2I2VMk+jxw5Etu3b+f6ogjgbdy4EaNGjeLOi+cLs7OzA2MM58+fR9u2bcEYw+rVqwEAPXr0QI8ePaCuro53795hwoQJ3NKwyMhIzJs3j5uNWVEzZ87Epk2bULduXQBFn42DgwMyMzOVniXFcySXyzFp0iSMHz8eurq60NfXx6NHj+Dp6YmhQ4dCIpHg48ePmDZtGhITE1UGFM3Nzbln59SpUxg9ejR3/ssvv1So79HR0Zg+fTp3BAQEqKx36NAh3jXPnj2LvLw8JCYmqlzeuGzZMggEAqxatQpr1qzh+vXmzRu0aNECzs7O2LRpE7S0tNCyZcsy+6ilpYWqVavyDkIIIYQQQgj5kiqUU4p834RCIa5du4bk5GRYWloCKFqmeeHCBbx79w76+vrYsmULPD09eTM2VAVjFLNGSu7yptCzZ0+EhYUpBX+CgoK4QExxTk5O+Ouvv1CnTh0AQGJiImxtbXH79m2lugMHDixzVpKDgwNcXV2VyhWBk7JkZGQAAEaPHs3NDmrdujUkEkmpya0lEgn27duHY8eOlXltCwsL1KhRA48fP+aWYr18+RIASk3c/rkuXLgAZ2dnnD59mle+bNkyxMTE8MoU70+xrPBL6devH3r06AErKysEBgZyz8/MmTMBALNnz/6s6wUFBUFHR4e3bDgsLAxZWVnc86Jw9uxZREZGYvr06bCyssLSpUtx6NAhAEBBQQGCgoLw8eNHnDp1Cq1bt0ZBQQHOnz+Ppk2b8q7j4OCAbdu2cfnAhg4dil69enHPVUxMDMLCwsrte7du3RAcHMydr1y5Eqmpqbw6Fy9eRHp6Ouzt7QEU5RX7/fff8eeff8LY2Bjjxo3D/fv3eTNZly1bhpo1a2LHjh24f/8+/Pz8cPz4cW5ZaqdOnSCRSHjLFAkhhBBCCCHkW6lQUCo+Ph6NGjXCmDFjsHXrVl6uGMUshdLakf+GkkGievXqQSQS4c8//8S8efOgpaWFmzdvwt7e/h/nlimekwoAAgMDERgYiFmzZnFlcrkc6enpCA4OhoGBAVfu4ODAm61VXHx8PLcssCQHBweYmJgoBWCAomBWafl15HI5Jk+ejDdv3mDSpEmYNm0at2tZ/fr1kZGRAUNDQ65+ZmYmZs2aBW1tbQwaNAh2dna8ZVJNmzZF9erVwRhDTk4O1NXVoaurC3d3dyxfvhwNGzaEubk53Nzc4OLiwiUH/yeePXuGo0eP4sKFC//4Wp8jICCAlyi7Xbt2+PHHH9GpUycu+PlPlMz3VFhYiFmzZqFTp068z6SwsBChoaEQCoXQ09PD1q1bee00NTVx7do15OTkoE2bNgCKkqn7+vqiYcOGvLolc0aVDMI2b94czZs3L7XP6enpSEhIwI0bN3h9zM/Px4QJE7jz3NxczJo1C4sXL4a6ujpu3bqFQYMGYfTo0VxOrVu3bqFz5844cOAA12+gaOZdw4YNYWFhAbFYzNtIQPHefX19MWbMmDL7SgghhBBCCCGVrUJBKcYYpFIpjh49ivj4eJw7d47LWZOQkIA///yTm6HCGMOYMWO45Vnkv6GgoAAFBQUQCoVIS0tDYGAgfH19ER0dzS3H8vDwQLNmzeDp6YnJkyfzAlkfPnyAnp4ekpOTy10CV1x4eDiGDBmCLl26cDNCTExMwBiDsbExxowZozLBuMKTJ0+gqakJqVSKZ8+ewcbGhvf6unXrKpRIfcGCBQCKloApZr2IRCJuB7Jbt27B1NSUq9+rVy9oamrC29uby9uTnJwMa2trODo6IikpCVu2bIGvry+39EldXR1SqRS7d+9Gbm4uGGMIDQ1Fp06dMH/+fMjlcri6ukIkEsHBwQE7d+6s8DiWJiUlBS4uLujfvz+3jFHx3qRSKbKzs8v9vKZPnw5vb29eWfEd5IqPGVCUR+vRo0dYsmQJ1q9fD6Bol7uAgADs2bMHEydOxNOnTzFr1izY2dmVet+srCyIRCK8efNGKUeWKuPGjUNoaChvBlKTJk0wdepUVK1aVeXsOoXGjRtz/75x4wbs7e2xZs0atG3bttz7VoRYLEZERATq1asHFxcXbqYUYwwCgQArVqzgZuMBRTO+LCws8Pvvv2Pq1Kk4evQoNmzYgBEjRnB1NmzYgE2bNsHOzg7Ozs7cZ9K4cWOYmJhATU0N7du3h5ubGwICAuDj44M9e/bgypUrCAoKQvfu3XH+/Hl06NDhi7xHQgghhBBCCPlsrALi4uKYmpoaCw0NZQYGBszNzY17TSAQsKioKF59VWXk3239+vVMKBSycePGsf3797Pu3bszX19flpuby6sXGBjIhgwZwiQSCa984MCBDADT1dVlDx8+LPd+YrGYAWARERGsZcuWLC4u7m/128fHh+nr67MqVaqwGTNm/K1rlCU4OJjJZDKl8uvXrzNdXV1Wq1Yt3vsNDw9XqiuTyVhubi7LyspimZmZLC0tjaWmprKMjIx/1Le2bduyK1eulPq6XC5nbdq0YV27dlX6HHfu3MkAMA0NDebj46OyfXZ2NgPAsrOzmVgsLvUoOT5paWns5MmTTC6Xs7S0NNa2bVu2aNEi9uLFC8YYY+np6WzWrFls48aNvHazZs1iixYt4s4fPHjATExMWPXq1dncuXNLfZ/z5s1jU6ZMYYsXL2YLFiwotV5JFhYWLCIiQqn84MGDzNDQkPXo0YMVFhaWeY0BAwawXbt2lXuvt2/fsuHDh7Nnz56xEydOsJ49ezLGGMvJyWGGhoZMR0eHHT9+nNdGLpczxhi7desWS01NLfXaqamp7PHjx4wxxpYsWcJq1arFFi5cyDIzMxljjB05coSNGjWK2drasrdv33LttmzZwoYPH15u3xVEIhEDwB4/jq1wG0IqorCwkCUmJpb7fSPk76Dni1QWerZIZaLni1Smr/V8KX4/iESiMusJGCtn2zMA7969Q/369SEWi3H16lU4ODjg2rVrsLOzg5qaGl69esWbaaCqjJDSyGQy3pJQ8uU8e/YMjRs3LjXH1/eksLCwQjOqvrWMjAx8+vSpUv4+5ufnQygUKo2DWCyuUCL/smRlZcHAwACPH8fC2tryH/aUkP8nl8uRkpICU1NTpR0sCfmn6PkilYWeLVKZ6PkilelrPV+K3w8ikajMTZPK7cH+/fuRmZnJnfft2xdOTk5Ku5wR8ndRQKrytGzZ8n8iIAXgPxGQAgAjI6NKC9hraWmpHAcdHZ1/FJAihBBCCCGEkMpQZjRALBZj3rx5SuUmJiY4e/ZsqduYE0IIIYQQQgghhBBSljJnSuno6ODevXswNjYGUDT9aseOHThy5Ag6deoEHx8f+q/vhBBCCCGEEEIIIeSzlbtuysLCAoGBgWjfvj2mTZuGTp06YdmyZWjZsiUcHR0BACdPnkTNmjV57U6dOsXtaEYIIYQQQgghhBBCSHEVSuZjZmaGI0eOoHv37nBycoKzszMEAgF0dHSgrq6OXbt28epbWFhg165dFJQihBBCCCGEEEIIISpVOMN0t27d8Ntvv2HJkiUYNGgQBAIBunfvjlq1auGvv/6qzD4SQgj5lzAy0vrWXSCEEEIIIYR8Jz5r/7+VK1fizZs3OHbsGACgS5cuuHnzZqV0jBBCyL+PoaHOt+4CIYQQQggh5DvxWUGpevXqwdHREebm5gCA9u3b49WrV8jPz6+UzhFCCCGEEEIIIYSQ71OFl+8pHDhwAPr6+gAAa2treHt7Q0uLlnMQQgghhBBCCCGEkIr7rJlSALiAFABUqVIFY8eO/aIdIoQQQgghhBBCCCHfv88OSlVEeno6/vzzz8q4NCGEEEIIIYQQQgj5DlQoKPX69Wvo6uoqlUdHR0MmkymVf/r0CUuWLPnnvSOEEPKvkpkp/tZdIIQQQgghhHwnKhSUksvlkEgkSuVNmzbF27dvv3inCCGE/DtlZNDGFoQQQgghhJAvo8LL9wQCgVIZY+yLdoYQQgghhBBCCCGE/G/4rN33IiIieIEogUCAqKgopVlUsbGxX6Z3hBBCCCGEEEIIIeS7VOGgFGMMrVu3Virr16+f0iwqxpjKmVWEEEIIIYQQQgghhACfEZQSCAR49uxZheq+ffsWTk5Of7dPhBBCCCGEEEIIIeQ791nL95o1a1axiwo/67LkOyOXy6GmVuF0Zd+FwsJCqKmpfZUZgt9qfGUy2Xf33f5ffFYJIYQQQggh5N/ib/8aW716NerWravy6N69+5fsY6VISkqCtra2UvmmTZvw+++/V+gar169QkJCQoXq5uXlYebMmRVODn/jxg0UFBRUqG5luHfvHho2bFhmnczMTHTv3h2vX7/mytLS0tCgQYMy2xUUFEAul5dZRywW482bNxXvcCn27t2Ld+/e/ePrlOfixYvo3LlzhesfO3asws9OcXK5HL1798bdu3c/q93w4cOxbt26z2ojk8kgEAiQk5MDANi5cyfmzp1bobYHDhyAr68vd56cnFzu+IhEIiQnJ3PncXFxGD9+fLk56m7duoWwsLAK9aukJUuWYMOGDX+rLQCcOXMGdnZ2ZdaRy+W4evWqytdmz56NZcuWqXzt/fv3yMvL45Xt3bsXs2bNKvN+169fx927d/Hw4UPY2NgAKHreRCIRNm7ciLVr15bZnhBCCCGEEEK+lgoHpUoGU+zs7DBv3jyVx9ixY794RytTUlISnj59iqdPnyI+Ph7JycnceUxMjMo2YrEYw4cPx7FjxyCRSCCRSMoMOJ0+fRr379+HQCDA4sWL0aVLFwgEAggEAmzbto1Xt7CwECtXrkS3bt0gEolw8OBBrq6q4/bt27z2ISEhZdZPTU3954MGwNDQEAMGDECfPn2QlZUFALh79y4EAgF8fX15x7lz57h2y5Ytw++//84bL6lUiujoaBw5cgRjx46Fubk5pkyZAplMxtVhjMHDwwM1a9aEkZERxo8fr5Rkv7jw8HCMHz8ex44dq9D7OXr0aKljtnjx4jLbnj59+rOCUsuXL8fjx48BFAVfVN1T1Xtbu3YtoqOj0bJlS8yfP19lu5LPkyol2wQGBpbbJioqCllZWYiMjOQdqoJGwcHBvIBKcHAwjIyMyry+j48PZs+ezZ3XqlULZmZmaN++fZmBxSNHjvA+Y0tLywq9vzt37mDdunXo0aMHAgMDVY7lwIEDy+xzRUgkEixYsAALFy4EAEyfPp27/vr167F8+XLufP78+Vy7NWvWwMbGBq9eveLKbt++jRo1apR5P3V1dfTr148LXHp4eGDhwoVgjCE+Pp52TSWEEEIIIYT8e7ByFBQUsNTUVLZu3TrGGGMnT55kt2/fLrPNy5cvmUAgKO/S31RiYiLT0tJijDG2aNEiZmBgwCwsLHhHjRo1mK2tLa+di4sLA6DyuHv3bqn3s7GxYUePHmUfP35kFhYWTCqVMsYYO3bsGGvbtq1SfalUynr27Ml27tzJCgsLmVgs5g5nZ2fm4+PDncvlcl7b69evszZt2vDaKA51dXX26dMnXv1du3aV+p5UHcnJybz2y5YtY4mJiYwxxn777Tdma2vLhg0bxh09evRgrVq14urn5eWxNm3asJ07dzLGGDt37hxTV1dnlpaW7Ndff2XLli1jz58/VxoTLy8vZmpqyq5fv86ePn3KGjduzObNm6dyvMViMWvXrh0bOXIkMzExYa9fvy71s1E4cuQI69Kli9KYDR8+nC1atIhXNzY2tsLjZWxszGubnJzM1NXVmbW1NevWrRs7fPgwq1KlCu+eAJhYLOa1u3jxItPW1mbBwcGMMcYKCwuZVCpVOko+D4wxNmzYMLZ27VruHABLTU1lUqmU/fjjj+zSpUtKbaRSKQPAsrOzGWOMtWzZkunp6TEDAwPuqFKlCuvSpQuvXWFhIatTpw47cuQIq1KlCqtSpQrT1NRkGhoa3HmfPn14bWQyGatXrx67f/9+uWN769YtXtvmzZuzevXqsW7durG//vqLWVhYsNDQUG4su3TpovT+3rx5w+rWrcuWLl3KGGNMLperHMvCwkJeu7Fjx1boM4+NjeW1S0pKYoaGhuzevXussLCQXblyhV25coUNGjSIjRgxgjsvaeHChaxRo0YsOzubyWQyZmJiovJ+06dPZ4wVfbcuXbrE5s6dyxYsWMAaNWrE3N3d2e7du9nbt29Z37592ZEjR5TuUxEikYgBYI8fx5Zbl5DPUVhYyBITE5W+b4R8CfR8kcpCzxapTPR8kcr0tZ4vxe8HkUhUZr1yE8TMnz8fT548gZ+fHwDg0qVLOHToEM6ePYtevXqpbFOnTh3e0p1/GzMzM3z48AFA0YwRW1tbTJ8+HcuWLcOiRYvg5uaGWrVqITg4WGlpTWFhIUaPHo2uXbvyyovP8Cjp2LFjSE9Px8CBAzF69GiMGjWKy80zYMAALF68GMeOHcPgwYO5NkKhEOfPn4eOjg4A8JYaqqmpQUNDQ+XyQ4WUlBSVM2aYilkS48aNw7hx47jXnZycIBaLER0djSlTpkBNTQ2TJk2Crq4ur11WVhbkcjnc3d1haGiIiIgIXLx4ETExMahevTpXLygoiDcDREdHB3/99Rf8/f0BAPn5+bC3ty9zto5cLse6deuwceNGbrnU0qVLsW/fPpXv0dXVFQKBAHv27MH27dvRq1cvhIaGolatWqXeAyga25Ljqq6urlTPwsIC2dnZAIDFixfjyZMnuHjxosprlswz5evrix9//JGb3ZOTkwOJRIIhQ4aU2i9/f38MGTIEixcvRs+ePfHq1StYWVmVmw/J3t6eWzp26NAhzJkzhxszdXX1UnNENW/eHM+fPwcA6Ovrw8bGBjExMUhKSuI9e5cuXYKXlxev7dWrVyGTyTB48GDuPdWpUwc3btwodUnoqVOnULduXbRp0wZbtmxBdnY2990cN24cJkyYgIyMDFSrVo3X5/v37yMuLg4PHz5ElSpVoK+vDy8vLyxcuBCGhoYAgBcvXvDuFRUVhZ49e6JZs2ZYunQpXr58iaZNm1Y4X9aiRYu47/vFixexY8cOXLhwgXtd1fLVGjVq4NSpU7C2tkZBQQF++ukn3usHDhwAoPz9/OOPPyAWi5GdnY3z58+jdu3aeP/+Pa9O586duZ1RMzMzsWzZMqSkpCAxMRHq6uq4d+8e7t27B3V1ddy5cweXLl3Cb7/9xrVX9TeBEEIIIYQQQr6Gcpfvubq6IicnBy1atMD58+exe/dujBs3Dr/++mupOaWaNWtW7nKnb+ndu3dISEiAlpYWpFIpJk2aBCsrKyQlJWHNmjWoUqUKAMDU1FQp+AQAWlpa0NPT4x2K4MDt27d5S/4SEhIwZcoUtGjRArt27cKjR4+4ZTxAUWBg//79mDx5Mh4+fAgAiIyMxMmTJ3Hx4kVuCVRkZCSePn1aofdnZWWFP/74AyYmJkrHnj17oK+vr7KdVCrFiBEjkJqaiuXLlwMAXFxcEBUVhaZNmyIgIIBXv127djAzM4ORkRHS09Px22+/IScnB6amptxypDNnzqhMAP7jjz9i9erV3PmjR4/Qp08fpWP9+vUAgOfPnyM1NRW//vor12bYsGEIDg7mXTcvLw9DhgzBs2fPcOHCBYwaNQouLi7o378/OnToUO4Y3rhxQ2kJlyIgW5xAIICenh5EIhF8fHzg5ubGPQs+Pj44fPgwd654ngAgNjYWq1atwp07d2Bubg5zc3PExcVBS0sL69at446SkpOTMWXKFCxZsgRbt26Fk5MTZDJZqYci0HD58mVIpVLUrVsXXl5ekEqlGDVqVJljAACPHz/GtWvXABQFOho2bIihQ4dCJBLBysqKywkmFouVgnbbt2+HlZUV95m/ffsWBQUFpQak5HI5VqxYgSVLlmDv3r04cuQIGGMYPHgwWrRoATc3N/j7+6NNmza8/GUSiQRubm7IycmBlZUVzM3NceTIEQBFS+QUY1kySCQSiWBtbY2TJ08iPDwc7dq1Q2xsbKljWTL/mba2NgwNDWFoaIgqVapAKBRy54aGhqUmu+/Rowc0NDQgFAqRnZ2N7OxsTJs2DQsWLODOiy9XBYqCpBs2bICBgQGWLl2K2bNnQyKRwMfHB9ra2vj06ROeP3+O3r17Ayha8nju3Dloa2tj1apV3LiMGTMGZmZmEIlEiIyMhFQqRf369REVFVX6Q0AIIYQQQgghla0i064KCgrY77//zrS1tdnly5cZY0XL2PT09NiOHTuYr6+vyuPfrPjyvYKCAiYWi9n+/ftZp06dlJZvFTdgwIBSl+zs2rWLGRgYsAMHDjDGGPvw4QNr2rQpq1+/Pvvhhx9YrVq1WN++fVW2HTt2LGvTpg3Ly8tjf/75J6tTpw4TCATcMiAPDw82a9Ysrg979uxhUqmUyWQyrm+jRo36rGV4xZdzvX79mrVr14517tyZpaenM5FIxB4/fswtY7p27RqrWbMmmzVrFm+aX0ZGBgPAIiIiWOfOnVmLFi1YUFAQt2zK39+fXblyhbVp04ZrU6NGDaavr88taztx4gRr0aIFO3HiBDtx4gSrXbs2W7ZsGTtx4gR78OABY4wxf39/Zmpqyi5evMhat27NLC0t2cyZM1l+fj533cePHzNra2vWsWNHbjmhsbExt3RvzZo1TF9fn23fvl3lErcjR46wH3/8sULL9xTGjh3LDAwMeJ+Du7s78/DwUFm/c+fObMiQIWzjxo3M2dmZicVi9vbtW1alShVePahYvscYYzdv3mTa2trs6tWrzMLCotTPtvhSsPv37zMAzMzMjF2/fp27fkZGBmOMlbp8z8vLiwFgrq6ubOTIkezjx4/Mz8+Pde/enatz8OBB1qtXL+78xo0bDADr1q0bt1RPQ0ODaWpqcueKw9vbmzHG2IULF5impiZr27Yt09HRYefPn2eNGzdmTZo0YStWrGDTpk1jQ4cOZdWqVWNmZmbc8tPFixezBg0asNu3bzNjY2MmFouZTCZjFhYW3HPDGGPdunVT+f6SkpKYpaUl8/DwKPO7U/yz/zvL93744QfuPfv5+ZXb9smTJ7x+ZmZmst27dzM7Ozsml8vZy5cvmbm5OWOsaBmpYhksY4zl5+ezdu3asaNHj7J79+6xtm3bMpFIxMaMGcN69erFjIyM2KlTp5hYLGZ6enplTtmVSCRMJBJxx/v372n5HqkUtESBVCZ6vkhloWeLVCZ6vkhl+rct36tQUEphypQpzMjIiGVmZrKcnBxmZWXFhg4d+o86+q0kJiYyTU1Ntm/fPtaiRYsyfyQWDw4MGDCAeXt78/LO5OfnM3Nzc6alpcXmzJnD1Y2MjGRz585le/bsYf369WPZ2dlMKpUysVjMGjVqxB4+fMjEYjGrV68eCwsLU+qjgYFBqUEpRd+aNWvG1VfkGMrLy2NCoZBFRESozJNTMvfQypUrmZ6eXqn5ahTHyZMnmZWVFQsICODuqQhKMVaUF6hVq1ZKQanLly+z9u3b895bbGwsF5Q6cuQI++WXX7jXmjRpwgVPFA4cOMB0dXVZq1at2PXr19n169dZgwYNmLu7O2OMsU2bNjFdXV22YMECVlBQwLUrHpRirCho0qRJE/bbb78pjfeRI0dY586dlcpHjRqlMih1+PBhJhAIWI0aNXh5v9zc3NiiRYu4c0X+MMYYCw0NZWlpaWzz5s2sf//+TCqVsrdv3zIATEtLiztUBaXu3bvHjIyM2LRp05T6cv78eZW5yRgrel7U1NRYx44dWfXq1Vl2dnaFglI2NjYMAGvbti337PXq1Ytt376dq7Nr1y7Wr18/xhhjqamprGHDhszOzo5169aNq9O5c2d27do1lX1jrCj/15YtW1jz5s3Zhg0bWGFhIZszZw6bOHEimzZtGtu2bRtbtWoVu3DhAps9ezb3bCQmJrJHjx6xp0+fMmNjYy4HlIWFBdPU1OTGUiAQKL2/5ORk1rp1a9amTRteQJExxo2PIpdWcWPHjmXLly/nvkNnz55lXbt25X2vjI2NeUGp3Nxclp2dzSufOXMmmzJlCu8o/rejuObNm7Po6GgmkUgYY4xduXKFNW3atNTxVPyPi4mJCTt58iRjrCgXoK6uLlu3bh1bunQpe/DggdJ3siQPDw+VfwcoKEW+NPo/3qQy0fNFKgs9W6Qy0fNFKtO/LShV4d33AGDLli0IDg6GgYEBqlSpgr1796Jjx46fc4l/hdzcXBw8eBAFBQXw8/NDYGAgIiMjYWRkhIyMDDDG0KJFC5w+fRqMMWhrayMpKQmHDh1CTEwMjh49iqFDh6Jr166oX78+qlSpgvfv32P06NFYs2YNd59mzZrBy8uLW9qnp6cHoVDIXa9x48bQ1tZGXl4eb4lXRezbtw+MMURGRnJlampqEAqFiIuLg0wmQ40aNVQuRxIIBBAKhdwyo0aNGuH69evo1q0bNm7cCFYUrOQdzZo1Q506dfD48WP07dtXZZ8Uy7h69+4NHR0d3Lp1CwAgk8mgoaFR6ntJS0srN9eTUChEXl4eNm3aBDs7O9jZ2WHt2rXw9vaGXC7H+PHjERUVhT///LPMe3Xt2hXh4eHYuHGj0muMMaipqZW6FK640NBQjB49GsOHDwdQtExOR0cHOjo62LZtG1atWsWdF89LFh0dDWNjY7i7u+P06dPQ0NCAt7c3tLW1cebMGdSuXRubN29GQEAA77M9dOgQevbsCbFYjBYtWpQ5VsWdPXsWoaGh6NOnD/r374+2bdvydnMrzY0bNyASiQAU5cC6e/cunjx5grCwMAwbNgyzZs3C/fv3kZWVxS0H3bt3L+rXr89bnvrs2TPcvn0bPXr04C2JLL5Dp6amJp4/fw4DAwPk5OSgT58+6N27N3788UfEx8dj7NixWLRoEXr37o21a9dyOcUkEgnatm2L1q1bIy0tDRoaGhg6dCgAYN26dejTpw/Gjh2rtOz04cOH6NSpE169eoWmTZuqzBlWFsX3TCgUcktTFeeq8lLp6upCT0+PV+bt7Y0GDRrAxsYGNjY2aNCgAXx8fJTaJiUlISEhAfXr14eWlhYAICwsDNHR0YiLiyu1fzExMVBXV8eAAQMAAE2bNsXs2bPRp08f+Pv7IygoCLa2tmW+zwULFkAkEnFHyVxWhBBCCCGEEPJPfVZQSk1NDW3atEFWVhb279+Pjh07wtnZmZfn599OJpPB0dERQUFB0NLSwvXr11G7dm14eHhgypQpMDQ0xLVr15CSkgIHBweunYaGBpYsWQJdXV2Ym5ujRYsWmDp1KmbMmAEDAwMIBIIK5eoBgMTEROjr63M/5jMzM1GtWrVS66sKipTl5cuXAIpyYikCI8WPTZs28eq7uLjAxsYGADBjxgylnEoCgYBLeq1IvF6W69evgzGGbt26ASgac8UPalVCQ0PLDbTUrFkTANCkSROuzMrKChKJBGlpadDR0YGZmVm5fQOKcoLVqFFDqfz9+/e4efMmNDQ0eMf+/fuV6gYHB2PVqlWYO3cuAKBPnz5cAM/d3R0eHh7c+cqVK7l2rq6uYIxh69atGDBgABhjmDhxItTV1dGnTx907NgRVapUwa1btzBnzhyu3bNnz7Bz507eM1keqVSKuXPnws/Pj3vWLl26xOV2Khm8UzxnYrEYbm5u3P0tLS0RGhqKOXPmYNasWXj79i0+ffoEd3d3pKSkcAHF2bNn4+zZs7zrJiQkYNOmTZBKpZBKpXjx4gUMDQ0xZcoUrs7jx4+xd+9eLjjk4eGBrl27Iicnp9T8Z4p+McYQEREBY2NjMMZw9OhRAEDHjh3h4OCAgoICFBYWYsyYMUhJSQEAvHnzBgMHDsSqVasqPJbFLVmyhPte/PLLL0p5yNLS0ip0nWXLlmH69OncJguqHD58GH369OHGJi8vD3/99Rd69OjB5X0rLi4uDgKBAI0aNUJycjLXp2bNmmH58uVo1qwZJBIJvL29uQBeabS0tFC1alXeQQghhBBCCCFfUoWDUq6urlwC5sTERIwePRpA0c5hpe069m8kFApx7do1brcroCj59IULFxAYGIg1a9Zg5MiR8PT0hKamJlfH2NgYb9++xZ07d+Dn54cRI0bg0qVLWL58OTZs2IC6detWuA8nTpzAwIEDAQDp6enIz8+HsbGxyronT57kgjsVlZKSgiFDhqic8dS2bdsyZyW5urrixIkTSoe5uXm5983IyAAAdO/eHQKBADdu3ABQNKOltJlgSUlJOHv2bLnBltatW0NDQwOPHz/myl6+fAl9fX2YmJiU27eKuHDhAmbMmKE0ZqqCjR4eHpg1a9YXuW9x/fr1w8SJE/Hw4UOcOnWKK/fy8uJmZVWUhoYGnj59qrRLZlBQEJeku7jt27fDz88PIpEIvXr14nZkBIo+p7CwMHh4eMDBwQGpqal49uwZnjx5gjp16gAoSv5ecufCn3/+GU2aNMG7d+9QUFCA4cOHY/78+WjTpg1Xx8bGBunp6bhx4waGDh2K27dv46+//kJkZKRScvSYmBhs3bq1Qu+/b9++OHHiBGbMmIGAgACYmpoCAAYPHozVq1fzvt8V1apVK/j5+UEsFkMsFuP06dPo1KkTdy4Wi7Fq1Spu57+yPH/+HJmZmcjMzOSCvsVJJBJs2rQJrq6uAIoSwo8aNQr169fH6dOnceXKFZw7d47XxtLSEsnJyahatSpCQ0ORmpoKAwMD3qyqfv36IS8vD23btv3s908IIYQQQgghX1LF9kAHEB4eDl1dXe6cMcbNZgCATp06ca9paGhg7NixGDly5Bfs6pdTcnesevXqQSQS4c8//8S8efOgpaWFmzdvwt7enheMYYwhJCQEf/31Fy5evIjhw4fj1atXqFmzJpYuXVqhe+fk5MDb25ub0fH27VvUqFFD6QeyWCyGVCrFmTNnsG7dOgQGBlb4/U2cOBETJ05U+Vp8fLzKLesBcO+n+O6BCpMnT1YZeCssLAQA/PLLL8jLy4ODgwP8/PzQpEkTrF27Fubm5nj06BHvR/rHjx8xbdo05OXlYezYsRgzZgwsLS2511u3bo2qVatCLpcjOzub2+1s9OjRmD59OqpVqwYNDQ0sWLAAkyZNKnW3s88RGBiIO3fuYOfOnf/4WhUll8tx+PBhSKVSrszJyQlLly7F4MGDy5w9V1ElZ7bl5uZi8eLFSkHAwsJC3LhxA/b29qhZsya366FCzZo1cfPmTejo6MDKygoAsHHjRuzYsaPUXfWAollyvr6+uHDhAvT19dGoUSPMmzePVyc1NRXe3t44efIk3r17hwEDBqBly5Y4fvw47t+/z32++fn5OHfuHN68eaPyXnfu3OEtTTQzM0P//v1RWFiIli1bljNSFTN16lTeuYaGhlIwrvjyxZIUM7uAouW9ivfGGFN6jleuXInatWujd+/eyMzMxPDhwxEfH4/g4GDo6enh5MmT6Nu3L/744w9MnjyZWyacnZ2NcePGwcXFBerq6nB0dISFhQWAolli+/fvh1AoxMqVK7FkyZJ/PiiEEEIIIYQQ8jd91vK9kvr27YuuXbsCABwcHLjDwsICs2fP/iIdrAwFBQUoKCiAUChEWloaDh06BEdHR/j6+uL8+fOIjY2FhoYGmjVrhu3bt4Mxhvnz56Nu3br47bff0LBhQ7x+/Ro+Pj7IyMhAdHQ0cnJyys1NI5FIMHbsWIwaNQoZGRn48OEDHjx4oPIHs46ODp49e4bVq1ejY8eOWL58OdavXw+BQIBTp05h9OjR3NIcxUyK0ty9exdRUVG4cuUKsrKylJbKubm5QSAQwNnZGQsWLCj1qFOnDgQCAUJCQri2nz59AgBUqVIFZ8+exapVq9CqVStoa2tjwIABqFOnDm7fvs0FwiIiItCsWTO0bt0aHz58QJMmTeDv7w99fX3Url0bZmZmuHbtGrp06QKhUIhq1apx99i6dSscHR3h6OiIHj16oFu3blixYkV5H3e5Xr9+jVGjRmH27Nn44YcfuPL09HSkpaUhNze33MCXk5MT93ls3rwZy5cv5y3pKj5mQFEeLX9/f+zevRsGBgaQyWSYOHEizMzMsHfvXsyfPx+jRo3izQxTJS0tDQkJCYiLiyv3+SssLES/fv3w7t07LFq0iCu3srKCo6Mj7ty5A2dn51LbW1tbcwGpoKAgODk5ITExkTfrqSShUIijR4/i/fv3mDRpEh48eIAhQ4YgNzeXq6OlpYWoqCh4eHggLi4OampqWLhwIXbt2oWGDRtCS0sL7du3h56eHjw9PTFhwgSlMUhLS4Orqytq1aqFvLw8bNmyBU2aNEGvXr3w/PlzdO3aFefPny9zKWxeXh7i4+MRFRUFAJ+da6oscrkcBQUF6N69O1avXg3g/2dKZWRkICIigveMFRQUICgoCLt370ZgYCCsrKxgYmKCO3fucDO+OnTogJCQEOzYsQNNmjThgnVGRkZo1KgRBAIBWrdujYsXL+Lw4cO4fPkyOnfuDDc3N4SEhGDjxo1YunTpZy8PJoQQQgghhJAvpqws6KGhoaxFixasRYsWTEdHhxkbG7MWLVqwxo0bMzU1NcYYYy9fvuT+rRAREcEEAkGZGda/pfXr1zOhUMjGjRvH9u/fz7p37858fX1Zbm4ur15gYCAbMmQIk0gk7OzZs+zSpUtKO3W1a9eOAWCtWrViOTk5Ku+3b98+5ujoyJydndmuXbsYY4xt376dGRsbMyMjI95udiXJ5XJuF7fSjuK7zamyZMkSpqurywwNDdn69esrMkSfJSgoSGX5/v37mZaWFmvYsCGLi4vjysPDw5XqSqVSlpuby7KyslhGRgZLS0tjqamp5WbqL0/J3fdKys3NZXXq1GEuLi5Kuw/Mnz+fAWA6Ojrs4sWLKttHRESwGjVqsIKCgjI/o5LXfv36NQsODmaMMRYdHc2sra2Zp6cne/fuHWOMsffv37Pff/+dHTlyhNduwIAB3DPEGGOnT59mxsbGrGbNmmzz5s2lvs/BgweztWvXsuHDhzNvb+9S65WEUnah8/LyYnp6emzkyJFKr12/fp23+15xUVFRrHXr1uzRo0el3nPXrl0sOjpaqVwmk3E7RhZXUFDA9u/fz6RSKSsoKGBdunRh06dPZw8ePGCMMZaXl8dWrFjB5s2bx2u3detWNmzYMO48MTGRVa9enRkbG1d4V9Hz588zW1vbCtV1c3NjBw8eZDKZjFWpUoW9f/+eMcbYmDFjmKamJnNycuLVV7zXlJQUdufOnVKvW1hYyK5evcoYK/p7XKVKFebs7MwePnzIGGMsLCyM7d69mxkbG/Oep+fPnzMbGxv24cOHCvVfsXsG7b5HvjTaYYhUJnq+SGWhZ4tUJnq+SGX6t+2+J2Cs9P9MHh8fD39/fwDA2rVrUa9ePQwcOBDJycnw8vJCYWEhoqKi8MMPP6CwsBD79+/HsWPHsGbNGuzfvx9eXl5fI65GyD/y8OFDtGnThlv+9D2TyWQqd4j7mpiKpWr/a548eYLmzZuXuVPk35WXl8dbaq2Qm5v72bt8FpeVlQUDAwM8fhwLa2vLf9BDQvjkcjlSUlJgamr6P/F3mHxd9HyRykLPFqlM9HyRyvS1ni/F7weRSFTmpkll9qBu3bpwd3eHu7s7qlevjhYtWsDd3V1lrqinT59i/PjxGDhwIJo1a0YBKfKfYWNj8z/zx/5bB6QA5Zxu/4usra0rJSAFQGVACsA/CkgRQgghhBBCSGX4W7/ES/6oZIxh+/btmDhxIrcrHyGEEEIIIYQQQgghpanwtIlTp05xO3np6Oigffv2+PDhA9LT0wEU7RT1d7ZYJ4QQQgghhBBCCCH/eyoclKpfvz6io6NhYWGBgoIC3L17F3379sWbN28QGxuLGjVqVGY/CSGEEEIIIYQQQsh3pMygVHJyMry9vbnz1NRUyGQyrFixAgCgoaGBN2/eYO3atdw25cUtXbr0C3eXEEIIIYQQQgghhHwPygxK5eTk4MqVK9y5WCyGQCDglZmbm2P37t1o164dr61AIKCgFCGEfGeMjLS+dRcIIYQQQggh34kyg1INGjTArVu3AAB//PEHMjMz8eTJE64MAEJDQ9GlSxfs2rULVlZWldtbQggh35Shoc637gIhhBBCCCHkO1Gh3ffEYjEePHiAs2fPgjEGCwsLeHp6IicnB7a2trC2tsbevXsru6+EEEIIIYQQQggh5DtRoUTnOjo6OHfuHAAgISEBhw8fxpYtW9C9e3d06NABf/75J3744YdK7SghhBBCCCGEEEII+X5UePc9BTMzM8ydOxfTp0+HpqYmAKB3795fvGOEEEIIIYQQQggh5PtVoeV7qigCUoQQQgghhBBCCCGEfK6/HZQihBDyvyczU/ytu0AIIYQQQgj5TlBQihBCSIVlZOR/6y4QQgghhBBCvhMUlCKEEEIIIYQQQgghXx0FpQghhBBCCCGEEELIV0dBKUIIIYQQQgghhBDy1VFQihBCCCGEEEIIIYR8dRSU+k7I5fJv3YV/HalU+q27UOlkMtm37sIXxxhDfn4+GGOQSCRf7b7f41gSQgghhBBCyL/ZfyIolZSUBG1tbaXyTZs24ffff/9b18zKykJSUpJSuUwmg0AgQGpq6t+67j+RlJQEoVBYZh2ZTIY+ffogLCyMK2OMwdzcHLm5uaW2Y4yhoKCg3GtHRUV9Xqc/U2FhIdatWwexuPK3lZ83bx4WLVr02e369OkDX19fAIClpSXu3btXat3s7Gz4+Pj8rf6ZmZnh4cOHn9UmODgYNjY23PmwYcNw7ty5cttNnDgRq1evViq3sbFBSEhImW1nz56NZcuWcec5OTlo164d8vMrtgvbvXv3YGlpWaG6hYWFGDNmDKZMmYLRo0dj69atmDBhAjp37oygoKBS2w0fPhzr1q2r0D0UFN/1nJwcAMDOnTsxd+7cz7oGALRu3brcMXz79i3evn2rVJ6TkwOBQFBqO1XfR2tra8TFxZXapqCgANu3b0d+fj4cHR1x4cIFfPz4ERcuXAAA1K9f/6sG+wghhBBCCCGkNP+JoFRxSUlJePr0KZ4+fYr4+HgkJydz5zExMQAAsViM9PR0fPr0CQkJCXj69CkuX74Mb29vTJs2DZ07d0b9+vUrFEzw9fWFQCDgjpo1a6qsFxcXx6tX8vjc4ENphEIhJk2aBEdHR7x//x4A8Pz5c2RnZ+PEiRPw9fXljsOHD3Ptdu3ahX79+vECU4WFhYiLi8Pp06cxdepUWFpaYujQocjKylK6L2MM9vb2XLBG4enTp7Czs4O+vj4aN26MvXv3ltn/HTt2YM6cOYiIiKjQ+504cWKpYxocHFxqO8YY/P390blz5wrd5+96+fIl5s+fz50vW7ZMqZ9Dhgwp9zohISFK7SoSOHjx4gU+ffqEyMhI3pGSkvKP3hcATJo0CStXruTOGWOQyWQ4c+YMZDIZ1NXVIZPJUFhY+I/vBQDp6elwcXHBgwcPsGrVKnTq1AnHjx9H586dIRKJMGPGDJiZmeHkyZPlXqvkWAYGBpbbJioqCllZWUpjGRsb+4/f28OHD9G1a1e8e/cOmZmZXL/09fWV+qv43F+9eoVu3brxAoLx8fGIiYmBubl5qfdijOH27dsYMGAAACA6Ohp2dnZ4+/YtRCJRqUF+QgghhBBCCPnayp6W8y+0bds2bNu2DYaGhlyZk5MTJBIJGjZsiNu3byM0NBSOjo7Q1taGnp4eDA0NkZycjFatWsHZ2RlDhgxBu3btoKGhUaF7/vrrrzh16hSioqLQs2fPUusZGxsjISFBqbxZs2ZKZcHBwfjpp59UXkfVzImwsDC0b98eANCvXz/ExsZys40OHz6M+vXr84I0WVlZuHnzJoYOHQoAGDduHM6fPw8PDw94enoiPDwcNjY2MDU1RatWrdC6dWucPn2au0dxcrkcM2bMwNWrVzF8+HCuPD09HX369MGoUaOwb98+PHjwAKNGjUKtWrXQt29fpeu8efMGHh4e+P333zFhwgTcuXMHOjo6KseguEWLFmHx4sW8siZNmijV8/X1xejRo3llP//8s8prDhgwoELBDaAoeKdY2iUQCKCurs69duvWLUgkEtjZ2cHAwADW1taYOHEiNm7cCAA4cOAArl69WqH7tGrVCg8fPkR+fj709PTKrZ+WloYXL15g1qxZvHKJRIK5c+dixYoVKttt2LABGzZsAACkpKRg0KBB0NLSgrm5Oe7evcvVK/kcenl5YcGCBdy54vvTpEkTvHr1qkLvURXGGLZs2YLly5cjLy8PGzduRP/+/REZGQm5XA43NzfMmzcPbdq0QfXq1ZGYmFih66ampsLAwKDCgcmbN2/i7du3OHr0KFcmk8nQpk0b3Lx5EwCQkJCgMiDUvXt33vmoUaN4AVwXFxc8fvwYkyZNQkBAAF6/fo24uDhIJBL88ssvuHLlCoCiWUyKgJGVlRXCwsLQsWNH1KxZExMnToS/vz9ycnJUzqjMyMiAoaEhoqOjMXToUJw9exYRERFISkqCra0tmjZtiqioKNStW7dC40EIIYQQQgghle1fH5QyMzPDhw8fABT9SLa1tcX06dOxbNkyLFq0CG5ubqhVqxaCg4O5GQX29vZKs0yGDBkCe3t7jBs3TuV9Sv7YrF69OgBg3759EAgEEAqFvGCEKmKxGNu2bVMqF4lESmX29vZgjHHnU6dORVRUFK5cuQI/Pz/ExMRg+vTpqFatGq9dTk4OZDIZXF1dUbVqVSQlJWHHjh24desWmjdvztWLiopChw4duHM1NTVs27YNHh4eAIryLTVo0KDcYMKHDx8wYsQIxMbG8gKBAHDx4kXo6Ohg9erVEAgEqFevHoKDg3Ho0CGloFRGRgYcHBwwatQobNiwAc7OznBxcYG/v3+5SxaFQqHSzA5Vgbvhw4dj4MCBAAA7Ozv06dOHN4up5DUVNmzYwFu2JZfLebObigc16tSpwwUe5XI5fH194enpiQEDBkBdXR3e3t4ICgri2sfHx6Nx48ZK91bMLmrXrh2AoudM8Zqq3EapqancM6l4/zY2NrC1tcWVK1egqanJjcm8efOgplY0CXLZsmVYvnw5127BggW4desWt5Rr2LBhmDFjBmxsbKCpqcm7p6oxnjx5MjZv3gwrKyscO3YMMpkMo0aNUqonFosxfvx4+Pn58co9PDxgbm7O+x4qltDt2bMHhw4dgoaGBnbt2oX69esjPT0dAwcOxIEDB6CtrY20tDRERUXB0dERQNH3SBH0O3ToEObMmcONpbq6eqnPVvPmzfH8+XMAgL6+PmxsbBATE4OkpCRoaGhwz9ulS5fg5eXFa6uurs5b3tulSxd4enpyz8muXbu4axe3aNEiPHv2DACwe/du3nUVAWoPDw/ezCgLCwscOHAAb968gVwuh7e3N44fP45ffvmFqxMYGIgZM2Zw388zZ87g4sWLePz4MdTV1SESiWBoaIiVK1eiX79+iIqK4n22Je9JCCGEEEIIIV/Lv3753rt375CQkAAtLS1IpVJMmjQJVlZWSEpKwpo1a1ClShUAgKmpKbp27cq1s7Ky4i2JOXbsGH7//Xde2cGDB7n6ZmZmkEql3IyFpKSkz0qUbWJigu3bt8PExETpWLduHerVq6eyHWMM8+bNQ1BQEHbs2AGgKKdRQUEBmjVrprRcrn///jAzM4ORkRFev37NLbdr0aIF9742bdoENTU1paCChYUF73oJCQno06eP0lE8QPP48WPUrVsX9+/fh4GBAe963bp1w/79+3n3EQgESrmt4uLi0K1bN7Rq1Qpubm5wdXXF4cOHIRKJ0Lt3b6SlpZU5tsuXL1dajvXu3TulekKhEHp6erh8+TKePXuGyZMnQ09PD3p6epgwYQKioqK48+JBLnd3d+Tk5HCHnZ0d77p3794FYwyMMd5MuI0bNyIyMhLTp0+Hubk5nJ2dAQC2trZYt24d1q1bp3LpXn5+PqKjowEU5VuSSqXl5lwyMTGBWCzGsmXL0LZtW+Tk5CA1NRUTJkzAiRMneIEhsVjMBVCXLl0KqVSK8ePHY9WqVZBKpejQoQOaN2+O5s2bQ0dHB/Xq1UPz5s3RuHFjXsJ8gUCAtWvXwtvbGxs2bEBISAj++usvaGho4M2bN7CxseEFPos7c+YMXr9+zQXHFHr06AF3d3elHEyLFi2Cs7MzfvvtN9jY2OCHH36AtrY2ateujTt37mD9+vV48eIFwsPDsXDhQq7d5cuXIZVKUbduXXh5eUEqlaoMkpX0+PFjXLt2DQCQmZmJhg0bYujQoRCJRLCysuLGofhYFmdoaMgd6urq3IxMQ0PDUmf/6evrw9bWFoWFhVi2bBmys7O5vHbZ2dnIzs7G/PnzlTYt6NmzJ8aPH489e/ZAJpNhwIABCAgIQExMDLS1tREQEMALUi1ZsgS//fYbunXrhp49e2LgwIHQ0NDAiRMncPr0abi4uEAqlcLX1xdDhw6lgBQhhBBCCCHkm/nXB6XU1dW5H4VCoRAuLi5wcnLClStX0L59e2hqakIikaBx48ZYunQp1+758+eQSqXc4eLiAm9vb17ZsGHDePcSCoXw9/cHULT8piKJnBU5hPT19TF69OhSDxMTEwgEAri5uXFtk5OT8fPPP+PcuXO4evUq6tSpg7t376JatWpYuXIlAgMD8eeff2LYsGHcUr3Lly8jJycHBgYGSEtLQ1ZWFrp37w4fHx+IxWJueZ0ieKPQvn17GBoa8hI76+vrY9y4cRg3bhySk5PRsGFDjBs3Dg4ODly7X375Bb6+vrxZOgp169ZFly5duPNPnz7hxIkT6NGjB1d28uRJtGvXDh07dsShQ4eQl5eH4OBg6OjoIDAwENWrV4eNjQ2uX79e6hjPmTMHYrGYd5S2BEkmk2HhwoXo3bs3ateuzZUr8m6pIhAIkJSUxB0lE8Irlu/JZDJudtvr168xd+5cXLx4EW3atMHp06e5JV4GBgZo2LAhGjZsCFNTU6X7qaur48yZMwAANze3CifV19bWRlhYGN6+fYulS5fC3t4eQ4cOxbVr19CwYUOunlgs5mYIqampQSgUQiAQcP/W0NDgjkePHsHe3p47HzlyJHcduVyOkSNH4smTJ3j8+DHat2+PKVOmgDGGBg0a4MmTJ3jw4IHKvq5du1ZpKSUALkfSqFGjuOdQQSKRYODAgbC2tlYKQjo4OGDPnj04c+YM2rRpw7VRU1PDkydPEB8fj61bt+L27dtlJg5X0NTU5Po+ffp0aGpqYsWKFbh27Rrq16/PBdOKj6VCYWEhr2/h4eHo3r07dz516lRe/UOHDnHB0IkTJ6Jnz57Q0dGBvr4+l6NOX18f+vr60NHRQf/+/XntxWIxcnNz4eXlhZ07d0JNTQ3Hjx/H/fv3AQAdO3bk/V3x8/PDlStXuKB7hw4duKTxL168wKtXryAUChEdHY0WLVqUOkb5+fnIysriHYQQQgghhBDyJf3rg1IKjDH4+vqibdu20NHRwciRI7mcRMUPxbI9xdIdxVH8R3nxsuKSkpJw4MABAEU5mebMmVNuvxQzUaRSKSwsLHD+/Hle4KvksWXLFgDA3r17YWVlhadPn+LVq1eoW7cudHR00LFjRy5A0Lp1a8yaNQvx8fFKM6YAoHbt2ggLC1OZf4gxxvsxff/+fWRmZnLnMpkM1atXx8CBAzFw4EDUqFEDNjY2GDhwILp161bu+y4pNzcXzs7OMDIywtixY8EYw8CBAzF16lRs3LgR3t7eSj/udXV1cfToUSxYsAADBgyAt7e3ymtraWlBW1ubd5QWeHBzc0NUVBSqVKkCmUwGiUQCiUTC7T6oOC8+GyUnJwcdOnTgjpKBls6dO3OfiaKPjRo1woULF9CrVy8uz5QikLFz506un6p2hxSLxdi6dSvU1NSgoaGBMWPGVGiMMzIycPXqVejp6eHgwYP49ddfIRaL4e/vzy1bVFy/+GwduVyOnJwc3LlzB4MHD8arV6/w/v177jh79iw2btyI9+/fY+vWrVw7xhiMjIxgaWmJ+vXrQ1dXF4cOHYKVlRXi4+Ph7OzMu6/C3r17kZycXOqMpVmzZsHAwEDlUleg6POQSqV49uwZgoODIZVKlQJYxXl5eUFNTQ3m5uZwcXEps25xJ06cAABERESgevXqqFWrFg4dOlTmWAJFf1uKf6fbtm3L9VMqlWLz5s28+oMHD0ZSUhJWrFgBiUSCkJAQBAcHY8qUKUrH9evXuYClwrlz5zBp0iQ8f/6cC/gmJCRw3/vRo0fzloiOHDkSFy5cwPHjxyGTyWBjY4NRo0bh/PnzmDx5MnJzcyGRSBAeHo5WrVqVOj6enp4wMDDgjrKSqxNCCCGEEELI3/GvD0rl5ubi4MGDKCgogJ+fHwIDAxEZGQkjIyNkZGSAMYYWLVrg9OnTYIxBW1ubCziVt3xPIBBwS1cYY5g8eTIGDRoEAFi9ejVevHjBy/ukiiLQVVBQgPj4eNSsWZObVVP8AIpmYikCF3Xr1sWxY8cwbNgwuLu7c8vDih8ODg4wMDDAlStXMHHiRJX3V8wiGz9+PHR0dLjZETKZrMxE7mlpaahVq1bFP4gySCQSODk54fnz5zhz5gx0dXUhEAiwefNmxMTE8JKjqzJ+/Hi8fPkSrq6uSq8xxiAQCFSOZ0nr1q2Dn58f9xkuXryYC1Y+e/YMvXv35s4VS7cAcLm5FEfx2V8Af/le8c/By8uLm23Ur18/aGhoQCKR4Oeff8b69etRt25dnD9/HoMHD0ZGRgbXbuHChWjRogWqV6+OZcuWITk5WWnJoyqbN2+GhYUFTE1NsX79ety6dQs+Pj5o1aoVGjZsiP79+3MzWhS7uq1ZswZmZma4evUqTExMMGTIEFhZWcHc3Jw7+vXrhxkzZsDc3By//fYbdz+xWIw//viDm0E1cuRIrF27Fq6urrh58yY6duyI3bt349ixY1yb6OhozJw5E5s2bSp1hzc1NTUsWbIEf/31l8rvlyKgfOXKFRw/frzMfG5nz55FaGgo+vTpg/79+6Nt27YVSrp+48YNLtebr68v7t69iydPniAsLAzDhg3DrFmzcP/+fd5YFlc8uF28z8W/48Xr6unp8XJ2PXnyBBEREbCxseGOiIgIPH36VOleISEhaNmyJbS0tAAUzWAKDw/H5cuXVb43RcA2LCwM/fr1Q82aNaGuro7OnTtj2rRpsLa2hr+/P0JDQ1VubKCwYMECiEQi7lDs9kkIIYQQQgghX8q/Oiglk8ng6OiIoKAgaGlp4fr166hduzY8PDwwZcoUGBoa4tq1a0hJSeEtOcvNzVVa7jVo0CDs2LFDqVyxq9u1a9cQExMDT09PAMAPP/yAkJAQiEQilcGdkj+mX716BcYY2rVrpzR7S0dHBzNmzODVt7e3R69evQAUBRtKBssEAgEuXrwIAGXODFLYt28fGGPc7BSZTMb9iFUlNDS0zKU7FZWXl4e+ffvi8ePHuHz5Mu+aderU4XJ+ladGjRoq+/v+/XsuMFL8KJlTSi6X4/bt27h06RKXW2z16tVcMKlVq1a4fv06d25vb89rn56ejpCQEGzevBmvX7/G7NmzuaTUxZfvFQ+IhYSEgDGGtm3b4vz581xQtG7duhgzZgw+ffqE5s2bY+nSpTh37hyAogT0/v7+3IyratWq4dGjR5BKpWU+Z69evcLmzZsxffp0AEVJ3RcsWABPT08sWLAA9+/fR2xsLDw9PZGSksIFHMeOHYvIyEj069cPjRs3hpOTE8LDw/H06VNuZo+3tzeXZyggIIC7t0gkwtatW7lcZ6ampvD394eOjg5sbGxw/PhxNGrUCM2aNeNmnoWHh2PEiBFcYLA0/fr1g5WVlcpNABRiY2PLDJxKpVLMnTsXfn5+XODo0qVL3FLGkuOpGEuxWAw3NzduJqSlpSVCQ0MxZ84czJo1C2/fvsWnT5/g7u7OG0uFksv3Hj16VObyvdLcu3cP06dP54579+4p1SkoKMCJEyd4OaN27tyJhg0b4tKlS4iJiVFq4+rqCoFAgEOHDmHy5Mlcv0xMTGBqagpnZ2fMmzcPtra2MDY2LrV/WlpaqFq1Ku8ghBBCCCGEkC/pXx2UEgqFuHbtGrekDij6oXrhwgUEBgZizZo1GDlyJDw9PXmzEFQt91LMaCpZrpjp0LNnT4SFhSn98AoKCoKZmZlS35ycnLhdAQEgMTERtra2Kmc8DRgwoMwf1w4ODjhx4oTS0bZt23LHSDEDZ/To0RAIBNxuZxKJpNSAkEQiwb59+/Drr7+We/2yMMYwePBgREZGIiQkhNtJ7ksRiUS4ffs2zp49qzSmFhYWvLpqamo4c+aMUpLy8qSnp6NevXowNTXF5MmT8eDBA6SkpMDLyws//PADAP7yPQ0NDTx8+LDc6yqWYrZo0QKjR4/mgoVNmjTBy5cvlZ6p0p6z33//HeHh4UhLS8Mff/zB9QkoCix++vQJP//8M8aMGQMTExOcP38eCQkJqFOnDgDA2NiYt4OjQCBAUFAQOnTogD179nAzexQ7TBaf5RMfH89dJy4uDhoaGggICMD06dOhoaGBwsJCWFpaQkNDg0s+PmjQIN4SwNJoamri8uXLSjs6Knz48AHHjh2DhYUFfHx8EBcXBwsLC15wVkNDA0+fPuWCu8XH0tDQUOn53759O/z8/CASidCrVy/eDoBJSUkICwuDh4cHHBwckJqaimfPnuHJkyfcGABFn6uDgwMvsN2mTRsEBARw57du3arQd2vEiBHIzMzkjhEjRijV2bVrFxo1aoQmTZoAKJr9tHjxYnh5eWH27Nkqc9/5+vpi3rx56Nu3L2QyGRYsWICxY8dys0IdHR3x8eNHpXEjhBBCCCGEkK/tXx2UApS3pa9Xrx5EIhF+/vlnzJs3D6mpqbh58+YXWVpSMndMYGAgAgMDebOw5HI50tPTERwczNuNzsHBAbdv31Z53fj4eDRo0EDlaw4ODujcuTNiYmKUjoEDB5aa80Uul2Py5Mno0KED7OzscPXqVYjFYvz1119o2rQpMjIyeD/4MzMzMXbsWGhra2PQoEGws7ODra0t93rTpk1RvXp1MMaQnZ2NvLy8csfr+PHjuHjxInbv3o1atWohNTUVqampZc5++Ryenp4wNjZGnz59vsj1VKlWrRrOnz8PkUiEFy9ewN7eHoaGhhg6dCgXsCy+fI8xBhsbG6XrfPr0ibcrHADMmzcPurq6cHFx4ZWXfM6ePXuGvXv38p4zoCh4GBAQAG1tbdja2vKSWQNFwYXg4GB8/PgR0dHROH36NAYNGoSPHz+W+rwBRYnjz507hzlz5nCzwUrKzMxEZGQk914tLS2VAoPq6up4//49GGNYvXp1qferKC0tLVy7dg0+Pj5o164dJk2ahMaNG+P27dvo1KkTTE1NcfDgQd5stZJjmZubi8WLFyuNZWFhIW7cuAGJRIKaNWti/fr1vL8tNWvWxM2bN/HixQt8/PgRAQEBWLlyJWJjY3lJ5I2NjXHhwgWl/GYaGhrceefOnZWSlRf34cMHpKWl4cCBA7xd/IoH34GiQPfy5cvh4eEBADh9+jR69+4NT09P9OrVC9OnT0fNmjXRs2dPpRlTTZo04YKGGzdu5O2ouXz5cm5X0OKBdUIIIYQQQgj52oTlV/m2CgoKUFBQAKFQiLS0NAQGBsLX1xfR0dE4f/482rZtCw8PDzRr1gyenp7cchWFDx8+QE9PD8nJyRXalUshPDwcQ4YMQZcuXbilXiYmJmCMwdjYGGPGjFGZYFzhyZMn0NTU5BI2lwxkrFu3rkKJ1BcsWACgaHmeIueSSCRCfn4+8vPzcevWLd4Ob7169YKmpia8vb25wERycjKsra3h6OiIpKQkbNmyBb6+vtySHEXi5t27dyM3NxeMMYSGhqJTp05l9u3kyZNgjMHJyYlX3q1bN4SEhJT73spy5swZrF+/HmfPnuXNgktOTuZyeJX3eRoaGvICZN27d+e9rljO9cMPPyA8PBy7du3CmTNncOrUKaVgR1nS0tLg5uaGyZMno6CgAI8ePULv3r1hYmKCyZMnw9raGvPnz8eoUaNgYmLCa5uQkIBp06ahbt263EwZTU1N1K9fH0ZGRujZsyc3S0YVReJruVyOq1evon379mjcuDH3bCpycolEIt54/fTTTwgPD4epqSlu3LihNJYHDx5Ey5Yt/1Fy69TUVMjlcsTFxZU5nnK5HL/++ivu3LkDgUCA7t274+jRo9wyTFtbWxQUFOD48eP4448/sGPHDty5c0cpd1NhYSH69euHd+/eccslAcDKygqOjo6oXbs2tm/fXmo/rK2tuX8HBQXByckJS5cu5e3290+IxWIEBQXh5MmTcHFxwYgRI7B7927uMxozZgzvcwgODkbfvn3Ro0cPDBo0CGFhYTh69CgXpBUIBDh+/DgWL16M1q1bY/r06Vi5ciWXZ+/SpUvQ0NCAtbU1XF1dceLECSxevBg3btzA/fv34enpiW7duiEgIICXKJ0QQgghhBBCvhr2L7d+/XomFArZuHHj2P79+1n37t2Zr68vy83N5dULDAxkQ4YMYRKJhFc+cOBABoDp6uqyhw8flns/sVjMALCIiAjWsmVLFhcX97f67ePjw/T19VmVKlXYjBkz/tY1yhIcHMxkMplS+fXr15muri6rVasW7/2Gh4cr1ZXJZCw3N5dlZWWxzMxMlpaWxlJTU1lGRsYX769CREQEq1OnTpl1Hj16xDQ0NNiWLVuUXuvQoQMDwGrVqsU+fPigsv3WrVvZ4MGDmUQiYWKxuNRD4dWrV6xZs2Zs8eLFLDU1lXctCwsLdvfu3TL7e+7cOa4vq1atYr/++is7duwYy8vLY4wxFhAQwJydnVlycjKvXY0aNVhISAizsbFhjx49KvMeCtevX2dt27ZV+ZqTkxOrUqUK27lzJ1f29u1bpqamxnR1ddnVq1d59TMzM5m2tjYTCoVs9+7dvNcGDhzIjh07xhhjbMeOHWzYsGFK91NXV2fv378vta++vr5MTU2NVa1alW3atKnM9/Xo0SMWHR1dZh3GGMvPz2dPnz5VKh88eDBbu3YtGz58OPP29i73OgoAWHZ2tlK5l5cX09PTYyNHjiz3Gm3btmVXrlwpt969e/fY+PHjWWxsLFu7di0bO3YsY4yx2NhYpqWlxfT19VloaCivjVwuZ4wxduXKFaW/ecXFx8ezly9fMsYYGzFiBKtfvz7z8vJiYrGYyWQydvjwYdajRw/266+/sk+fPnHXnjdvHluwYEG5fWeMMZFIxACwx49jK1SfkIoqLCxkiYmJrLCw8Ft3hXyH6PkilYWeLVKZ6PkilelrPV+K3w8ikajMegLGytle7n+YTCbjlnCRr+v+/ftl7gz2PfmvPmfv3r2DmZlZqTvjfQv/lbFMTk6GRCJRyo32JYjFYpUz03Jzcyu88YAqWVlZMDAwwOPHsbC2tvwHPSSETy6XIyUlBaampkozIAn5p+j5IpWFni1Smej5IpXpaz1fit8PIpGozE2T/v2/3r6h/8KP2+/V/0pACvjvPmeVEVD5p/4rY1mjRo1Ku3ZpSyX/SUCKEEIIIYQQQioDhV0JIYQQQgghhBBCyFdHQSlCCCGEEEIIIYQQ8tVRUIoQQgghhBBCCCGEfHUUlCKEEEIIIYQQQgghXx0FpQghhFSYkZHWt+4CIYQQQggh5DtBQSlCCCEVZmioenc/QgghhBBCCPlcFJQihBBCCCGEEEIIIV8dBaUIIYQQQgghhBBCyFdHQSlCCCGEEEIIIYQQ8tVRUIoQQgghhBBCCCGEfHUUlCKEEFJhmZnib90FQgghhBBCyHeCglKEEEIqLCMj/1t3gRBCCCGEEPKdoKAUIYQQQgghhBBCCPnqKChFCCGEEEIIIYQQQr46CkoRQgghhBBCCCGEkK+OglKEEEIIIYQQQggh5KujoNT/ALlc/q278NUxxiCTyb51NyrV9/r+JBIJAEAs/rq7vH2v40kIIYQQQggh/1b/+aBUUlIStLW1lco3bdqE33///W9dMysrC0lJSUrlMpkMAoEAqampf+u6/0RSUhKEQmGZdWQyGfr06YOwsDCujDEGc3Nz5ObmltqOMYaCgoJyrx0VFfV5nVbh5MmTiIiI+MfXKU9ERATq16//2QG5o0ePws7ODgCwbNkyTJw4scz6x44dQ0JCwmf3b/HixXBzc/vsdmZmZoiMjAQAXLp0CS4uLuW2uXfvHqysrJTKt23bBldX1zLbRkZGwtLSklc2ceJEBAUFVbjPVlZWCAkJqVDdixcvomHDhjhz5gzatWuH69evo3HjxliwYAEYYyrbBAcHo3nz5hXuj8Lw4cOxbds2AEB2djbMzMxKvUdpNm3aVO4YFhQU4ObNmypfGzhwIHx9fVW+FhMToxQoW7FiBTZu3Fjm/fz9/fHq1SucPHkSAwcOBAD4+fkhPz8fM2fOxNGjR8tsTwghhBBCCCFfy38+KFVcUlISnj59iqdPnyI+Ph7JycnceUxMDICi2Rfp6en49OkTEhIS8PTpU1y+fBne3t6YNm0aOnfujPr168PHx6fc+/n6+kIgEHBHzZo1VdaLi4vj1St5PHz48Iu8f6FQiEmTJsHR0RHv378HADx//hzZ2dk4ceIEfH19uePw4cNcu127dqFfv368wFRhYSHi4uJw+vRpTJ06FZaWlhg6dCiysrJ499y+fTssLS1RtWpVuLi4ID09vdT+JSYmYsyYMdixY0eF3s+9e/dKHbPhw4eX2fb06dPo2LEj1NQq9xFfvnw5Hj9+DKD0z1kx86cslpaWvDY7d+4st010dDTEYjEiIyN5x5cIHoaFhaFhw4a8MplMhpycHBw5cgSmpqaQyWRfbHYRYwx79uzB4MGDsWXLFvTo0QNv3rxBQkICOnXqhF27dqFVq1ZwdnYu91qurq68sRwyZEi5baKjowEUfV9Kjmd5AdvyZGZmYsyYMfD29gYAODk5cX07deoURo8erfS5M8Ywc+ZM2NnZITExkbtWSEgI6tSpU+b9CgsL0aVLF7x58wZyuZz7zqmpqSE2NrbSvxOEEEIIIYQQUlFlT735j9m2bRu2bdsGQ0NDrszJyQkSiQQNGzbE7du3ERoaCkdHR2hra0NPTw+GhoZITk7mfvAOGTIE7dq1g4aGRoXu+euvv+LUqVOIiopCz549S61nbGysckZNs2bNlMqCg4Px008/qbyOQCBQKgsLC0P79u0BAP369UNsbCy39Onw4cOoX78+goODufpZWVm4efMmhg4dCgAYN24czp8/Dw8PD3h6eiI8PBw2NjYwNTVFq1at0Lp1a5w+fZq7h8KJEycwd+5cHDp0CD/88ANGjx6NyZMnq5yJwRjDpEmT8NNPP+H06dMYNmwYbG1tSxsuTt26dZWCLCtXrkRcXFyFxub48eMqr5udnQ09Pb1y719yGaC6ujp3n5SUFERHR2PZsmXYsGEDJkyYgCpVqvBm0uno6JR7D4WHDx+iVatWGDZsWIXq37x5E9euXUPnzp25MrlcDkNDQ8THx6tsk5CQgA4dOgAAcnNzUVBQwD0bJ06cQMeOHQEoj2VSUhJq1arFnbdp04b79927d7lr/h0PHjzA1KlT8fTpU9jY2CAqKgpTp06FtrY2Ro4ciaFDh2LLli1o3bo1Hj16hOzsbOjr65d5zfXr12PatGnw8fEpdZZScTdv3kRGRgZvLIGi70pMTAzq168PALC3t8fVq1eV2vv5+fHOi8+4MjU1xZkzZ9CxY0f06dMHhw8fxp07dwAUzXzq0KEDevXqBR0dHe47IRAI4O/vj9GjR2PQoEG4ceMGMjMzcevWLVy/fh2DBw/m3W/Tpk1wd3dHeno69PT04OLigo8fP+LTp0+oVq0aZsyYgbS0NERFRaFu3brljgchhBBCCCGEfA3/6aCUmZkZPnz4AKDoR5ytrS2mT5+OZcuWYdGiRXBzc0OtWrUQHByMZcuWASj6UVly5sqQIUNgb2+PcePGqbxPQkICzM3NufPq1asDAPbt2weBQAChUAh1dfUy+yoWi7mlQsWJRCKlMnt7e96P2qlTpyIqKgpXrlyBn58fYmJiMH36dFSrVo3XLicnBzKZDK6urqhatSqSkpKwY8cO3Lp1i7e8KSoqihdEUFNTw7Zt2+Dh4QEAkEqlaNCgAV69elXme1q9ejUWLlwIJycn7nz8+PEoLCxUGo/58+cjPDwc4eHhuHr1KpycnBASEqIyKFecQCBQWp5Z2jLG7OxsAEUzv7Zu3Yrw8HCVgSoAXEAqLS0NNWrU4MoZY+jSpQt37uPjw5s1d+XKFdjb2wMomin3448/4tixYwCKxl8ikZQ5M2fcuHHYs2cPd759+3Zu3NXV1SEUClX22dHRERcvXgQAtGjRAs2bN0dMTAwiIyNRp04dboxevnyJvn37AiiauVWvXj3uGgKBAHPmzMGFCxcAFC09fP78OVasWAEAvJlRqvpQpUoVZGZm4s8//0RaWhrWr1+P1q1bq3yff/zxBxwcHJSCV9u2bcO+ffugqanJlefn56NXr15wd3fH9u3bMWLECIwdOxbVq1fH4sWL8ddff8HIyAhaWlpwd3eHo6MjAODgwYMYMWIEr8/dunWDpaUl1NTUIBQKVc4KWrduHebMmQMAOHToEKZOnQobGxvs2LEDQ4cO5T1vNWrUULqGt7c3t2xyx44diIiIwF9//QWgKNBnZmamdM/mzZvj5MmTXJC1eND51q1bWLt2LSwsLHjBVnV1dezcuRPz589HXl4efHx80LdvX6VAa926dbnPIS4uDsuWLcP79++Rnp4OXV1dSKVSREZGgjGGV69ecYFHAEr3JIQQQgghhJCv6T+9juPdu3dISEiAlpYWpFIpJk2aBCsrKyQlJWHNmjWoUqUKgKKZCl27duXaWVlZ8Zb3HDt2DL///juv7ODBg1x9MzMzSKVSXLlyBUDRrBGpVFrhfpqYmGD79u0wMTFROtatW8cLHBTHGMO8efMQFBTELXnr06cPCgoK0KxZM6VcNP3794eZmRmMjIzw+vVrbrldixYtuPe1adMmqKmpKQUdLCwseNdLSEhAnz59lI65c+cCKAqmPXnyhAtIAUCXLl3w8uVLXkCqsLAQM2bMwIEDB3D58mV4eHigfv368PDwQNeuXVXOOinu3bt3Ssvhli9frrKunp4e1NTUsGbNGkyaNAn6+vrQ09PD+fPnsX79eujp6XGHgrGxMXJycrhj9+7dvGtOmDABjDHuUASkYmNjsWrVKty5cwfm5uYwNzdHXFwctLS0sG7dOu4oycfHB1KpFF26dMHkyZMhlUqxdOnSMscAKMoT9ObNGwDA06dP4ejoiM6dO8PCwgL16tXjlk2KxWJu/C0tLSGVSnH79m00adIEUqkUf/75J5o3b47mzZujVq1aMDIy4s6LB2MEAgHi4uJgb2+PhIQETJkyBbm5udDQ0ICHhwe2bNkCDQ0NPH/+XKmveXl52LBhA3R1dXnlLVq0QFRUFCZNmsQr79y5M1asWIF27dph/PjxMDMzg6mpKQQCAVatWoUHDx7AyMgI+/btw+LFi2FkZAQAGDZsGKRSKRYvXoxmzZpBKpWW+zwBwIwZMyAWi9GgQQNs3rwZly5dwtu3bzFo0CA4OTnh8uXLXN3i46mgq6sLQ0NDGBoaQkdHB5qamty5gYFBqfft3bs3AKBBgwbIzs5GdnY2nJycsHPnTmRnZ+P58+dKyyF1dXWxZcsW5OXlccG0jx8/4uTJk9DW1sbjx4/BGOOCzG3atMGBAwegoaGBpUuXokuXLtDV1cXChQuRk5MDoOi7K5FIoKOjg5cvX5Y7XoQQQgghhBBSWf7TM6XU1dW5H4xCoRAuLi4oLCzEiRMn0L59e2hqakIikaBx48a8H/7Pnz/nzUQaNmwYevbsiTFjxvCuXZxQKIS/vz8AYNSoUTh16lS5/Vu2bFmpARRVpkyZws2mSk5OhqurK+Li4nD16lVUr14dd+/eRbVq1bBy5Ur89ttvGDRoEK5cuYLdu3dDR0eH+zFtaGiItLQ0ZGVloXv37hg8eDBGjBjBJX5XBHcU2rdvj+joaIhEIm62kb6+PjdzbNWqVbC1tYWdnR03Syw2NhaMMXz69AmdOnVCQkIC7O3tsWnTJlStWhUA8ObNG0yYMAEfPnzA9evX0ahRI9y6dQsODg5wc3ODkZERnJ2dMWvWLCxcuFDlksmaNWsiNjaWV1ba8j0A2LBhA5KSknifZXJystI1isvKykJeXh4A5ZlrxZfvCQQC7rkYOXIkfv75Z/z444+4efMmDh8+jMTERAgEAqVcTMWpqakhOTkZd+/exevXr9GpU6cKLdfT0NDgco8tWrQIFhYWWLduHe7cuQMdHR1u1pxYLObNJCs+i08oFMLV1VVpqdn+/fu5f0ulUgiFQsjlctSoUQM3btwAUDQDKCgoCDk5OVi5ciVSU1OxadMmlQnGd+/eDSsrK6Xk6np6ejhx4gRatmwJJycn/PLLL7zXFy9ejGPHjmHUqFEqx2DXrl282YyKWYr+/v549+4dZs+ejU2bNpU5jkDRdzstLQ1v3rzBzp074eLigrVr10IoFCI0NJQ3M67keALAiBEjeDO0AOXleyXft8K2bdswevRo3utnzpzhJdTPyMjgLUEWiUTYtm0bHB0d0aVLFwQGBsLHxwfDhw+Hnp4etmzZAi0tLQBFM//69++PvXv34tOnT3j06BH279/PBVANDQ0RExMDfX191K1bt8zlpfn5+cjPz+fOS+aTI4QQQgghhJB/6j8dlFJgjMHX1xcbNmzg7exW8geXWCyGtra2UsBJIBBwy31Kk5SUhAMHDgAo+nE2Z84cpRxLJS1duhSLFy8GULQ0atu2bejTp0+p9RXLhPbu3YtZs2ZBW1sbSUlJpeaA2blzJw4ePAhfX1+l2Se1a9dGWFgYBgwYoNSOMcZ7r/fv3wfw/0u2ZDIZqlevzu3ctXv3btjY2HDnALhZF1OmTIGXlxdMTU3h5uaGkSNH4syZMzh58iRGjRqFYcOGwd/fX2UOoGHDhqFly5YYN24crl69iuvXr6sMBlZ0+d6NGzfwxx9/ACiaYaJYpimTyVBYWMidq6ur8wJg8+bNw6VLlwAAEomEtySt+PK9H3/8Effu3QMAeHl5wcrKCgcPHuSCI0BR8EbVbpDFrV+/HnK5HHXr1sX48eN5ywXLcuLECQD/v+Nkq1atMGHCBN7nIhaLlZ77rKwsZGVlYdasWejcuTNWrlzJvSaXy7FixQosXrwYQqGQex+MMWhoaHA776WmpiIvLw9WVlZIS0tDYWEhAgMDlYJ9nz59wooVK7B3716V76FevXrw8PDA/Pnz4eDgoLQ0bvv27Rg/fjxkMhn8/PwwduxYACg1cHfhwgW8fPkSZmZmOHHiBDp16lTeMAL4/7HU1dWFn58fIiIicObMGTRt2pT7vikSuZccTz8/Py4f219//YVHjx5xSzJzc3N5ASWg6PPKzc1FzZo14erqikGDBmHevHlKfTIzM8P8+fN5ZYWFhWjUqBFiY2O5zyYhIYELdLVs2RItW7bk6hsbGyM8PBzZ2dmoWbMmrl27BjMzMzRs2BABAQGYOHEiwsPDoa+vj1atWpU5Rp6enp8VVCeEEEIIIYSQz/WfXr6Xm5uLgwcPoqCgAH5+fggMDERkZCSMjIyQkZEBxhhatGiB06dPgzEGbW1tLmdPecv3BAIBl4eKMYbJkydj0KBBAIpyJ7148aLc7eMVga6CggLEx8ejZs2a3A/d4gcAXv6bunXr4tixYxg2bBjc3d15y8cUh4ODAwwMDHDlyhXeLIviFAGe8ePHQ0dHh1uSKJPJykzknpaWxktqrYriB/KcOXPg6OiI9u3bw9vbG2fPnkViYiKcnZ0REREBHx+fMpNSt2jRAnfv3sXhw4eVAlKMMaipqSmNl1wuV7pOdHQ0+vfvzyWAjoqKgo6ODnR0dDBnzhwcPHiQOy85U2Xfvn1ISkpCUlKSUt6v4sv3FAEpxf2MjY3h7u6O06dPQ0NDA97e3tDW1saZM2dQu3ZtbN68GQEBAYiMjOTaPXz4ELt27cLgwYPRrl07uLi48AKppYmJiUFISAiqV6+OrVu3IjY2FjExMTh8+DAmTJiATZs24fTp08jKyuLGOzIyElZWVpgwYQIEAgHGjBmDlStXcssNzc3NYWFhgT179qBevXowNzdHUlISgKLg1rt376ChoQENDQ0YGBggNDQUnTp1wunTpzFz5kxMnjwZN27c4GZLyeVyjB49Gj/++CN+/fXXUt/LlClTkJKSgmvXrim9pvjO5OXlYdq0aVygTFWOq8zMTEydOhWurq7Q19fH4sWLeWNdGplMhk2bNqFRo0ZwdXVF69atERkZiTVr1mDy5Mm4efMmFi9ejKysLAgEAqWE+Io+Kr6ziqBkabnl9PT0uKXEirHdsWMHbGxsuMPAwEDlBgEPHz6EqakpqlSpws2GCgsLw927d7lZjarG8MGDB2jZsiWXP8ra2hrz58+Hra0t/P39ERQUVO5GAwsWLIBIJOIOxY6ehBBCCCGEEPKl/GeDUjKZDI6OjggKCoKWlhauX7+O2rVrw8PDA1OmTIGhoSGuXbuGlJQUODg4cO1yc3MhFot5x6BBg7Bjxw6lcsUsp2vXriEmJgaenp4AgB9++AEhISEQiUQqgzslg1WvXr0CYwzt2rXjAiPFjxkzZvDq29vbo1evXgCAzZs3KwXLBAIBl/RaW1u71GTeCvv27QNjjFsWJZPJuB+4qoSGhqJFixZlXrNmzZoAgCZNmnBliuVaCQkJUFdX53YsK4+amprK5NDv379HfHw8FxhRHIrZUCX7PHLkSGzfvp3riyKYtHHjRowaNYo7L54vDAAKCgoQHh6O/fv34+TJk3jw4AEWLVoE4P+X75UMiLm6uoIxhq1bt2LAgAFgjGHixIlQV1dHnz590LFjR1SpUgW3bt3ikmoDwMyZM7Fp0yZuNs6+ffvg4OCAzMxMpWdJ8RzJ5XJMmjQJ48ePh66uLvT19fHo0SN4enpi6NChkEgk+PjxI6ZNm4bExEQuoNi8eXOcP38eR44cgb6+Ppo1a4aoqCicOHECUqkUUqmUmz2Wk5MDqVTKfa4ikQht27bldnFUU1NDfHw8wsPD0bFjR8TGxiIzMxM2NjbcTKLU1FSIxWKlXGcl6ejoYMKECdy1VYmNjS03MLphwwZ07dqVCxZPmjQJK1asUDmWxcfT09MTNWrU4GZV+fv74/Xr1ygsLESXLl3w/v17eHl54caNGzAxMVG5fE/xPZw6dSr8/Py48/J2BVSQy+WYPn06d2zZskVlvUOHDvGWOX78+BFHjhyBjY0NNmzYoFQ/JCQEAoEAP/30E+7fv8/1a8yYMZg0aRJ69uyJu3fvIiAgQGkHv5K0tLRQtWpV3kEIIYQQQgghX9J/NiglFApx7do1bkkdUPRD9sKFCwgMDMSaNWswcuRIeHp68nb60tLSgra2Nu9QzHwoWa74MdqzZ0+EhYUp/SgLCgpSGUxxcnLidgUEgMTERNja2qqc8TRgwIAyf3w7ODjgxIkTSkfbtm3LHaOMjAwAwOjRoyEQCLi8NxKJhDdzoziJRIJ9+/aVOdMFKEqMXqNGDTx+/JgrUyRNLi1x++e6cOECnJ2dlcZMsVtdcaNHj8bGjRvLDdCVNGnSJOjp6aF37944evQo3r17h+7du2PmzJkAipbvFQ+ITZs2rULX7devHyZOnIiHDx/y8o8FBQUpzdQKCwtDVlYW6tSpwys/e/YsVq5cyS2bK54XraCgAEFBQdi1axe6deuGx48fo6CgAOfPn+ddp1GjRrxrxsTEYOrUqejXrx/y8/O5mT2Knf8U4uPjedfp2bMnhgwZgsePH0NDQwN79+6Fh4cHNDQ0uHqmpqZc/rPyrFy5UimnlIIikNi7d29s3rwZt2/fRrVq1ZQCPh4eHkqJ6cViMW7evKn0vczIyOACvTKZDPv27eO9fu7cOURGRqJly5bYunUrGjduDG9vb6XPpEOHDrh06RIXuN64cSN+++037jwrK4sLaJZFXV0dmZmZ3KFYPlpcSkoKfH19MXLkSABF300XFxc4OTlh79692Lp1K5dnTMHOzg6RkZHQ1dVFbGwsXrx4AUNDQy4hvba2Nuzs7FCjRg3erpOEEEIIIYQQ8i38Z4NSgPK29fXq1YNIJMLPP/+MefPmITU1FTdv3vwiy05K5pUJDAxEYGAgbxaWXC5Heno6goODebtwOTg44Pbt2yqvGx8fjwYNGqh8zcHBAZ07d0ZMTIzSMXDgwFJzwsjlckyePBkdOnSAnZ0drl69CrFYjL/++gtNmzZVSqScmZmJsWPHQltbG4MGDYKdnR1vaU/Tpk1RvXp1MMaQnZ2NvLw8CAQCuLu7Y/ny5QgKCsKLFy/g5uYGFxcXmJiYlDue5Xn27BmOHj1a6tLEL2XRokWIjY1FUlISdu/ejZSUFIwbNw7GxsYAlHffK7m8Dyga78OHD3NJwYGiwKSZmRkGDx7MJSEHlJ+jwsJCzJo1C506deJ9JoWFhQgNDUVubi709PSwdetW3uw2TU1NXLt2DQ8fPkRqaiqCg4OxatUqZGdnl5lovVmzZnj+/DkyMjKwZMmSUuvdvHkTNjY23HlISAhvHMaOHYs//vgDjDFuyd8/tXLlShgZGcHe3h7Pnz/H/Pnz8fHjR0yePBmnT5/Go0eP8PHjR65+ydxgALBixQpIJBL06NGDKyssLMSdO3e4PGjLly9XCtbt2bMHly9fRkZGBu7du4f169fD1NRUaSxXrlyJPn368ALXampq3Lm+vj4vZ1dJUqkUL1++RGFhIbdjn6GhIfr27atUd+bMmfj555/RtGlTJCQkwM7ODhoaGvDx8YG5uTl27dqF3r1749ixY7x2BQUFGDlyJH788UfY29tjypQpXBA6LCwMly9fxrt373gBfUIIIYQQQgj5Fv7Tic4LCgpQUFAAoVCItLQ0BAYGwtfXF9HR0Th//jzatm0LDw8PNGvWDJ6enpg8eTIvkPXhwwfo6ekhOTn5s2bYhIeHY8iQIejSpQvs7e0BACYmJmCMwdjYGGPGjFHKQ1PckydPoKmpCalUimfPnvF+/APgtn4vz4IFCwAULQFzdXUFULTsSrFr1q1bt2BqasrV79WrFzQ1NeHt7c0FwpKTk2FtbQ1HR0ckJSVhy5Yt8PX15ZbrqKurQyqVYvfu3cjNzQVjjMstNH/+fMjlcri6ukIkEsHBwQE7d+6s8DiWJiUlBS4uLujfvz83u0Xx3qRSKbKzs8v9vKZPnw5vb29eWfEd0hRjZmZmhsTEROzYsQOenp5wdXWFk5NThfualpYGf39/ZGZmYtq0aZDJZJg4cSJOnz6NM2fOoF+/frh58ybc3d3Rpk0bpfbjxo1DaGgogoODubImTZpg6tSpqFq1aqnBTABo3Lgx9+8bN27A3t4ea9as4c2iY4whMzOTN17VqlVDcHAwPn36xAVqir/+6dMnXLx4kcup9ndIJBKkp6cDKEq0XtYub97e3li9ejUSExPRqlUrjBgxAuPHj4empia8vLzg5eWFJ0+ewNPTE40bN8b169fRrl07pev4+vrCy8sLixYt4mZV1a9fH+7u7jhz5gxvV72Sqlatyn2Xnz9/jpo1a0JNTa1CMxIrQrFUsVGjRujduzc3Uwoo+oxu3boFd3d3rv6nT5/w/PlzBAQEwM/PDzNnzsTYsWPx559/cjPanJ2dYWBggFGjRsHLywvXrl2DoaEhatWqBUtLSwgEArRr1w47duxAjx49kJiYCDc3N2zevBn16tXDzz//jLy8PEyYMOGLvEdCCCGEEEII+WzsP2z9+vVMKBSycePGsf3797Pu3bszX19flpuby6sXGBjIhgwZwiQSCa984MCBDADT1dVlDx8+LPd+YrGYAWARERGsZcuWLC4u7m/128fHh+nr67MqVaqwGTNm/K1rlCU4OJjJZDKl8uvXrzNdXV1Wq1Yt3vsNDw9XqiuTyVhubi7LyspimZmZLC0tjaWmprKMjIx/1Le2bduyK1eulPq6XC5nbdq0YV27dlX6HHfu3MkAMA0NDebj46OyfXZ2NgPAsrOzmVgsLvUoPj6//PIL++2339idO3d41/Lw8GATJkwo8/28fv2aBQcHM8YYi46OZtbW1szT05O9e/eOMcbY+/fv2e+//86OHDnCazdv3jw2ZcoUtnjxYrZgwYIy71GchYUFi4iIUCo/ePAgMzQ0ZD169GCFhYVcuZ2dHVNTU2Pjx49XajNkyBAGgHXv3p1Xfvr0adavXz/G2P8/81KplFdn7Nix7I8//ii1n1lZWczAwIBpaGiwHj16MLFYXGrd5ORkdufOnTLrKDx48IDJ5XJe2aVLl1izZs3Y5cuXWY8ePVh+fn6512GMsVGjRrGtW7cqlb98+ZKZmZkxCwuLcr/jW7duZcOGDSv3XgUFBWzUqFHswoUL7NOnT0xdXZ17rWfPnkxTU5O5u7vz2ije5+vXr1lkZGSp187Pz2chISGMsaLvfpUqVdjIkSPZ69evGWOMXb58ma1bt46Zm5tzzypjjN28eZP9+OOPSt+z0ohEIgaAPX4cW6H6hFRUYWEhS0xM5P3tIuRLoeeLVBZ6tkhloueLVKav9Xwpfj+IRKIy6wkYK2cLOaKSTCZTSoBMvoxnz56hcePG0NbW/tZdqXSFhYUqd2z71hhjZc5GS0tLg1Ao5C1T/Tf4L3wvZTIZwsPDv9gsrJLy8vKgq6tb4fKKysrKgoGBAR4/joW1teU/6CEhfHK5HCkpKTA1NeV2oSXkS6Hni1QWerZIZaLni1Smr/V8KX4/iESiMjdN+nf/evsX+7f/8P0va9my5bfuwlfzbwxIAcr52kpS5Nz6t/kvfC+FQmGlBaQAlBp4+icBKUIIIYQQQgipDBR2JYQQQgghhBBCCCFfHQWlCCGEEEIIIYQQQshXR0EpQgghhBBCCCGEEPLVUVCKEEIIIYQQQgghhHx1FJQihBBSYUZGWt+6C4QQQgghhJDvBAWlCCGEVJihoc637gIhhBBCCCHkO0FBKUIIIYQQQgghhBDy1VFQihBCCCGEEEIIIYR8dRSUIoQQQgghhBBCCCFfHQWlCCGEEEIIIYQQQshXR0EpQgghFZaZKf7WXSCEEEIIIYR8JygoRQghpMIyMvK/dRcIIYQQQggh3wkKShFCCCGEEEIIIYSQr46CUoQQQgghhBBCCCHkq6OgFCGEEEIIIYQQQgj56igoRQghhBBCCCGEEEK+OgpKEULIN8IYQ34+JQ4nhBBCCCGE/G+ioBQh5IsaMmQIDhw4wJ2PGzcO69at484TExNx9uxZuLi4wMXFBQDg7++PV69e4eTJkxg4cCAAwM/PD/n5+Zg5cyaOHj1a6f0WCATIzMystOtbWloiMjKSV/bmzRvUqVOn1DY7duxAVFRUpfWJEEIIIYQQQr4lCkoRQr6o6dOnY+7cubh48aLSa61bt0aDBg2wbt06tGvXDrt27QIAFBYWokuXLnjz5g3kcjnGjBmDHTt2QE1NDbGxsVBTU/2nKi4uDgKBoEKHr6/vP35vlpaW0NLSgra2dqmHhoYGF1grzZEjR7B58+Zy71e1alV07doVcXFxePr0aanvrXXr1irbJyUlISUlhTsPDw/HrVu3Pus9E0IIIYQQQkhlEX7rDhBCvi8dOnTA4cOHERAQAHV1dSQkJEAgECA0NBQSiQQBAQGws7Pj6qenp0NPTw8uLi74+PEjPn36hGrVqmHGjBlIS0tDVFQU6tatW+r9DAwMkJqayp0HBAQgPz8fzs7OvHrq6uq8c5lMpnStwsJCXrlQqPwn8tGjR2jevHmp/dm9ezcCAwN5ZYr+hYeHw9zcHJGRkSgsLCz1GgrDhg1DTEwMHj9+DGdnZxw7dgz9+vVDTEwMevfujbi4ONy/fx/a2toq23t4eEBbW5sLgN28eRPnz5/H5cuXy703IYQQQgghhFQ2CkoRQr6YmJgYDB06FPv378dPP/2ENWvW4OXLl3j79m2pbeLi4rBs2TK8f/8e6enp0NXVhVQqRWRkJBhjePXqFTp27MjVt7CwQFxcHO8axYNHYWFhkMlkGDRoUJl91dDQUCozMTHhnTPGlOq0bdsWAoGgzGv379+fd96yZUsIBAJs3boVd+7cwevXr3HlyhV4eXkBAHe9Zs2aKS3x8/Dw4P69b98+SCQStG/fHkDR+z5x4gQaNGiANm3a8NrduHEDx44dw7Nnz7iyUaNGYeXKlbh16xa6dOlS5nsghBBCCCGEkMpGQSlCyBfTsGFD/Pbbb+jbty/Cw8MRHByMcePGwcrKCrNnz4aVlRW6d+/Oa3PlyhUcOHAAP/30E5YuXYqwsDBkZWVh+vTp3NIzkUgELS0tGBkZ4eXLl0r3HTduHPbs2cMrW7NmDffvqVOnYsuWLUrtMjIyYGhoCKAoMKQ4z8nJgb6+vsr3eOjQIcTExEBTUxNSqRR5eXnIyclBQkIC4uLiMHbsWIwfP56rHxMTg6pVq0IikWD+/PkYMmQI1NXV8enTJ6SlpcHW1hZJSUlcHxQGDhyIU6dOAQAuXbqEPn36wN3dHR4eHlxQCiia/VRyueDTp08xaNAgbNu2jTfLrGrVqti2bRsGDx6MgICAUpf9EUIIIYQQQsjXQDmlCCFf1IwZM9CrVy8kJCTwyiMiIgAAwcHB8Pb2houLC6RSKaytrdG/f3/s3bsX9evXh1AoxP79+3Hp0iVs3boVhoaGiImJQXx8POrWrQsdHR2le/r4+EAqlUIqleKXX37B9u3bufOpU6cqzYD6J8zNzSESifDhwwckJyfj9OnTePv2LRwcHODl5YUBAwbw6isCbkBR0K59+/YYMGAATExMuCWFQqEQQqGQt8TwyJEjEIvF6NSpE1fWo0cPvHjxAhkZGQCAkJAQpKamokOHDlydo0ePonv37li4cCGGDBkCmUzGO5ydnbFixQrY2dlhyZIlkMvlKt9nfn4+srKyeAchhBBCCCGEfEk0U4oQ8sWtXLkSOTk53PmOHTuwadMm6OnpQV1dHWpqahAIBBAKhTA2NkZ4eDiys7NRs2ZNXLt2DWZmZmjYsCECAgIwceJEhIeHQ19fH61atVJ5PzU1NS4Z+ocPH1CvXj1uSZ9IJIKVlZXKdkZGRmWeq1KlShWcOHEC79+/h0AggFQqxatXr3Dp0iUwxlCrVi3e8sJq1aph2LBhOH/+PBhjGDBgAH799ddy76OhoQENDQ1u9pRcLoeamhoOHDgALS0tAED9+vWxbds2MMbAGINAIEBcXBwOHjyIW7duqVyiCABjxoxBSEgIzp07V2oSeU9PTyxfvrzcfhJCCCGEEELI30VBKULIF3f37l388ccfaNWqFfbv34/evXvD19cXtra2KuurqanhwYMHaNmyJZc/ytraGvPnz0fdunXh7e2NGjVqqGxfPKjCGENUVBR++OEHriwtLU3lTKmUlBRkZ2ejdu3aSE1Nhbm5OVJSUkpdtgeAN6to5syZqFOnDg4ePAgzMzPY2dkhMTERhw4d4rVxd3fn/i0QCODu7g5LS0u8e/eOVw4AW7duhZubm8p7jxkzBn5+frwyCwsL7t/+/v5wcnLC/PnzAQAODg5YvXo1Tp06hTVr1iAsLAwAsHDhQshkMrRu3brM5XsLFizAzJkzufOsrCyYm5uXWp8QQgghhBBCPhcFpQghX1x4eDh++OEHCIVC2Nvbo1u3bjA2NoZEIoGuri6vbkhICC/PlCJAo0hoLpFI4OrqCk1NTaxcuZLXViQScTmhFPfV1dXlBWuSk5NVBqV2794Nf39/hISE4Pnz56hXrx6qV69e5vvKy8uDhoYGRowYgU+fPuHVq1cQiUTQ0NDAq1evABQlEy9PVFQUGGN4+/Ytunbtyi11LG1mEwD4+vrC19cXAPDq1SvY29srLZFU5caNG7yk5qmpqWjYsGG57bS0tLgZWYQQQgghhBBSGSgoRQj54i5fvoxVq1aha9euGDduHABALBYjMTERlpaWvB3h7OzsEBkZifbt2+P58+dcHqXnz58DALS1tWFnZ4c3b96gRo0avPu8evUK9erV486PHTuGvn378uokJCQotQOAWbNm4eLFi/j555+hra1d7pI6iUSC9PR0nDt3DsuXL+dmaMnlckRHR+POnTsQCASQy+UwMjLC9OnTS72WItijqanJvcfKIBKJcOzYMZw5c4YrS0pKwo8//lgp9yOEEEIIIYSQz0FBKULIFxUdHY0XL17wkm8DwLlz59CyZUuYmpoqtSkoKMDIkSPx448/QigUYsqUKahSpQoAICwsDJcvX4ZQKMSBAwcwYsQIrl1AQAC3pC86Ohpbt25FcHAw93peXh6Sk5N5M6cUNDU1ERAQgGbNmiEhIQHNmzdHbm4ud9+SHjx4gBo1aqB69ero378/Tp48CQBwdXWFiYkJIiMjYWFhgfz8/DLHRyqVorCwkHvfQFHAS0GR9Ly4/Px8bNy4ET179kTLli3LvH5x8+fPh62tLbckEgDevHmDOnXqVPgahBBCCCGEEFJZKChFCPmiVqxYAXd3d2hoaCAvLw+5ubkoLCyEh4eH0vI7hVq1asHS0hICgQDt2rXDjh070KNHDyQmJsLNzQ2bN29GvXr18PPPPyMvLw8TJkzAhw8fcOLECTx69AjJyckYOHAgBgwYgA4dOkAkEiEvLw+RkZGwtLSEnp4ed6/8/HxERkbi6tWr2LVrF2rWrIk//vgDe/fuRc2aNdG3b1907twZ1tbWsLS0hJGREfT09ODn58ebTTVkyBA8fPgQhYWFqFevHpo0aYK0tDRIpdIyx+f3339Xyg1VfEfBWbNmYd26ddy5VCrFsGHDYGtri2HDhlXoM5BIJJg6dSru3r2L4OBgHDhwAC1atIBEIkFUVFSZuaQIIYQQQggh5GuhoBQh5IsRi8XQ0NCAu7s7GGOoX78+5HI5WrZsCWtrawwcOFCpzdWrV9GvXz8MGDAAt2/fRsOGDXHlyhU8efIEmzdvxsmTJ9GzZ08AQGBgIObMmYMRI0bg8OHD+OWXX9C0aVN4eHhAT08P27dvB1CUN6lt27aQSCRYsWIF734XL16Eq6srunfvDi8vLzg7O0MgEMDV1RWvX7+Gv78/Ll++jNWrVyMpKQnu7u5Yv349Hj58iEOHDuHBgwcAgA0bNqBq1aq8gJe1tTXevn2LqVOnljpGxXNDVcSiRYugpaWF3r17V6i+r68v5syZA1tbW9y4cQPGxsZ49uwZ5syZA5FIhLFjx6JmzZoVvj8hhBBCCCGEVBYBY4x9604QQv635eXlKSVAL6tcIT8/n8vPJJVKy0wUrqDYQa/4rn2lUcx60tDQgEwmg1AoRF5eHiQSCapVq1Zu+2/hzZs3yMzMRNu2bb/odbOysmBgYIDHj2NhbW35Ra9N/rfJ5XKkpKTA1NS0Qt9LQj4HPV+kstCzRSoTPV+kMn2t50vx+0EkEqFq1aql1qOZUoSQb660wFNZASkAvN3hKhKQAioWjFJ1TUWeJ11d3XL79S01aNDgW3eBEEIIIYQQQiqEwq6EEEIIIYQQQggh5KujoBQhhBBCCCGEEEII+eooKEUIIYQQQgghhBBCvjoKShFCCCGEEEIIIYSQr46CUoQQQirMyEir/EqEEEIIIYQQUgEUlCKEEFJhhoY637oLhBBCCCGEkO8EBaUIIYQQQgghhBBCyFdHQSlCCCGEEEIIIYQQ8tVRUIoQQgghhBBCCCGEfHUUlCKEEEIIIYQQQgghXx0FpQghhFRYZqb4W3eBEEIIIYQQ8p2goBQhhJAKy8jI/9ZdIIQQQgghhHwnKChFCCGEEEIIIYQQQr46CkoRQgghhBBCCCGEkK+OglKEEEIIIYQQQggh5KujoBQhhBBCCCGEEEII+eooKEUIIYQQQgghhBBCvjoKShFCiAofPnxAgwYNlMo/ffqE6dOnAwCWL1+OS5cuVVofGGMqy9PS0hAcHFxp9yWEEEIIIYSQr4GCUoSQ/zlxcXEQCASlHkBRQKiwsFCprZ+fH6RSKQDgwoULUFP7/z+jK1eu5K7h6urKa2dmZlbmPQUCAebPn8/VT09PR/PmzREVFaXUh4kTJ8LBwQEpKSnlvtfw8HDu35mZmTh48GC5bQghhBBCCCHkaxB+6w4QQsjXZmlpCalUitmzZ/PKTU1NMXfu3FLbSaVS+Pj44Pjx40hKSsLjx4/Rp08f7vUpU6bAzc0NFhYWiIyM5LV98eIF5HJ5mf3S1tbm/l2tWjVMnz4d/fr1Q3h4OLS0tMAYw8yZMxEREYG1a9fCzs4Ox48fR/PmzVVe7+7du+jXrx8SEhKgqakJmUyGcePGoUePHqhdu3aZfSGEEEIIIYSQykYzpQgh/5OEQiE2b96M3377DcOHD0f79u2xf/9+CIVC2NnZwdzcHO/evePNevLw8EDLli0RHR2NjRs3Yvz48Rg0aBB27NgBsViMmjVrQl1dHUKhcry/atWqMDQ0LPMoHpQC/q+9+46K6njcBv4s7MLSi4oIIti+9t6wY4mi2MVesfcSK/YaTNRYgr2BJWrsvUbEXhHsKAoiBlQU6WVh5/3Dl/tzpRplSczzOeee4947c+/sZdyVx5m5wODBg1GhQgU8ePAADx8+hLOzM86ePYuTJ09ixIgRGDBgAGrUqIEpU6YgIiJCo25SUhKGDBkCd3d36OnpAQAKFiyIfv36YdasWXlzU4mIiIiIiL4AQyki+k9zdHRErVq10KtXL439O3fuhL29Pfbv3w8AiIiIwM6dOzFp0iQMHz4cwcHBmDNnDnR0dCCXyzMESt/Kvn37UL16dcyaNQsJCQm4f/8+ihcvDoVCgUmTJqFUqVK4evUqDh48KNVJTk6Gq6srbGxsMHbsWI3zLVq0CGfOnMGiRYvypL1ERERERES5xel7RPSflJSUhIsXL2rsUyqV0npR6dLXmLK2tsaDBw8wZswYODk5Yc+ePdizZw8AYPfu3Rg8eDBKliyJNm3aaNQ/f/48mjRpkut2NWvWDGfPnsUff/yBzZs3IyQkBJMmTcLu3bultvn5+cHNzU1jvaj0UCw4OBg9e/aEnp4eDh8+DLVarTFt0MTEBGfPnkXr1q1x/vx5rF69GiVKlMjQjuTkZCQnJ0uvY2Jicv0eiIiIiIiIcoOhFBH9J7m6uuLEiRNQq9VS8CSTyTB37tws69y8eRNeXl44ePAgJkyYAJVKhdWrV6NZs2YYMGAAPDw88O7dO406DRo0QFRUlPQ6ICAAbdu2xaNHj2BkZJThGgqFAgDQtWtXdO3aFf3790diYiI8PDwwc+ZMjbIGBgbSn9Of1BcVFYUGDRqgT58+MDc3z/R96OrqIioqCnPnzoWJiUmmZTw8PLK9F0RERERERF+L0/eI6D9r3759KFasGC5fvozGjRtrhD4HDhxAdHS0NBoqICAAvXv3RoECBQAAp0+fxpEjRyCTyXD27FksX74cqampGa4hl8s11o3y8vLCgAEDYGtrC3Nzc/j6+iIoKEg6nllQBQAzZsxASkoKhg0bhpMnT6JMmTKYM2cOZs6cqXHd6tWrY/HixahcubL0BEEzMzM8fPgQQggEBgbC0tISJiYmWLJkCQoVKpTp9dzd3REdHS1tL1++/Nv3mYiIiIiIKDMMpYjoP2vGjBno27cvateujaJFi2Ljxo0AgEmTJqFmzZqQyWRo0qQJBgwYABMTE6xfvx7lypXLcB6VSoWkpCTpdUJCQqaLnT99+hT79+/Hjz/+KO1LSUlBy5YtceHChWzb+uDBAzRv3hzx8fGwtLQE8HEh9IsXL6J79+7SSKnP+fv7Q6FQoGzZsgCAyMhIFCxYMIc7A+jr68PU1FRjIyIiIiIi+pYYShHRf1b58uWxePFiGBkZYc+ePVLg5OLigh49esDQ0BB6enrw9/dHiRIl0KpVq0zP4+LighkzZkChUGDdunWYO3cufvjhhwzlJk+ejB9//BHFihWT9nXp0gWbNm1Cx44d8fjx4wx10tLSoFKpMHXqVHTr1g1TpkzBkydPIJPJYGNjg+PHj+PNmzd4+PBhpm1bu3YtOnXqJE1RjIiIkEZ7ERERERER5SeuKUVE/1kDBgzAvHnz0L17d/j7+2POnDkax9VqNeRyOTw8PDBq1Cjo6HzM8YUQSEpKwtOnT/H69Wt4eXlhy5YtsLa2xuDBg7FixQopBAI+PskvMDAQhw4dgrW1Nfr374/3798jKioK79+/x/v37xEdHY0ePXrg5s2b0iirpUuX4vTp07hw4QK8vb3h5OSEBQsWYN++fejWrRuAj+tK+fr6Zvr+rly5gl27diEwMFDa9+zZM9ja2n7L20hERERERPS3MJQiov80hUIBGxsbAB+fyBcYGIhx48bhwoULCA8Px+DBgyGEwIMHD1CpUiUAwJ49e7Bt2zYUKFAAlSpVQpUqVVC5cmU8evQIKpVKI5ACAF9fX7x79w7Dhw9HoUKFYGVlhUKFCmn82czMDHXq1MHGjRsxbNgwDBo0CJcvX8bVq1cRFhaGH3/8Ef7+/tDR0YGOjg4ePXqERYsWAfgYnvXu3RubN2+Wrnno0CG4ubnBy8sLz58/x4MHD1CmTBkcOHAA7du319LdJSIiIiIiyhpDKSL6TytZsiSOHz+O2rVrIzg4GD/99BMCAwMxfvx4VKtWDeXKlcOoUaM0nqrXuXNneHh4ZBhxtGDBAkRGRma4xtq1a3PVFi8vLykgGzNmDJYsWQJzc3OUKFECt27dQmxsLN6/fw+VSpWhrpWVFQAgODgY3bp1w7t377B792788MMP8PX1xciRI/HixQuUKFECPXv2zPX9ISIiIiIiyisykdXquERE/yEqlQpyuTzDKKd/GyEEDh06hDZt2mS62PrfFRMTAzMzM/j5BaNaNYdvdl4itVqNN2/ewMrKSpoiS/StsH9RXmHforzE/kV5SVv9K/33h+jo6GwfmsSRUkRE+DiN73sgk8nQoUOH/G4GERERERFRjhi7EhERERERERGR1jGUIiIiIiIiIiIirWMoRUREREREREREWsdQioiIiIiIiIiItI6hFBER5ZqFhX5+N4GIiIiIiL4TDKWIiCjXzM0N8rsJRERERET0nWAoRUREREREREREWsdQioiIiIiIiIiItI6hFBERERERERERaR1DKSIiIiIiIiIi0jqGUkRElGsfPiTmdxOIiIiIiOg7wVCKiIhyLSoqOb+bQERERERE3wmGUkREREREREREpHUMpYiIiIiIiIiISOsYShERERERERERkdYxlCIiIiIiIiIiIq1jKEVE35Xjx48jISEhv5uRo7S0NKxduxbJyVkvHP7u3bsMxxMSEvD27dtsz3348GE8fPjwm7STiIiIiIgorzCUIqJ/LbVajdTUVGkLDQ1Fly5d8ObNG439qampUKvVOH/+PGQymca2ZMmSDPtkMhkcHR0zXK9o0aKZlk3ftm/fnqHO52WuX78OANi/fz9Gjx6dbSg1c+ZMeHh4aOzbuXMnOnfurPHehBAaZaZMmYI3b95k+d4qVKjwd243ERERERHRN8VQioj+tXr27AmFQiFt9vb2SEhIQPHixTX2KxQKtGvXDo0aNUJiYiIWL16M3r17IzExEaNHjwYAxMXFYd68eThy5AiOHTuW5TVv376NxMTEDFvr1q2zrBMZGYkDBw6gSJEiqFixIt6/f4/x48ejRIkSmDx5cqZ1QkJC4OXlhZ9++glKpRJKpRLt27fHihUrcPHiRY335u3tLdW7ePEiAMDJyQnjx4/H/v370bBhQ6mdM2fORK1atf7O7SYiIiIiIvqmGEoR0b/Wrl27IISAv78/6tevj0aNGiEgIACPHj3CsGHDUK9ePTx9+hRCCBw9ehQ6OjpQKpWQy+XQ1dWFUqmErq4uAEBXVxf3799HWFgYdHSy/mhMTk7G69evpaAoJiYGS5culc6TGR0dHUybNg2LFi1Camoq2rVrh8qVK+PWrVs4f/48pk+frjHaKTIyEh06dMCkSZNw48YNGBsb4+TJk3B0dISlpSVSU1MxceJENG/eHGq1Gv3795fqrlq1CiNHjoSfnx+2bNkChUIhvW+lUoljx46hXbt2X3/ziYiIiIiIvpI8vxtARPR3REVF4cCBA9i5cyfu3buHlJQUdO3aFatXr5bKVKpUCX369IGDgwO6dOmC5s2bw9DQEGq1GkKIDFPfYmNjYWpqmu11L1y4gFWrVuHMmTMoU6YMPD09ER4enm2QtW3bNqSmpsLKygq1a9dG6dKlsWvXLhgbG+PChQvo0qULXFxc8Ntvv6FkyZJ49uwZWrduDXd3d6l+wYIFMW3aNEREREClUmHGjBm4desWZDKZdJ2QkBD8+eef2LhxI/r06YPKlSvD2tpaWoPq8uXLCAkJgbOzM9RqdbZtJiIiIiIiymsMpYjoX0lXVxc+Pj4YMmQIbG1tUb9+faxbty5DucWLF6N69eo4evQoGjVqBDMzM+nY1q1bsWfPHun1X3/9BVtbWyQmJmZ5XRcXFxQsWBAtWrTAwYMH4enpiatXr2LatGlZ1unUqROWLVsGHx8f2Nvb4+TJkzA3N9co06pVK/j5+aFkyZKoU6cO6tSpg5o1a+L27dsa5aytraU/lyxZEkFBQdLrJUuWwNLSEi4uLggMDISXlxcuXryIhw8f4sWLFzh58iTev38PIyMjLF26FD/++GOWbSYiIiIiIsprDKWI6F/J1NQU27ZtAwBcu3YNJUqUwI4dOzKUs7e3R5EiRdC0aVMAgBACy5cvh7+/P7y8vJCamgoASExMxKNHj1ClShVcu3Yty+vKZDIMHDgQT58+RZ06dTBs2DCUKVMG+vr6SElJybSOubk5li1bhrlz5+LcuXN49+4dli9fjrlz58LIyAi3bt1CamoqnJycAEBawPzSpUvSOaKjo2FtbY0PHz5AX19f2p+UlASFQgFdXV20bNkSQgh4e3tjw4YNMDMzk96ft7c35s+fj/nz56Nu3bqoXr16tvc3OTlZYxH2mJiYbMsTERERERF9Kc7dIKLvwvPnz1G3bt0MW2ZBVWZOnz6NChUqYMSIEXj9+nWmZaKjo2FkZATg40LiKpVKem1qaorY2Ngsz9+uXTsEBwdj5syZmDVrFj58+IC5c+dCqVTi0KFDOHPmjFR2wYIFMDAw0NjSR0iZm5tnOJb+hL62bdsiISEBvXr1QpkyZbBo0SKoVCpYW1tj7dq1SE5Oxvv37/HgwYNMny74KQ8PD5iZmUmbnZ1dru4jERERERFRbnGkFBH9a6nVaqjVaqSlpaF06dI4fvx4hjLpC4Pr6OhorKEUHh6O+fPno3z58hg4cCBWrlyJ3r1745dffkGPHj0yPE0vNjYW8fHxsLKywosXLzBgwAB4e3tj5MiR6NGjB+zt7fHs2bNM23nz5k08e/YMQgjMmjULFSpUwB9//IEOHTpg1qxZOH36NH777Tep/Jw5czB16tQs3/eWLVtw5coVbNiwAQCkkVOLFy+Gl5cXLC0tcenSJTRp0gSFChVCgwYN8Pr1a6xZswZyuRzNmjWDUqnM9t66u7trTO+LiYlhMEVERERERN8UQyki+tfq2bMndu/eLb0uXbp0lmV79eqFHj164KeffkJQUBAqVqwItVqNqlWr4v379zhz5gyGDRuGX375BZUqVULLli016l++fBnly5dHUlISXFxcMGHCBPTt2xdBQUGYOnUq3NzccOTIkUyv/fLlS+zZswf79u2DlZUV2rRpA19fX7i7u+Pq1atISEhA/fr1pfKRkZEoVKhQju9/+/btAIB79+6hYsWKaN68ObZs2YKWLVuiSJEiCAsLw7p162BlZYVJkybB2dkZBgYGUr3s6Ovra0wTJCIiIiIi+tZk4tNHTxER/Uvt2LEDixYtwpkzZ2BtbY379+/D3d0dbdq0waBBg6Crq4ukpCT4+fmhZs2a0NPTgxACnp6emD17Ns6cOYMaNWrA2toaly5dwq1bt3Dy5El4eXkBALp16wZbW1tcvnwZ1atXx5o1awAAb9++hZ2dHXx8fNCwYUPcv38fhoaGKFasGICPa1DFxsbC2NhYauudO3dgbW2NIkWKoH///qhbty6GDh2a4T0tX74cO3fuRLNmzTBq1CjY2NgAADw9PXH+/Hns3bs3Q53g4GDs3bsX+/btw/Pnz1G6dGkMGTIEXbt2RZUqVRAfH4/nz59/ceAUExMDMzMz+PkFo1o1hy+qS5QdtVqNN2/ewMrKik+EpG+O/YvyCvsW5SX2L8pL2upf6b8/REdHZ/uEc4ZSRPSvNmnSJJw+fRp3796FnZ0ddHV1kZycDLlcDoVCAblcDmtraxw9ehQmJiZSvbCwMHTo0AFxcXHYu3cvKlasCODj2k9HjhyBjo4OduzYge7du+PixYto3rw5njx5gujoaFSsWFHjA9zf3x9Vq1ZFhw4dEBoaCiMjI1y8eBGAZig1atQorFq1Ksv3cuDAAXTo0EFj36tXr7Bx40acOHECp0+fRmpqKtasWYM7d+5kCKXWrl2LiRMnol27dujVqxfMzc3RtGlT+Pv7Y8yYMUhJSYGxsTFSUlKwc+dOFCxYMNf3maEU5RX+w5vyEvsX5RX2LcpL7F+UlxhKERF9Q0+ePMFff/2FAgUKSItym5iYaHzArlmzBl27dkWBAgU06h44cAAuLi7Q09PT2P/5GlQjRoyAvb09pkyZkm1bgoOD0bRpU0ydOlUa+fRpKJW+BlZWdHV1IZPJsjz+4sULlCpVCmZmZli6dCn69euncTwxMRFqtVpafL1Zs2YoXrw4bt68CXt7e+zatQu6urpwc3PDmTNncOHCBZQrVy7b95SOoRTlFf7Dm/IS+xflFfYtykvsX5SXGEoREf3LCCGyDYv+qd69ewc9PT1ERUVJ0wnTPXjwABUqVMj1uRhKUV7hP7wpL7F/UV5h36K8xP5FeemfFkpxoXMiohz8GwMpANLIsE+nLab7kkCKiIiIiIgoLzB2JSIiIiIiIiIirWMoRUREREREREREWsdQioiIiIiIiIiItI6hFBERERERERERaR1DKSIiIiIiIiIi0jqGUkRElGsWFvr53QQiIiIiIvpOMJQiIqJcMzc3yO8mEBERERHRd4KhFBERERERERERaR1DKSIiIiIiIiIi0jqGUkREREREREREpHUMpYiIiIiIiIiISOsYShERUa59+JCY300gIiIiIqLvBEMpIiLKtaio5PxuAhERERERfScYShERERERERERkdYxlCIiIiIiIiIiIq1jKEVERERERERERFrHUIqIiIiIiIiIiLSOoRQR/SNFRkZCrVZr5VopKSlISEjQyrWIiIiIiIjoI4ZSRPTNxcfHIyAgQGPf+vXrce7cuVzVT0xMRPPmzTFv3rwsywghEBsbq7Fv2LBhkMlkkMlkGDp0KNLS0jKtm5qaCplMhqSkJADA5s2bMWTIkFy17Vt69uwZDh48mOXxNWvWIDAwUHsNIiIiIiIi0iKGUkT0zW3duhWTJ08GAKSlpSE1NRVKpRJdunRBaGgoUlNTkZqaCiFEhrppaWno378/EhISsG/fPkRGRmZ6jQMHDqB8+fK4ePGitM/T0xOenp6wtbXFuXPn0L59e8THx2d6jU/bplarIYRAWlqaFGqlbyEhIVK9oKCgDMc/38LCwjSudf78eY3jBgYG0rFff/0VP/30U5b30dTUFI0aNUJISAj8/f2zvGbVqlUzrR8REYE3b95IrwMCAjTuFxERERERUX6S53cDiOj7olar8dtvv2HOnDkAgBo1amiMmrK3t5f+fOTIEbRp00Z6nZaWBjc3N1y/fh1Xr17F4cOH0aJFCxw/fhzW1tYa1+nUqRNCQ0Ph4uKC06dPo2jRohg9ejR8fX1x7tw5WFpaonXr1qhXrx52796NsmXLAgBu3bqFWrVqAQCMjY01zvnq1SsAH8McMzMzFC1aNMP7K1CgQIbgKV2hQoUy3V+9enVcvnwZ06ZNw7NnzwAAV65cwZYtW1C4cGHs2rUL3bt3z1CvV69eCAoKgp+fHzp27Ijdu3ejffv2CAoKQsuWLRESEoIbN25AqVRmet3Zs2dDqVRixYoVAIALFy7gyJEjOH36dKbliYiIiIiItImhFBF9U15eXgCAWrVqoXfv3gAAHx8fODk5aZT7/HVMTAy6deuGp0+fwtfXF0WKFMHQoUPx8uVL1KlTBwcPHkS1atWk8k+ePMGwYcNgaWmJ3bt3Y/369ShZsiSioqI0yjk7O6NGjRpYsWIFBg0ahJo1a0KlUkGhUCAuLg76+vpYv349rly5gu3bt0Mmk0Eul0Muz/rj8dWrV7CxsZFGPZ09exaJiYmQyWSZlpfJZHj16hU2bdqEW7du4d69e+jcuTNmz54NZ2dnODk5wdDQEO3atctQd/bs2dKft2zZgqSkJNSuXRsAIJfLsWfPHpQsWRLVq1fXqOfr64vdu3fj7t270r5+/fphwYIFuHjxIho2bJjl+yMiIiIiItIGTt8jom8mKSkJ06dPR6tWrTBx4kRUrFgRjo6OCA0NRc2aNaVt6NChqFGjhjSyKCAgAI6OjoiNjcWVK1c0RlMtWLAAI0eORP369eHh4YGUlBQAwOjRo1GjRg00bNgQTZs2xeLFixEQEAAhhLQ9ffoUR44cwYIFC3DhwoUM7TU2NoZCocDIkSM19hcsWBAKhQLv3r3L9H1u2LABrVq1QlxcHABg3rx5iImJgY5O1h+pc+fORdeuXXHs2DE0atQIQ4YMwZQpU1ClShWcOXMGo0ePxsSJExETEwMAcHV1labnnTx5EgAwduxYrFq1SuO8Fy5c0AjhAMDf3x9dunSBp6cnihUrJu03NTWFp6cnunXrBn9//yzbSkREREREpA0cKUVE34xSqcSQIUPw7NkzXLp0Cd7e3jA2NkZKSgqaNm0qldPX15cCqZMnT6JDhw4YOHAgVCoVChcunOm5T58+jXHjxuH333/HnTt3sG/fPgwYMABNmjTBrVu3sHTpUowbNw46OjpQqVRYt26dFB75+PjA19c3wzk/HymVLioqCubm5ihYsGCmbfHw8ED//v3h6uqKkSNH4uXLl+jWrRvGjBmT5b0ZPXo02rRpA0tLS5QqVQoLFy7EwoULpeNCCISHh+Pt27cwNTXFzp07kZaWhmbNmkllmjZtii5duiAqKgrAx/WqIiMj4ejoKJXZtWsXhg8fjtmzZ6N79+5ITU3VaEfHjh0RHR0NJycnjB49GnPnzs00TEtOTkZycrL0Oj0sIyIiIiIi+lY4UoqIvqkpU6bg1q1bWL58OYyNjdG8eXPo6+vDzs5O2qysrCCTyTBu3Dg0aNAA+/fvx6pVq7BixQpERUVluv3www8ICAjA/v37IZfLYWxsjF27duHHH3+EpaUlAMDb2xuPHj1Cx44dkZycjEePHiExMRHlypXLtK1ZjZTKTvropY0bNyIhIQGdO3fG0qVLIZfLoa+vL43k+lytWrXQo0cPpKSkYOfOnbh9+zbc3d0RFxeHuLg47NixA/Pnz0fJkiUBAAqFAkqlUpoSqFaroaOjg23btkFfXx8AUKJECXh6ekojwwAgJCQE27dvR0REBBQKRabb1atXcf78eSgUiixHd3l4eMDMzEza7Ozscn2PiIiIiIiIcoOhFBF9U0OGDIFSqcT169cxYsQInD59GuHh4VCpVGjatCm8vb3x/PlzqFQq/PrrrzA2Nkbr1q0hhIBCoYCxsXGmW1paGuRyOUqXLg0AGDp0KPT09DB27Fhp4e43b96gb9++CA8Px9GjR1G3bl2cOXMm00XEASAxMRFCCKxZs0Zjf+3atVG2bFl8+PBBY390dDSMjIwAfAyN6tWrB5VKJe0zNTVFbGxslvemb9++OH78OIYPHw4fHx/s3r0bx48fh1KpxNKlS6VF0DMzYMAAKBQKdOzYETVq1MCrV69gb2+Pdu3aQaFQ4NChQwCAqVOnwsXFBYsWLYIQAnv37kXt2rWl4Mrd3R0FChRA1apVMWvWrCyv5+7ujujoaGl7+fJllmWJiIiIiIj+DoZSRPTNxMbG4tChQzA0NETBggUxcuRI6OjoYNSoUdi6dStkMhl0dHTQuXNnbNu2TWOUjq+vb5YjexQKBSpUqKBxrXXr1iE1NRWNGzeW9r169QrOzs4oUqQIoqKioK+vj/j4eI1Fy9NHCAGAgYEBZDIZhg8fjh07dqB27dpQKBQIDAzE48ePYW5urnHN8PBwaXrh0aNHsWvXLqxYsQJDhw5FYmIi7O3tswyWTp48ie3bt8PGxgbz58/HkiVL4OHhgQULFiAyMhKPHz/OdvFxLy8vKVh69OgRbG1tNdbP6tChQ6b1fH19Nc4bGRmZ5bTET+nr68PU1FRjIyIiIiIi+pYYShHRN2NiYoI3b97gypUrGDZsGE6dOoWQkBAcPXoUrVq1ksqtXbsWP/74I8LDwzXqlylTRiNoSd98fHxydf3atWujSZMmSEtLQ//+/fH48WP069cPQ4cOlaa3NWrUCImJiQgICICHhweeP3+OX375BQkJCfD29oa1tXWWT9G7dOkSatWqhdu3b6Nfv37YuXMnxowZg2LFiuG3335DqVKlcOvWrUzrXr58GWFhYdi8eTMcHR2hUChgYWGBhg0bYuvWrXB1dYVSqczV+8yt6Oho7N69G507d5b2RUREoECBAt/0OkRERERERH8HQyki+maEELh27Rr69OmDEiVK4ObNmxg0aBB69OiBIkWKSOVq166NTp06Yfbs2V91vdu3b8PPzw9LliyRFuI+c+YMzp07hypVqmDs2LFQq9WoUKGCFIDp6OhAqVRi8uTJAD4GN8uWLcPMmTNx7949VK5cOdNrpaSkYMeOHahQoQJatmwJT09P1K1bFwAwceJErFmzBo0aNcLBgwfx/PlzJCQkaNSfP38+/vjjDxQvXhwAsHnzZtStWxdLly7FunXrpPZkJjk5GcuWLcPdu3e/6P5MnToV9evXl9oJAM+ePYOtre0XnYeIiIiIiCgv8Ol7RPTNREdHY+zYsejUqRMeP34MtVqNvn37YuHChYiNjUVKSoo0ZW/GjBnYtWuXRv3AwMAsRymVKVNG4/WDBw/QunVrbNiwAcuWLcPDhw8BAGPGjMHIkSORmpoKOzs7pKWlZXgC3dSpUxEREYEJEyZAoVDAx8cHTk5O8Pf3R7t27ZCUlITU1FSkpqZCV1cXALB48WIYGhpi6NChqFu3LqpWrSqdr02bNihevDhKly6NsWPHwtXVFS4uLpg/f36G91GxYkU8ePAgw/7y5csD+L8n/6VTqVTo1asX6tevj169emV6bz6XlJSE0aNH4+rVqzh79iy2bduGSpUqISkpCYGBgRptJyIiIiIiyi8cKUVE34y5uTnu3r2LOXPmwMbGBkWLFsW5c+cQGhoKU1NTPHr0CDVr1gQAFC9eHO7u7hr1S5cunemT944cOaJRTq1Ww9XVFbNmzUK3bt1w5swZDBkyBGPGjEGBAgVQqFAhFClSBHZ2dnBwcECpUqXg5eUFADh37hz27NmDEydOSGtLlSlTBhcuXMDt27fRrVs3HDlyBBYWFqhcuTJsbGygUqlw4MABbNq0CTo6OhlCHR0dHVSqVAlKpRLr169HWFgYXF1dM71HAQEBUKlUWW6fr2M1ffp07Nu3D6dOnYKVlVWOPwMvLy/Y2dnh7du38PX1hbW1Ne7evQtnZ2c0adIEAwcOhLW1dY7nISIiIiIiymsykb7QChFRPlKpVIiPj88QymTl5cuXsLOz+1vXiomJyXTh7rdv36JQoUKZ1lGr1RoLs/9TPXv2DB8+fECNGjW+6XljYmJgZmYGP79gVKvm8E3PTf9tarUab968gZWV1b/i7xj9u7B/UV5h36K8xP5FeUlb/Sv994fo6OhsH5rE6XtE9I+gUChyHUgB+NuBFIAsPxSzCqQA/Gv+QVCyZMn8bgIREREREVGu/Dt+yyIiIiIiIiIiou8KQykiIiIiIiIiItI6hlJERERERERERKR1DKWIiIiIiIiIiEjrGEoREVGuWVjo53cTiIiIiIjoO8FQioiIcs3c3CC/m0BERERERN8JhlJERERERERERKR1DKWIiIiIiIiIiEjrGEoREREREREREZHWMZQiIiIiIiIiIiKtYyhFRES59uFDYn43gYiIiIiIvhMMpYiIKNeiopLzuwlERERERPSdYChFRERERERERERax1CKiIiIiIiIiIi0jqEUERERERERERFpHUMpIiIiIiIiIiLSOoZS9I+hVqvzuwlal5aWBiFEfjcjT6WmpuZ3E74barX6u+8vRERERET038FQ6gtFRERAqVRm2L98+XIMHjw4V+d4/PgxwsLCclU2ISEBP/74Y65/EfX19UVKSkquyuaFa9euoVSpUtmW+fDhA5o0aYKnT59K+969e4eSJUtmWy8lJSXH4CoxMRHPnj3LfYOzsHnzZrx48eKrz5OTY8eOoUGDBl9cb9GiRejfvz8AoH///li0aFG25Tdt2oQPHz588XV69+6NJUuWfFGd1NRUyGQyxMXFAQDWrl2LyZMnf/G1nZ2dsXfv3hzLbd68GT4+Pl98/k99+PABMpksx3KXL19GQkJChv1Hjx6Fk5NTtnU3btyINm3afHHbGjRogKNHjwIAHjx4gDp16mRb/u7duzh69CgiIyNRsGBBqX1//fUX9u/fj9GjR39xG4iIiIiIiPICQ6mvEBERAX9/f/j7+yM0NBSvX7+WXgcFBWVaJzExEb1798bu3buRlJSEpKSkbAOn/fv348aNG5DJZJgxYwYaNmwImUwGmUwGT09PjbJpaWlYsGABGjdujOjoaGzfvl0qm9l26dIljfrnz5/PtnxkZOTX3zQA5ubm6Ny5M5ydnRETEwMAuHr1KmQyGby8vDS2w4cPS/XmzJmDwYMHa9wvlUqFJ0+eYOfOnRg4cCDs7OwwcuRIjdE5QgjMnj0b1tbWsLCwwJAhQ5CUlJRl+wICAjBkyBDs3r07V+9n165dWd6zGTNmZFt3//79fyuU+hJCCIwZMwahoaEAMv85W1tb5+pcn9c7efJkjnUCAwMRExOD+/fva2zBwcFSmeTkZOnvQ/qmVquhUqky7FepVBrnd3BwQJcuXeDj44OwsLBMfw7du3f/gjuWtZ07d6JVq1ZITU3FwYMHpfO3bdsWvr6+0mtHR8ccz+Xk5KTRxqlTp+ZY58mTJ0hLS8twL+/fvy+V0dHRwciRI7Fnzx4AwIYNGzBw4ECkpKTg5cuXSEtL+/s3gIiIiIiI6BtiKPUVPD094eTkhA4dOmD//v24e/cuOnToAGdnZ2kUS7pu3bpBJpPB0NAQt2/fxsSJE2FgYAADAwNcv349y2usWLECo0ePRnh4OLZv3w4fHx8IIbB79254eXlplNXV1cWJEydgZGSEXbt2oWfPnkhMTJS2jh07Yv369dLr+vXrZ7he9erVNeqkb7q6uhnKbty4McMv/3Xr1sWzZ88yDQbevHkj1R01ahT69u0rjTr5/fffYWNjg7Nnz0rbtm3bMGvWLKnOzJkz4e/vj/Xr1wMAjhw5AgMDA7Rs2RK7du1CsWLFcOHCBZw8eRJyuVyqt3jxYqxduxa7du3C+fPn4evrizlz5mR6v5OSkjB48GD06tULixcvzjJc/FzDhg0z3LPevXtnKBcSEqJxT7y9vfHLL79ker/SR7nkhlqtRmpqqrR9ys/PDwkJCRg2bBicnJwQHByM8uXLS+18/Phxrq8DAJGRkVCpVDmO2El34cIF7NixAw0aNJA2R0dH9OvXTypTpkwZ6e9D+nbmzBn07Nkzw/4ePXpI9ZKTk9GkSRN4enpi+PDhsLGxyfBzWLNmTYY2fR7YWlhYAMgYun3+d2zlypVQqVT49ddf0aFDB9y4cQNnzpzBggULULlyZZw5cwZnzpzBiRMncnVv9u3bB5VKleuRZBcuXMDDhw817mWDBg1QqVIlaRRhWFgYhg0bhrdv30KlUiEsLAxTpkxBQkICAgMDUaxYsVxdi4iIiIiIKK/Jcy5C6YoWLYpXr14B+PjLa/369TFu3DjMmTMH06dPx6hRo1CkSBGcPXs2Q+iRlpYGNzc3NGrUSGP/xIkTs7ze7t278f79e7i6usLNzQ39+vWTwpbOnTtjxowZ2L17N7p16ybVkcvlUlgDQGOqoY6ODhQKRabTD9O9efMmwwgsAJmO5ho0aBAGDRokHe/QoQMSExPx5MkTjBw5Ejo6Ohg+fDgMDQ016sXExECtVmPs2LEwNzfHvXv3cOzYMQQFBaFQoUJSuVOnTmmMHjEwMMDq1atx4MABAB8DiebNm2c7WketVmPJkiVYtmyZNL1q1qxZ2LJlS6bvsX///pDJZNi0aRNWrVqFFi1a4PLlyyhSpEiW1wA+3tvP72tmQZ69vT1iY2MBADNmzMCdO3dw7NixTM/56XSy27dva4RAQgj06dNHej19+nRMnz5dev306VNpGuWmTZvQrVs3aRre48eP8fLlS2n0UGbT0Zo3b44///wTALBjxw5MmjRJume6uroaod+nKlasiAcPHgAATExMULNmTQQFBSEiIkKj7504cQI///yzVC8kJATAx59pxYoVcfnyZVhZWWHixIlo3rw5nJ2dM71emTJlcPToUXTr1g116tTJMHVVqVRm2dbGjRvj4MGDAIDo6Gg4ODggKipKOv5paJZOR0cHa9eulQLD0aNHa4TKP/zwAwDAx8dH6m8LFizAzJkzpTIymUw6t46ODuRyeaZTB0eNGoVVq1YBANq2bQtbW1uo1WocO3YM9erVk+5lYmIiDA0NoaPz8f8Y5syZg+joaISEhCA1NRWnTp0C8HFa5aVLl3Dv3j24u7tL1wkODoaDg0Om94iIiIiIiCgvcaTUF3jx4gXCwsKgr68PlUqF4cOHo2zZsoiIiMAvv/wCIyMjAICVlVWG8AkA9PX1YWxsrLGl/yJ56dIljVE5YWFhGDlyJCpVqoQNGzbg9u3bmDZtmnRcV1cXW7duxYgRI3Dr1i0AwP3797F3714cO3ZMChru378Pf3//XL2/smXLYv78+ShYsGCGbdOmTTAxMcm0nkqlQp8+fRAZGYm5c+cCALp27YrAwECUK1cOx48f1yhfq1YtFC1aFBYWFnj//j169OiBuLg4WFlZSSNUDh48CB0dnQy/rNepU0dj/aTbt2/D2dk5w7Z06VIAH9fgiYyMRLt27aQ6vXr1wtmzZzXOm5CQgO7du0vr8fTr1w9du3ZFp06d4OjomOM9/HTq1qejoD4nk8lgbGyM6OhorF+/HqNGjZL6wvr16/H7779Lr9P7E/BxBFtcXJy0fRpyAICHhweEENKWHkhdv34d69evx+7du2FnZwc7OzukpqbC2toaS5YswZIlSzTCrHSnT5+GSqVCsWLF8PPPP0OlUmUa0nzOz88P586dA/BxnaZSpUqhZ8+eiI6ORtmyZaXRPFmNvlu1ahWaN28OKysrxMbGYsuWLWjVqpV0T7MKpx48eIASJUpkGFWVvq5VZuRyOczNzWFubg4zMzMAkF6bm5tDoVBkWq9y5cqwsbFBamoqzp07h9jYWOzZswcNGjRAbGwsYmNjUa9ePSnInT59uvR3pHXr1lCpVNi8eXOO93L58uWIjY2FgYEBDh06hBkzZsDExARNmjRBrVq1pPAvMTFR+hwBgLNnz6JgwYLw8PCAmZkZ6tSpA2dnZ3Ts2BH379/H0aNHoVKp0LRpU5w6dYqBFBERERER5RuGUl9AV1dX+kVaLpeja9eu6NChA86cOYPatWtDT08PSUlJ+N///qcx7Szd2rVr0aVLF43t7du3uH//Ptq0aYNr164BAP766y+0aNECZmZmePr0KebNmwd7e3solcoMU+U6duyIoUOHIjExEUeOHMG4cePQtWtXaarc3r17sX37dqkN6dO8Pl1XJn10UJEiReDm5pblln79Txe+DgoKQv369fHixQscPXoUFSpUwIEDB2Bra4vVq1fDy8sLAwcOxMSJE6VAIjAwUFro/a+//oKFhQUqVKiAU6dOITExEQ0bNgTwf1Op0llbW8PU1FRjWluRIkWkEVv37t1D3bp1MWjQIDRu3BgA8OzZMxQqVAgXL15EtWrVULx4cUyYMEFjRM2dO3fQoEEDvHz5EufOnUOhQoVw6tQpxMfHY8mSJRg1ahQaNWqE1atXZ7n+V506dXI1fS/d7Nmzoaenh06dOkn7QkND8ddff2VaXiaT4f3794iIiEBERESGsOXT6Xvp91mtVqNbt26YOHEixo0bh3HjxiExMRFKpRJ6enooVaoUSpUqBXt7+wzX09HRwZ07dxAaGorffvsNly5dytVC4Hp6erh58yYAYNy4cdDT08O8efNw7tw5lChRQgpPEhMTM4xgCggIwNKlS7F48WIAH0cYjRs3DtHR0UhMTMTw4cNRs2bNLK9ta2urEczl5M8//8x2+t6+ffs0yg8bNkwKDHfs2AGFQgEjIyOYmJigS5cuuHTpEkxMTGBiYgJ9fX2sWLFCOmdqaiqOHz+O69evY+nSpRohUlbkcjkePnyIxMREeHh4ICQkBMuXL8fLly/x/Plz6cEAn9/LLl26wNXVVRoJt2TJEoSHh2P27NkwNzdHYGAg5HI5Hj9+jMqVK2d5/eTkZMTExGhsRERERERE3xJDqb9BCAEvLy/UqFEDBgYG6Nu3L65cuZJhlMbni2mvW7cOKpVK2pKTk2FnZ4dRo0ZhyJAhUogRFRWFtm3bYvr06ShdujSePHmCw4cPIzExEaVLl8atW7eQmJiI4sWLY8iQIbh9+zYMDAzg7u6OsLAwmJqaZtn2gQMHQqFQoEqVKtK+zZs3Q6VSISEhAXK5HPfu3dNo5+fbhAkTAAALFy5EtWrVEBwcjEuXLsHS0hJmZmaoXr06FAoFFAoFmjZtCk9PTxw7dkyaRvSpihUr4vz585n+ki6E0PhlOyIiAnfv3pVep6amwsHBAa6urnB1dYWJiQkaN24MV1dXKbxIH1k0bdo0LFu2DFu2bMGhQ4ekNXxWrFiBBg0awNnZGb6+vpku+D1p0iQcPXoUK1euRK9evTK9r+lT0z7dMhsJBHxcLHvz5s1SyJe+gHdaWhpSU1Ol15+vDdW3b184OjrC0dER69at0zg2ffp06Z737NkTwMdgaePGjZgzZw50dHSkqWJCCDx8+FBqZ5kyZTJt588//wwdHR3Y2dmha9eu2Y46+lT6Atv37t1DoUKFUKRIEezYsQOurq5SmcTERGmKKfBxSqGzszMUCgWMjY1x8OBBHDp0CCkpKWjVqhViY2Oxf/9+dO3aNcvrvnr1SiNUyknTpk2lPp3+ZMJP+/mngSHwsa9ERETghx9+gEqlghACa9aswciRIzNsYWFhGDdunFR348aNiIqKgo2NDRYsWCAF0DlJv5cqlQo+Pj5o2bIlduzYARcXF43pe5/ey2PHjmHs2LFo06YN3N3doVAoMHDgQOzZswcLFy5EQEAAIiMjpRFzWUkfaZW+2dnZ5arNREREREREucVQ6gvEx8dj+/btSElJgbe3N06ePIn79+/DwsICUVFREEKgUqVK2L9/P4QQUCqViIiIwI4dOxAUFCQtPt6oUSOUKFECRkZGePnyJdzc3PDLL79I16lQoYIUCACAsbEx5HK5dL7//e9/UCqVSEhI0JjilRtbtmyBECLD07rkcrm0Bk3hwoU1Fs1O32Qymcb6N6VLl4aPjw8aN26MZcuWaYxSSd8qVKgAW1tb+Pn5oVWrVpm2KT28admyJQwMDHDx4kUAH0OnrKZQAcC7d+9yXOtJLpcjISEBy5cvh5OTE5ycnLB48WKsW7cOarUaQ4YMQWBgIH766adsr9WoUSMEBARg2bJlGY4JIaCjo5PhfmU2Wufy5ctwc3OTAsjTp09LIaanpycWLlwovf58XbKzZ89KI6WmTJmicezT6Xu7du2S9l+6dAkGBgb49ddf8euvv0KhUODUqVMoVqwYDh48CF1dXezevRubN2+WpoMBwKFDh3D58mU4OzujU6dOqFGjRq4WRPf19UV0dDQAwMvLC1evXsWdO3dw/fp19OrVCxMmTMCNGzcQExMjTQe9desW6tevDzc3NwDAgQMHMGTIEERGRmLUqFEoUaIEKleuLG1ZsbW11QiVcpLen+VyucYIyPTt82Arffrtp2Fj+lS4mjVrStuePXvw9u1bqcyLFy8wa9Ys9OvXD8WKFcOYMWM0wtWsREVFwcvLC8WLF8esWbOgp6eHJ0+eYOXKlRgxYgT++OMPeHp6atxL4OPf58TERDx48ABDhw4FABQrVgzDhg1Dr169cOLECRw9ejTTBx18yt3dHdHR0dL28uXLHNtMRERERET0JRhK5VJqairatGmDU6dOQV9fHz4+PrCxscHs2bMxcuRImJub49y5c3jz5g1cXFykegqFAjNnzoShoSHs7OxQqVIljB49GuPHj4eZmZnGosc5CQ8Pl6YHAR/X7LG0tMyyfG6mMH3q0aNHAD6uifX5qC8DAwMsX75co3zXrl2lEUnjx4/P9Aly6UHHpyM5spL+ZMH0qXepqanQ19fPsvzly5dRqVKlbM+ZPhLk09FAZcuWRVJSEt69ewcDAwMULVo0x7YBH0OJwoULZ9j/8uVLXLhwQRqplL5t3bo1Q9mzZ89i4cKF0kgtZ2dnKUwaO3YsZs+eLb1esGCBRt3ExETcvHkTmzZtwpkzZ3DgwAHpyXKfP30v/Wc/Z84cCCEwYcIETJgwAUIIODs7w9jYGM7OzqhatSosLCywc+dOaVpm+tPgvL29pb524sQJaZ2qz8O79GslJiZi1KhRmDRpEgDAwcEBly9fxqRJkzBhwgQ8f/4cb9++xdixY/HmzRspUCxbtiy8vb2lEV5+fn44dOgQ2rVrhyFDhqBnz56IiYmRpsNl59NQKSefTt9Lf5/ZTd/Lyvbt26XpkePGjUNkZKTG8ZkzZ2L06NGoXbs2gI8jDIcMGYIPHz5keS+Bjw9BaNGiBYoVKwYdHR1cvHgRe/bsQdWqVWFjY4OwsDBMnToVjx490ghn05/ymZSUBBMTE2lq7oQJE2BqaoqqVati1qxZ0v3Oir6+PkxNTTU2IiIiIiKib4mhVC7J5XKcO3cO27Ztk/YFBwfj6NGjOHnyJH755Rf07dsXHh4e0NPTk8oUKFAAz58/x5UrV+Dt7Y0+ffrgxIkTmDt3Ln799dcvejz7nj17pClQ79+/R3JyMgoUKJBp2b1790rhTm69efMG3bt3z3TEU40aNbIdldS/f3/s2bMnw5abKT/pTzxr0qQJZDIZfH19AQBJSUlZjgSLiIjAoUOHNALAzFStWhUKhQJ+fn7SvkePHsHExERjbaqvcfToUYwfPz7DPcssbJw9e7Y0/fFLtGnTBiYmJujcuTOOHDmCV69eoVu3btKIq0+n7ykUCmmh95y0b98eLi4u0lQ04GPo5O/vjxYtWmiUPXXqFMzNzTP8TFatWgVvb29ER0ejRYsW0hMZgY8/p+vXr2P27NlwcXFBZGQk7t69izt37sDW1hbAx5GAbdq0kerMnz8fdevWxbJlyxAYGIg2bdpgw4YNKFeuXLbv5Uum7xUtWhSTJ0+W1v9KD5Li4uKkfdu3b9eY5pqVdevW4cOHD9JWvHhxjePr16+XHgDwaVsfPHiQIRD19/fH4MGDkZaWBhMTE6xcuVLj+OHDh3Hq1CnUqlULBw8eRKFChbBt2zbpXgIf/944ODjg999/R3JyMuzt7XHx4kVpQfP27dvj1atXaN68eY7vjYiIiIiIKC8xlPoCn/+iW7x4cURHR6N169aYMmUKIiMjceHChQzTXIQQ8PHxQZcuXVC+fHkolUo8fvwYffv2zfW14+LisG7dOukX/ufPn6Nw4cIaARjwcbSKSqXCwYMHNaYE5sawYcOwc+fOTI+FhoZKCyt/rnfv3ihTpgyCgoIybCNGjMg0eEtfaL1t27ZwdXWFi4sL/P39kZiYiHnz5sHOzg5RUVEwNzeX6vz1118YM2YMEhISMHDgQAwYMEDjyWFVq1aFqakp1Go1oqOjkZycDHNzc7i5uWHcuHG4evUqbt26BXd3dwwfPjxX6w7l5OTJk7hy5YpGEJMXVq5cifDwcISGhmLhwoV4+/Ythg4dKo3w+fzpexMnTsxwjpSUFKxevVpjmt7AgQNhZGSEfv36SWsUARlHtsXHx2PGjBkZQsC0tDT4+voiKSkJ1tbWWLp0aYbF6S9cuICHDx/ir7/+wvHjx7FgwQIEBwdLI6+y4u3tjfDwcJQtWxYrV67Ew4cPsyyrq6uL//3vfxrT94yMjLL8GTs5OeHnn3+W1tVKH5Gnr68v7evVqxeqVauW5TUDAgIAAEOHDtV4al9wcLBGuU/va7qJEyfC2tpaYzpiWloarly5gri4OOjq6mL58uUZQueDBw/iwoULeP/+PS5cuAAPDw8oFAqNe/nhwwf07t0bM2bMQJkyZVC0aFE0aNAAABAZGYnffvsNhQoVyrSPEBERERERaVPOc1xIkpKSgpSUFMjlcrx79w4nT56El5cXnjx5giNHjqBGjRqYPXs2KlSoAA8PD4wYMQLu7u7YsWMHVCoV3Nzc8PTpU9ja2uLRo0eIiYmRfgHNTlJSEgYOHIh+/fohKioKr169ws2bNzNdX8fAwAB3795FeHg46tatK+1PHzmzb98+ae2efv36wcvLK8vrXr16FZaWlggNDUVMTEyGqXKjRo3CqlWrcrxv7u7uAD5Oz3NycgIAac0dIyMj7Ny5E8bGxlL5zp07w9LSEitWrJCCsHv37qFRo0YYPXo0vL29MX/+fPzxxx/YsmULTE1NoaOjg5SUFBw5cgSJiYmQyWR48eIFihYtit9++w1TpkxBmzZtoFKp0L17d8ybNy/Hdufk6dOn6NevHyZOnIjy5ctL+9+/fw8hBOLj43MMvjp06IBDhw5p7Pt0VE36PStRogRCQkKwefNmLFu2DB4eHqhRo0au2/ru3Tt4e3ujQ4cOaNu2Ld69e4c+ffrg6tWr2L59O1xdXdG/f3+MGDEiw8LnaWlpaN++PV68eIHDhw9L+8uWLYs2bdrAxsYm237wabBz6tQpdOjQAbNmzUL16tUzLX/z5k2MGzcO4eHhOHfuHKpUqYL58+ejRo0a0tMmGzVqpHFvy5Urh8DAQI3zpC/MLoT4JgFkusTERMyePRupqamoVq0a1q1bh969e0vXKVGiRLbXmzt3Lnbt2oWNGzdqrM82dOhQKBQKjXv8OVtbW2lU1I0bN6Trf9oXjIyMUK5cOcjlcjg4OMDPzw+LFy9GkyZNpDXtFixYAEdHRwwcOBBr167Ndj01IiIiIiKiPCMo15YuXSrkcrkYNGiQ2Lp1q2jSpInw8vIS8fHxGuVOnjwpunfvLpKSksShQ4fEiRMnRGpqqkaZWrVqCQCiSpUqIi4uLtPrbdmyRbRp00Z07NhRbNiwQQghxKpVq0SBAgWEhYWFOH78eJZtVavVIjExMdstJSUl2/c7c+ZMYWhoKMzNzcXSpUtzc4u+yKlTpzLdv3XrVqGvry9KlSolQkJCpP0BAQEZyqpUKhEfHy9iYmJEVFSUePfunYiMjBTR0dFf1bYCBQqIp0+fZnk8Pj5e2Nraiq5du4q0tDSNY1OnThUAhIGBgTh27Fim9e/duycKFy4sUlJSsv0ZpZ87KSlJ1K9fXwwePFjcv39f41z9+vUTHh4e2b6fmzdvilu3bgkhhPDx8RF16tQRK1euFK9fvxZCCPHw4UPRo0cP4evrq1GvW7duYvHixaJ3795i3bp12V7jUwBEbGxshv0///yzMDY2Fn379s30ntjb24vx48cLd3f3DH8vQkJCxLRp04Szs7PUd+3t7cW9e/eybEefPn2EhYWFWLVqVbbtjY2NFQCESqXK8b2tXr1aLFy4UERHRwsXFxexbds2IYQQ3t7eQl9fX9jb24sPHz5kqLdmzRrh4uIiNm7cKHr27CnUanWO1xJCiMaNG4sjR45k2H/u3DlRqFAhUalSJRETEyOEEOLt27eiQIEC4ocffhBnz54VQgjx9OlTsW3bNmFhYSGWL18u1Q8LCxN169YV/v7+uWpHdHS0ACD8/IJzVZ4ot9LS0kR4eHiGz1Kib4H9i/IK+xblJfYvykva6l/pvz/k9Lu5TIgvXA2biAB8fGpc9erVpackfs9SU1NztXj4P5H4xiOlPhUUFARLS8tsHziQmby8nwkJCTA0NMywPz4+/ouf1vmpmJgYmJmZwc8vGNWqOXxFC4k0qdVqvHnzBlZWVv+Jz1PSLvYvyivsW5SX2L8oL2mrf6X//hAdHZ3tQ5P+nb9lEv0DpD958L/g3xpIARnXgvuWcloXKyt5eT8zC6QAfFUgRURERERElBcYuxIRERERERERkdYxlCIiIiIiIiIiIq1jKEVERERERERERFrHUIqIiIiIiIiIiLSOoRQREeWahYV+fjeBiIiIiIi+EwyliIgo18zNDfK7CURERERE9J1gKEVERERERERERFrHUIqIiIiIiIiIiLSOoRQREREREREREWkdQykiIiIiIiIiItI6hlJERERERERERKR1DKWIiCjXPnxIzO8mEBERERHRd4KhFBER5VpUVHJ+N4GIiIiIiL4TDKWIiIiIiIiIiEjrGEoREREREREREZHWMZQiIiIiIiIiIiKtYyhFRERERERERERax1CKiCif7NixA+Hh4bkqq1KpEB8fDwDo378/Fi1aBAB48+bNF183OZmLlRMRERERUf5jKEVE9AV69+4NmUyW5TZo0CCN8k5OThrHf/75ZwDAq1ev4Obmhjt37mR6nbS0NKSmpiI1NRVpaWlYvXo1nJycEBkZKZURQqBx48a4ceNGpudITU3Fmzdv4Ofnh71792LGjBlo1qwZLC0tceLEiW90R4iIiIiIiP4ehlJERF9o2bJlSExMzLClj1763P79+/HixQuYmpqiefPmAIDBgwejePHimDFjBhITEzPUadasGRQKBRQKBUqWLIkxY8bgf//7H6ZPny6VWbp0KUxNTVG7dm2NusHBwTAwMIBCoUCJEiXQrVs3bNq0CYmJiRg6dChCQkLQqlWrb3hHiIiIiIiIvpw8vxtARPRvI5fLERQUhIoVK0r7pk+fDn19/UzLKxQKzJ8/H507d0b16tUxZswYBAQE4N69exg9ejQ6dOiAvXv3wsTERKPegQMH4ODggA4dOkAmk2HlypVwd3cHAERGRuLly5c4dOhQhuulpaXB0NAQb9++hVKpzLRNarUaOjr8fwkiIiIiIso//I2EiOgLhYWFoUWLFti/fz8A4Ny5c9i7dy+MjIwyLR8UFIQtW7agd+/eaNGiBU6cOIHz58/D0tIS3t7e+N///odatWrBx8cny2s+fvwYBQsWxIYNG+Dt7Y2lS5fijz/+QJEiRZCUlJSh/Pv372FiYiKNtvp8a9Gixbe5GURERERERH8TR0oREX2hokWL4ujRo3B2doaZmRmmT5+OSZMmZToNDwBKlSqFtm3bYtu2bbC2tsa5c+dQrlw5jTJ169bF2bNn0aRJkyyva2VlhcDAQOl1WloaChYsmGnZAgUKaKw/BQCLFi1CWFgYPD09c3yPycnJGguix8TE5FiHiIiIiIjoS3CkFBHRF5LJZKhevTq8vLzg4uKClJQUuLm5QV9fHykpKZnWWb16NQ4cOIBFixbh0aNHmDdvHq5cuYK4uDi8fPkS/fr1w8KFC3O8rlKp1NjyioeHB8zMzKTNzs4uz65FRERERET/TQyliIi+QHR0tDRNr2rVqjA0NIShoSFkMhlMTU0RGxubab0iRYqgUaNG+Omnn+Ds7AxTU1MMGzYMCoUCfn5+2LJlS47Xfv36NQwMDKTN2NhY47gQQnpaHwDp6X3pm1qtlsqkv86Ku7s7oqOjpe3ly5e5vUVERERERES5wul7RERfIDw8HIULF0ZKSgq6du2KCRMm4Pfff8fatWtRrVo1PHv2LEOdkJAQ7N27FwEBAZgwYQKePXsGXV1dAMCxY8fw559/omXLlhnqrVq1SmPx88KFCyMsLEx6nZaWpjFaytfXV2P6n0KhyPQ9rF69GgAwZcqULJ8YqK+vn+XC7URERERERN8CQykiolyKi4vD3bt3Ub16dQwYMACWlpaYNm0a6tati27duuHmzZsIDAxEXFycxigmtVqNVatWYcKECWjcuDFCQkKwZcsWuLu7Q6FQYP/+/fD19c1wvbZt28LOzk5aR+r169dZBk0A4OTkBCGE9DopKQkqlQomJiZIS0vD06dPUbZs2W94R4iIiIiIiP4+Tt8jIsqlbdu2oUqVKli8eDGePHmC33//HTKZDE2bNoWdnR3Onz+PEiVK4NixY7h//75Ur0SJEvDx8cGYMWMAAM2bN8fy5cvRpUsXvH79Gg0aNECJEiU0rnX+/HmMGTMGHTt2xIMHDwB8HCklhJA2lUqVZVsfP36MJk2a4OeffwYAnDp1Cg0bNkSZMmXg6emZbV0iIiIiIiJtYChFRJQLb9++xYIFCzBu3DiMGzcOx48f1xgNtWPHDvTs2RNDhw7FggULULt2bSQlJWmcY+/evZDJZChatCiqVasGmUwGNzc37Nq1CzKZDOPGjct1e4QQ0vpVOjofP8ojIiKwe/duuLq6onbt2mjdujXmzZsHAGjdujUiIiKwdOlSbN26FRUqVMCVK1e+8q4QERERERH9fZy+R0SUC2fPnkX16tXRs2dPyGSyDMfLlSsHABg5ciSOHj2KypUrZ3g6XufOnbMdoZQeLuXGtm3bMHDgQLRt2xZ6enpSG9etWwcXFxesWrUKhQsX1qijq6uLNm3awMXFBb/99hsOHz6MevXq5fqaRERERERE35JMfLoACRERZUmtVn9RcPQ9iYmJgZmZGfz8glGtmkN+N4e+I2q1Gm/evIGVldV/9u8X5R32L8or7FuUl9i/KC9pq3+l//4QHR0NU1PTLMuxhxMR5RL/UUBERERERPTt8DcsIiIiIiIiIiLSOoZSRERERERERESkdQyliIiIiIiIiIhI6xhKERERERERERGR1jGUIiKiXLOw0M/vJhARERER0XeCoRQREeWaublBfjeBiIiIiIi+EwyliIiIiIiIiIhI6xhKERERERERERGR1jGUIiIiIiIiIiIirWMoRUREREREREREWsdQioiIiIiIiIiItI6hFBER5dqHD4n53QQiIiIiIvpOMJQiIqJci4pKzu8mEBERERHRd4KhFBERERERERERaR1DKSIiIiIiIiIi0jqGUkREREREREREpHUMpYiIiIiIiIiISOsYShHRP9arV69QsmTJHMs1b94cu3bt0kKLPnrw4AGio6MBADExMTh06JDWrk1ERERERPS9YChFRPkiJCQEMpksyw0AhBBIS0v7ZtcsWrRotteUyWSYOnWqVH7UqFEZjvfv3x9Hjx5Fjx49AABr167F1q1bs71uQECA9OcPHz5g+/btmZabM2eOxrV0dXVhYWGBTp064a+//gKQ+X3T1dWFlZUVRowYkeF+PX36FD169EChQoWgVCpRpUoVrFmzBkKIv3UPiYiIiIiIvhV5fjeAiP6bHBwcoFKpMHHiRI39VlZWmDx5cpb1ZsyYgYULF2bY/+eff0pBUbqnT5+iVKlS0uuHDx9CrVZn2y6lUin9efny5fDx8cGaNWsgl8vh5uaGDRs2QC6X4/bt27h79y5WrFiBq1evZnm+q1evon379ggLC4Oenh5SU1MxaNAgNG3aFDY2NhnK16tXTxp5pVar8erVKwwYMADdu3fHhQsXpHJ+fn6ws7MDACQlJcHHxweDBw+GnZ0d3N3dAQD+/v5o0qQJWrdujVOnTsHKygq+vr748ccfcfv2bWzcuDHbe0FERERERJSXOFKKiPKNXC7HihUr0KNHD/Tu3Ru1a9fG1q1bIZfL4eTkBDs7O7x48UIaoZRu4sSJSExMRGJiIhISEuDt7Y34+HhpX2JiImxtbTNcz9TUFObm5tlun4ZScrkcMpkMgwYNgpubG2QyGRYuXAgdHR3s2bMHVapUwV9//QV7e3sYGxtnuF5SUhKGDBkCd3d36OnpAQAKFiyIfv36YdasWZneE4VCgYIFC6JgwYKwsrJCtWrV4O7ujosXLyIqKkoqZ2FhIZUrWrQo+vTpg169emH37t1Smf79+8PZ2Rk7duxA9erVUbRoUfTq1QtHjx7Fpk2bcPjw4S/+mREREREREX0rDKWIKN85OjqiVq1a6NWrl8b+nTt3wt7eHvv375f21atXD40bN4ZSqYRSqcTZs2cxd+5c6XX6NmjQIJibm3+T9k2ZMkUafTRr1iyoVCo8ffoU1tbWUKlUUKlU0hpT6ZKTk+Hq6gobGxuMHTtW49iiRYtw5swZLFq0KFfXl8lk0NHRgb6+frblDA0NoVAoAAA3b95EQEAApk2blqFcrVq14OLigrVr1+bq+kRERERERHmB0/eIKN8kJSXh4sWLGvuUSiVUKpXGvvQ1pgCgZcuWEEIgNTUVALB06VKMGjUKarVaY2rejBkzoKPzMXc/f/48mjRpkut2NWvWDGfPnpVe6+npQVdXFwCgo6MDHR0dyGQy6OvrQy7P+DEaHByMnj17Qk9PD4cPH87QNhMTE5w9exatW7fG+fPnsXr1apQoUSLTtgQFBWHRokVo0aIFDA0Ns2zzjRs3sGPHDmlNrJs3b8LIyAgVK1bMtHyNGjWwZs2aLM+XnJyM5ORk6XVMTEyWZYmIiIiIiP4OhlJElG9cXV1x4sQJqNVqKXiSyWSYO3dulnVKliyJFy9eaOxLXyfpc1OmTMGiRYvQoEEDjalvAQEBaNu2LR49egQjI6MM9dJHG6VLX4BcLpejQYMGuHz5snTs08DsyJEjaNOmDaKiotCgQQP06dMny9Faurq6iIqKwty5c2FiYiLtv3TpklQnJSUFKSkpaNGiBTZt2qRRv3LlylLolpycjKJFi2Lq1KmYMGECACAqKgrm5uYa7ftUkSJFNO7J5zw8PLL9ORAREREREX0tTt8jony1b98+FCtWDJcvX0bjxo0xc+ZM6diBAwcQHR2NPXv2SPtCQkIghIBarUbNmjXx66+/QggBIQQePnwIMzMzJCQkQAghTY+Ty+Ua60Z5eXlhwIABsLW1hbm5OXx9fREUFCQd/zyo2rFjB3bt2gXg46grlUqFrVu3onbt2ihfvrw0hc/FxQUAUL16dSxevBiVK1eWniBoZmaGhw8fQgiBwMBAWFpawsTEBEuWLEGhQoWka9WsWRP+/v7w9/dHYGAg4uLicPz4cRQpUkSjTcePH4e/vz+OHDmC4sWLo27duhg3bpw0osvExARv377NcmH3iIgIWFhYZPlzcXd3R3R0tLS9fPkyx58lERERERHRl2AoRUT5asaMGejbty9q166NokWLSk+EmzRpEmrWrAmZTIYmTZpgwIABGvXWrl2L+Ph4jB49Wtr3888/Y8CAATAwMMjyek+fPsX+/fs1RlalpKSgZcuWGk+3+1RMTAyuXLmC0NBQODo6Qi6Xw8/PD61atcK7d+8QFRUlLYqeGX9/fygUCpQtWxYAEBkZiYIFC2ZaVqlUwsHBAQ4ODrCzs9NYeP1TRYsWhYODAxo2bIjDhw/j4MGDmDJlinS8Tp06SElJQUBAQKb1b968iZo1a2Z6DAD09fVhamqqsREREREREX1LDKWIKF+VL18eixcvhpGREfbs2YNy5coBAFxcXNCjRw8YGhpCT08P/v7+Up3ExESsX78ez549Q4sWLbB06VL8/PPPOHnyJKZPn57t9SZPnowff/wRxYoVk/Z16dIFmzZtQseOHfH48WNp/7JlyxAaGopWrVph7dq1sLS0xK5du6BWq7Fv3z64uLigVatWUpCWlbVr16JTp05SaBUREYECBQp86a3KUqlSpTB//nysWLECV65cAfAxlKpevTrmzJmTofytW7dw/PhxDB069Ju1gYiIiIiI6EsxlCKifDVgwAD4+/ujbNmySEpKQv369TWOq9VqyOVyeHh4SFPRDAwMcOfOHTx58gTt2rXDzJkz4e7uDktLSxw/fjzDQunAxyf5zZkzB4cOHUJERAT69++Pdu3aoWHDhqhQoQKGDx+O6Oho9OjRQ1pEvVq1ali6dCnCw8OxdetWGBsbo1SpUlizZg2srKxQq1YtDB06FL/88gvCwsIyfX9XrlzBrl27NMKhZ8+ewdbW9hvdwY9GjhyJ8uXLY/To0dJ98vb2xsWLF9GnTx8EBATg1atX+P3339G2bVsMHDgQ7du3/6ZtICIiIiIi+hIMpYgo3ykUCtjY2AD4+ES+27dvY9y4cWjXrh3Cw8MxePBgREdH48GDBwAAlUqFa9euYe3atVi0aBHatWuHly9fYsaMGVi2bBlKlCiBLVu2aFzD19cXDx48wPDhw1G4cGHUrl0bffr0wfz587Fnzx7cvXsXCQkJACCNfHJycsLgwYNhZWUlnWfPnj2YOnUqNmzYAABwdHSEq6srOnXqhA8fPmhc89ChQ2jTpg28vLzw/PlznD17Fi9fvsSBAwdQrVq1b3oP5XI5VqxYAT8/P6xfvx4AULFiRdy4cQOpqalo3rw5SpYsiUWLFmHWrFlS+4mIiIiIiPILn75HRPmuZMmSOH78OGrXro3g4GD89NNPCAwMxPjx41GtWjWUK1cOo0aNwrt379C+fXucPHkSNjY2aNOmDc6ePYuKFSsCAHr27ImePXviwIEDmDp1Kho1aoSSJUsC+DiFLje8vLykgCwzvr6+2LNnj0ao5OnpiQEDBuDatWtwdnZGcHAwunXrhnfv3mH37t344Ycf4Ovri5EjR+LFixcoUaIEevbsmeHcmU21+5yDgwOEEJkea9asWYZjpUqVws6dO3M8LxERERERkbbJRFa/3RARaZlKpcp2wXDg4yLh6U/Ty45arYaOTv4MBhVCSKOk5PLvI/uPiYmBmZkZ/PyCUa2aQ343h74jarUab968gZWVVb79naXvF/sX5RX2LcpL7F+Ul7TVv9J/f4iOjs72oUnfx29LRPRdUCgUOZbJ6ql1n8vPL3CZTIYOHTrk2/WJiIiIiIj+DRi7EhERERERERGR1jGUIiIiIiIiIiIirWMoRUREREREREREWsdQioiIiIiIiIiItI6hFBER5ZqFhX5+N4GIiIiIiL4TDKWIiCjXzM0N8rsJRERERET0nWAoRUREREREREREWsdQioiIiIiIiIiItI6hFBERERERERERaR1DKSIiIiIiIiIi0jqGUkREREREREREpHUMpYiIiIiIiIiISOsYShERERERERERkdYxlCIiIiIiIiIiIq1jKEVERERERERERFrHUIqIiIiIiIiIiLSOoRQREREREREREWkdQykiIiIiIiIiItI6hlJERERERERERKR1DKWIiIiIiIiIiEjrGEoREREREREREZHWMZQiIiIiIiIiIiKtYyhFRERERERERERax1CKiIiIiIiIiIi0jqEUERERERERERFpHUMpIiIiIiIiIiLSOoZSRERERERERESkdQyliIiIiIiIiIhI6xhKERERERERERGR1jGUIiIiIiIiIiIirWMoRUREREREREREWsdQioiIiIiIiIiItI6hFBERERERERERaR1DKSIiIiIiIiIi0jqGUkREREREREREpHXy/G4AERH98wkhAAAxMTHQ0eH/Z9C3o1arERsbC6VSyb5F3xz7F+UV9i3KS+xflJe01b9iYmIA/N/vEVlhKEVERDl69+4dAMDe3j6fW0JERERERP8WsbGxMDMzy/I4QykiIsqRpaUlACA0NDTbLxWiLxUTEwM7Ozu8fPkSpqam+d0c+s6wf1FeYd+ivMT+RXlJW/1LCIHY2FjY2NhkW46hFBER5Sh9aK+ZmRn/cUR5wtTUlH2L8gz7F+UV9i3KS+xflJe00b9y85/ZnKBKRERERERERERax1CKiIiIiIiIiIi0jqEUERHlSF9fH7Nnz4a+vn5+N4W+M+xblJfYvyivsG9RXmL/orz0T+tfMpHT8/mIiIiIiIiIiIi+MY6UIiIiIiIiIiIirWMoRUREREREREREWsdQioiIiIiIiIiItI6hFBERZSsxMREDBgyAiYkJChcujJ9++im/m0T/YEeOHIFMJtPYXF1dAQB+fn6oWbMmlEolqlWrhuvXr2vU3b59OxwcHGBoaIi2bdvi9evX0jG1Wo3JkyfD0tISFhYWGD9+PNLS0rT63ih/pKSkoHz58jh//ry07/nz52jSpAmUSiXKlCmDo0ePatQ5ffo0ypUrBwMDAzRq1AhPnjzROL5kyRIUKVIExsbG6Nu3L+Li4qRj/Mz7b8msf02YMCHD55inp6d0/Gs+qyIjI9GxY0cYGhrC3t4emzdv1sr7JO0JDg5G27ZtYWZmhuLFi8PDwwNqtRpA3n4Psm/9N2TXvzp37pzhs+vT78ev+e7L6Xv3qwgiIqJsjBw5UtSoUUM8fPhQnDlzRpibm4vff/89v5tF/1CLFi0SLi4u4u3bt9IWExMjYmNjReHChcX06dNFSEiImDp1qihYsKCIiYkRQghx7do1oVQqxb59+8TTp09FixYtRIsWLaTzLl68WDg4OIjbt2+La9euCXt7e/HTTz/l19skLUlKShKurq4CgPDx8RFCCJGWliYqVaok3NzcREhIiPjtt9+EUqkUz58/F0II8eLFC2FoaCjWrFkjgoODRd++fUW5cuVEWlqaEEKIPXv2CAsLC+Hj4yPu3bsnqlWrJoYMGSJdk595/x2Z9S8hhHB2dhY//fSTxudYYmKiEOLrP6tcXFxEq1atxLNnz8Tu3buFnp6euHz5stbeM+Wt5ORkUaFCBdG7d28RFBQkTp48KQoVKiRWr16d59+D7Fvfv+z6lxBClC1bVmzbtk3jsyslJUUI8XXffTl9734thlJERJSlhIQEoVQqNf6xPnfuXNGwYcP8axT9o/Xr109MmjQpw/7NmzcLBwcHoVarhRBCqNVqUaJECbFp0yYhhBB9+/YV/fr1k8qHhIQImUwmnj17JoQQwt7eXmzZskU67u3tLezs7PLujVC+e/DggahataqoWrWqRmhw7tw5YWhoKOLi4qSyTZo0ETNnzhRCCDFr1izRuHFj6Vh8fLwwMjISf/75pxBCiEaNGonZs2dLx319fYW+vr6Ij4/nZ95/SFb9S4iPnzfHjh3LtN7XfFY9f/5cABDBwcHScTc3N9GnT59v9r4of124cEHo6+uLhIQEad/ChQtF/fr18/R7kH3rvyG7/pWSkiLkcrl48OBBpnW/5rsvp+/dr8Xpe0RElCV/f3+oVCrUq1dP2le/fn3cvHkTQoh8bBn9Uz18+BClS5fOsP/atWto0KABZDIZAEAmk6FevXrS1IVr166hYcOGUnl7e3vY2tri+vXrCA8Px4sXLzSO169fHy9fvkR4eHgevyPKLxcuXECTJk1w6dIljf3Xrl1D9erVYWRkJO2rX79+ln3J0NBQmiYjhMCNGzc0jjs6OiI1NRUBAQH8zPsPyap/xcfHIzQ0NNPPMeDrPquuXbuGokWLwsHBQeP451O46N+rbNmyOHLkCAwMDKR9MpkM8fHxefo9yL7135Bd/3r69CnUajVKliyZod7Xfvfl9L37tRhKERFRlsLCwlCwYEHo6elJ+2xsbJCUlIR3797lY8von+rx48e4cuUKKlWqhP/973+YNWsWVCoVwsLCYGtrq1HWxsYGr169AoBsj4eFhQGAxnEbGxsAkOrT92fYsGH49ddfNf4RDGTfV3I6HhkZiaSkJI3jenp6KFCggNTX+Jn335BV/3r8+DGEEFi1ahWKFy+OGjVq4Pfff5eOf81nVU59l/79ChUqhB9++EF6nZycjM2bN6Np06Z5+j3IvvXfkF3/evToEczNzTFq1CgUK1YM9erVw5kzZwDgq7/78rp/yb/JWYiI6LuUlJQEfX19jX3prxMTE/OjSfQP9urVK8TGxkJHRwdeXl4IDw/HiBEjEBMTk2VfSu9H2R1PSkqSXn96DGA//C/61n3p8/r8zPtve/z4MXR1dWFnZ4fDhw/j0qVLcHNzg6GhITp06PBVn1U59V36vqSlpaFXr1549+4dJk6ciN69e+fZ9yD71n/P5/1r8+bNSEpKQt26dTFmzBgcPnwYrVu3xtWrV1G4cGEAf/+7L6/7F0MpIiLKklKplP4hlC45ORkANIYOEwEf/wc3JiYGJiYm0r6UlBT06tULTk5Omfal9H6UVV8zMDCAUqkE8PEf7Onl2Q//u5RKJSIjIzX25aYvmZqaavSlzOoLIfiZ9x/Xq1cvtGvXTvocq1SpEh49eoTVq1ejQ4cOX/VZlV1d+r6o1WoMGDAAR44cwfHjx1GkSJEcf/7sW5RbmfWvadOmYezYsTA2Ngbw8bPr+vXrWL9+PRYuXAjg73/35fS9+7U4fY+IiLJka2uLd+/eQaVSSftevXoFpVKJAgUK5GPL6J/q00AK+Lj+QVJSEooUKZJh/adXr15Jw8FtbW2zPJ5e5tPj6UPGPx9OTt+/7PpKTscLFCgAfX19jeMpKSmIjIyU+ho/8yizz7G//voLwNd9VuXUd+n7kJaWht69e+OPP/7Avn370KxZMwBf99nFvkXpsupfMplMCqTSpX92fe13X173L4ZSRESUpapVq0Iul+PKlSvSvosXL6J27drSQp1E6Xx8fGBmZoa4uDhp3507d1CwYEE0aNAAly5dkhaLFkLg8uXLcHR0BPBxwc2LFy9K9YKDg/Hq1Ss4OjrCxsYGdnZ2GscvXryIYsWKoUiRIlp6d/RP4ejoCD8/P8THx0v7Ll68mGVfio+Px507d+Do6AgdHR3Url1b4/jly5ehp6eHqlWr8jOPMG/ePDRu3Fhj3507d1CuXDkAX/dZVadOHYSFhSEkJETjeHrfpe/D2LFjcfDgQRw9ehRt2rSR9js6OubZ9yD71n9HVv1r0KBB6NOnj0bZ9M+ur/3uy+l796t9k2f4ERHRd2vIkCGiRo0a4v79++LcuXPC3Nxc7N69O7+bRf9AcXFxws7OTvTo0UMEBQWJM2fOCFtbW7F06VIRHR0tChYsKKZPny5evHghpk+fLqysrERsbKwQQohLly4JfX19sXfvXvHs2TPRqlUr0apVK+ncP/30k3BwcBA3btwQt27dEg4ODuLnn3/Or7dKWgZAelR1WlqaKFeunHBzcxMhISFi9erVwsDAQISEhAghPj4aXalUilWrVomQkBDh5uYmKlasKNLS0oQQQvz+++/C3Nxc/Pnnn+Lhw4eiRo0aYvjw4dK1+Jn33/Np/7p165aQy+ViyZIlIiQkRKxfv17o6+uLGzduCCG+/rOqRYsWolWrViIoKEjs27dP6Ovri6tXr2r1/VLeuXbtmgAgPD09xdu3b6Xt/fv3ef49yL71/cuufx06dEjI5XKxbds2ERwcLBYuXCiMjIxEaGioEOLrvvty+t79WgyliIgoW7GxsaJPnz7CyMhIFC5cWPzyyy/53ST6B3vw4IFo0aKFMDY2FtbW1mLu3LlCrVYLIT7+Y6pq1apCT09PVK9eXdy8eVOj7qZNm4SdnZ1QKpWiXbt24s2bN9IxlUolxo4dK8zNzYW5ubmYMGGCFDLQ9+/T0EAIIQIDA0WDBg2Evr6+KFOmjDhx4oRG+SNHjoj//e9/Ql9fXzRu3FgEBQVpHF+4cKGwsrISRkZGon///iI+Pl46xs+8/57P+9fhw4dFlSpVhFKpFGXLlhX79+/XKP81n1URERHCxcVFKJVKUaxYMeHt7Z3n74+0Z+LEiQJAhs3e3l4Ikbffg+xb37+c+temTZuk774aNWqICxcuaNT/mu++nL53v4ZMiP8/fpCIiIiIiIiIiEhLuKYUERERERERERFpHUMpIiIiIiIiIiLSOoZSRERERERERESkdQyliIiIiIiIiIhI6xhKERERERERERGR1jGUIiIiIiIiIiIirWMoRUREREREOXr//j0ePnyY6bGQkBA8efJEyy0iIqJ/O3l+N4CIiIiIiL6cWq3Ghw8f8PbtWzx//hxPnz7FrVu3EBoaimPHjsHIyAgA8Pjx4791/pIlS0KhUEivDx8+jDlz5iAkJCRD2SVLliAsLAwHDx7M8bx9+/ZFu3bt0KxZM7x+/TrLcqVLl4aurm6Wx0NDQ1GsWDHpdUREBO7du4cffvghxzYQEdE/A0MpIiIiIqJ/uJkzZ2LTpk0AgJSUFKhUKsTFxUGtVkNPTw/29vYoXbo0ypYtix9++AEpKSlSKFWuXLm/dc2nT5+iVKlSWR4PCAiAj48Pxo0bl+tz7t+/HwcPHsTcuXPh7e2N8ePHZ1k2PDwc1tbWmR67efMm6tevj6CgICmY2rFjB37++WdERERAR4cTQoiI/g0YShERERER/YPFx8ejbdu2KFy4MJRKJfT19aGvr4/Xr19jzJgxOH78OGxtbTXqvH79GgYGBlAqlRBCQK1WQyaTQSaT4eTJk2jVqhXi4uKgq6uLGjVqwM3NDRMnTgTwcQRWbkKd+/fvY968ebkOpW7dugU3NzcsX74cxYsXB/AxMLty5YpGuUePHqFevXpZnketVmPSpElo1KiRxkgpNzc3zJgxA/v374erq2uu2kRERPmLoRQRERER0T+Yj48P2rZtm+Xx5s2bZ7r/6tWrcHR0BAB06tQJhw4dkoIpADA2NpbKTp48GZMnT4YQAgUKFEBkZKTGua5fv473798jJSUFM2fOxPjx4/Hs2TMUKlQIjx8/xocPHxAXF6cxVbB48eLQ19cHABw7dgy9e/fG6NGj0bt3b6SmpgL4GEBZWFh80f2YN28ebty4AT8/P439lpaWmDZtGsaOHQtHR0cULVr0i85LRETaJxNCiPxuBBERERERZe/Vq1eIjY2VXoeGhqJly5Y4e/asxkgpY2PjDIFMhw4dYGZmBnd3d1y4cAFDhw7F/fv3oauri3bt2qFjx45wc3PDgQMHsHTp0gyhVJMmTaCrq4vAwEBYWVmhdu3aCAoKwtmzZ7Ns7507d1C1alUAwNSpU1G4cGGMHz8eK1euxPXr11GrVi2sXbsW58+f16gXGBgIJyenTKfvLV68GFOnToW3tzd69+6d4ZoqlQpNmjTBy5cvceLECZQvXz7be0pERPmLI6WIiIiIiP4FRo4ciUOHDmXY//lIKRcXFxw9ejRDOQsLC5QtWxZBQUEAgDJlykAul0NPTw+FChVC2bJlUaRIkQz1oqOjce3aNYwcORJBQUGYNm0aevbsidTUVFy6dAn169fHqFGjsl3ofNGiRQCAN2/eYOHChVLw9fTpU5QtW1ajrFqtzlA/Li4OI0aMwO+//45169ZlGkgBgEKhwIkTJ9C9e3dUrVoVo0aNwtKlS6XRYURE9M/CUIqIiIiI6F9i6NChWLBgAQAgODgYtWvXhp+fH+zs7AAAM2bMQFhYWKZ1o6Ki8PjxY4SGhgL4OCJJV1cXKSkpePv2LR4/fozw8PAM9bZs2QIHBwdp1FGLFi1QvHhxFClSBPXr189121NTU9G3b1/Uq1cPvXv3RkREhDS9MDMFChSQ/pyQkIDXr1/jzJkzuH37do4h0+PHj3HixAlEREQwkCIi+gfj9D0iIiIion+BDh06ZDpS6nOZjZTq3LlzhrppaWmZ1v98TalNmzahYMGCiIqKwpw5c/DgwQOsXLkSXbp0kZ7Ol9NIqfRAaufOnZg9ezbmzJmDsmXLIjAwMMv3YWZmhg8fPmTY//79e7x58wYAMGjQIBQrVgyzZs0C8PGpfH379s32yX1ERPTPwZFSRERERET/En9npFRaWhp2796tsc/T0xOLFy+Gn5+fxoikdKmpqZDLP/6qMHDgQACAl5cXAMDIyAju7u6Zhkrpo5Lat28vBVRRUVFo3749wsLCUKVKFansiRMnkJycnOHaTk5OGDt2bJZP0LO0tISlpSUA4MWLF+jSpYs0BfDJkycAABMTk0zrEhHRPwtDKSIiIiKif4lt27ZJYU/6SKcWLVpAV1cXABAbG4smTZpo1KlSpQoePHiQ6fmyG02U04SKT0OlBQsWICIiAp6engA0QyFTU1PY29vD09MTY8aMAfAxTMoskPpUWloawsPDM13nCvg4RS8sLAxOTk7Svvfv38PQ0BBGRkbZnpuIiP4ZGEoREREREf0L/PLLL3j//r30OiwsDF26dMH69es1ghsLCwuNep+GR8uWLcPGjRuxa9cuVKpUCcDHJ9b169cPsbGxOHDggDRCKifFixeX/mxubo64uLgMi5YDgK6uLrZt26axr1u3brh+/XqW5542bRqmTZuGfv36SSO0Prd582aUKVNGY/TV27dvUbhw4Vy1n4iI8h9DKSIiIiKif7iQkBCUKVMm02OdOnXSeP35mlDpU/v27t2LNWvWwNjYGBs2bEC5cuVQunRpbNq0Ca9fv8aff/6J0qVL592b+MS1a9fw559/YuXKlejfvz86duwoHStatCgWLFiA/v37Z1k/NDQUq1evxooVKzT2h4WFoWjRonnVbCIi+sYYShERERER/cPZ2tri0aNHGvtCQ0PRsmVLnD17Fra2ttL+rEY62dvbY/v27QgNDcWZM2fw66+/apx/+/btcHNzg4ODQ47tCQ0NRUJCgvT6w4cPiIuLw+PHjzXKlSlTJsun39WtWxcvXrzAiBEjsHnzZuzatStX0+7i4+Ph6uqK6tWrw83NTePYw4cPswzviIjon4ehFBERERHRP1hwcHCO6y99KjU1FY8fP4ahoSHMzMzw8uVLvH79Gk+fPkVAQAAuXryIBw8eoG7dupg8eTJsbGywbds2rF69GgsXLkSfPn0wZ84c2NvbZ3mNvn37wtfXN8P+cuXKabxOTEyEUqnM9ByGhoYYMGAAWrdujZ07d+LJkyeQyWSIiYmBnp5epnXu3buHPn36QKVS4dixY7h//z527NgBJycnGBoa4uLFi+jZs2eu7xUREeUvmchpBUMiIiIiIso3mT3lLjcaN26MtWvXombNmgAABwcHVK1aFY6Ojmjbtm2G0Ck5ORleXl5YvHgxKleujP3792sc9/Lywpw5cxASEvK334uTkxOcnJwwZ84cjf1CCNSpUwfPnz9HhQoVcPDgQY21sdLS0tCnTx/88ccf6Ny5M9auXQsLCwskJCSgTZs2uH//PmJiYuDk5IR9+/ZxoXMion8JhlJERERERN+xhIQEGBoa5rp8WloaUlNToa+vn4et+nIHDhxA4cKFUa9evfxuChERfSMMpYiIiIiIiIiISOt08rsBRERERERERET038NQioiIiIiIiIiItI6hFBERERERERERaR1DKSIiIiIiIiIi0jqGUkREREREREREpHUMpYiIiIiIiIiISOsYShERERERERERkdYxlCIiIiIiIiIiIq1jKEVERERERERERFr3/wAq49DoVS7K/wAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 可视化特征重要性\n", "def plot_feature_importance(feature_importance_df, top_n=20, figsize=(12, 8)):\n", "    \"\"\"\n", "    绘制特征重要性图\n", "    \n", "    Args:\n", "        feature_importance_df: 特征重要性DataFrame\n", "        top_n: 显示前N个特征\n", "        figsize: 图形大小\n", "    \"\"\"\n", "    plt.figure(figsize=figsize)\n", "    \n", "    # 选择前N个特征\n", "    top_features = feature_importance_df.head(top_n)\n", "    \n", "    # 创建水平条形图\n", "    plt.barh(range(len(top_features)), top_features['importance'], \n", "             color='skyblue', edgecolor='navy', alpha=0.7)\n", "    \n", "    # 设置y轴标签\n", "    plt.yticks(range(len(top_features)), top_features['feature'])\n", "    \n", "    # 反转y轴，使重要性最高的特征在顶部\n", "    plt.gca().invert_yaxis()\n", "    \n", "    # 设置标题和标签\n", "    plt.title(f'LightGBM特征重要性 (前{top_n}个特征)', fontsize=16, fontweight='bold')\n", "    plt.xlabel('重要性得分', fontsize=12)\n", "    plt.ylabel('特征名称', fontsize=12)\n", "    \n", "    # 添加网格\n", "    plt.grid(axis='x', alpha=0.3)\n", "    \n", "    # 调整布局\n", "    plt.tight_layout()\n", "    \n", "    # 保存图片\n", "    plt.savefig('lightgbm_feature_importance.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "\n", "# 绘制特征重要性图\n", "plot_feature_importance(feature_importance_df, top_n=20)"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 4. 多维度特征选择\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始多维度特征选择...\n", "\n", "=== 选择 50 个特征 ===\n", "方法1: LightGBM重要性选择\n", "方法2: 递归特征消除 (RFE)\n", "方法3: 统计特征选择 (SelectKBest)\n", "✓ 50个特征选择完成\n", "\n", "=== 选择 100 个特征 ===\n", "方法1: LightGBM重要性选择\n", "方法2: 递归特征消除 (RFE)\n", "方法3: 统计特征选择 (SelectKBest)\n", "✓ 100个特征选择完成\n", "\n", "=== 选择 200 个特征 ===\n", "方法1: LightGBM重要性选择\n", "方法2: 递归特征消除 (RFE)\n", "方法3: 统计特征选择 (SelectKBest)\n", "✓ 200个特征选择完成\n", "\n", "多维度特征选择完成\n"]}], "source": ["# 多维度特征选择\n", "def multi_dimensional_feature_selection(X, y, feature_importance_df, n_features_list=[50, 100, 200]):\n", "    \"\"\"\n", "    多维度特征选择方法\n", "    \n", "    Args:\n", "        X: 特征数据\n", "        y: 标签数据\n", "        feature_importance_df: LightGBM特征重要性\n", "        n_features_list: 要选择的特征数量列表\n", "    \n", "    Returns:\n", "        selection_results: 各种选择方法的结果\n", "    \"\"\"\n", "    print(\"开始多维度特征选择...\")\n", "    \n", "    selection_results = {}\n", "    \n", "    for n_features in n_features_list:\n", "        print(f\"\\n=== 选择 {n_features} 个特征 ===\")\n", "        \n", "        # 方法1: 基于LightGBM重要性选择\n", "        print(f\"方法1: LightGBM重要性选择\")\n", "        lgb_selected_features = feature_importance_df.head(n_features)['feature'].tolist()\n", "        \n", "        # 方法2: 递归特征消除 (RFE)\n", "        print(f\"方法2: 递归特征消除 (RFE)\")\n", "        lgb_estimator = lgb.LGBMClassifier(\n", "            objective='binary',\n", "            n_estimators=100,\n", "            learning_rate=0.1,\n", "            random_state=42,\n", "            verbose=-1\n", "        )\n", "        \n", "        rfe = RFE(estimator=lgb_estimator, n_features_to_select=n_features, step=0.1)\n", "        rfe.fit(X, y)\n", "        \n", "        feature_names = X.columns if hasattr(X, 'columns') else [f'feature_{i}' for i in range(X.shape[1])]\n", "        rfe_selected_features = [feature_names[i] for i in range(len(feature_names)) if rfe.support_[i]]\n", "        \n", "        # 方法3: 统计特征选择 (SelectKBest)\n", "        print(f\"方法3: 统计特征选择 (SelectKBest)\")\n", "        selector = SelectKBest(score_func=f_classif, k=n_features)\n", "        selector.fit(X, y)\n", "        \n", "        statistical_selected_features = [feature_names[i] for i in range(len(feature_names)) if selector.get_support()[i]]\n", "        \n", "        # 保存结果\n", "        selection_results[n_features] = {\n", "            'lgb_importance': lgb_selected_features,\n", "            'rfe': rfe_selected_features,\n", "            'statistical': statistical_selected_features\n", "        }\n", "        \n", "        print(f\"✓ {n_features}个特征选择完成\")\n", "    \n", "    return selection_results\n", "\n", "# 执行多维度特征选择\n", "selection_results = multi_dimensional_feature_selection(\n", "    X_balanced, y_balanced, feature_importance_df, \n", "    n_features_list=[50, 100, 200]\n", ")\n", "\n", "print(\"\\n多维度特征选择完成\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始特征选择性能评估...\n", "\n", "评估基准模型（所有特征）...\n", "基准模型 AUC: 0.9991 ± 0.0010\n", "\n", "=== 评估 50 个特征的选择方法 ===\n", "评估方法: lgb_importance\n", "  AUC: 0.9999 ± 0.0001 (改进: +0.0008)\n", "评估方法: rfe\n", "  AUC: 0.9995 ± 0.0007 (改进: +0.0005)\n", "评估方法: statistical\n", "  AUC: 0.9993 ± 0.0007 (改进: +0.0002)\n", "\n", "=== 评估 100 个特征的选择方法 ===\n", "评估方法: lgb_importance\n", "  AUC: 0.9994 ± 0.0008 (改进: +0.0004)\n", "评估方法: rfe\n", "  AUC: 0.9991 ± 0.0010 (改进: +0.0000)\n", "评估方法: statistical\n", "  AUC: 0.9986 ± 0.0008 (改进: -0.0005)\n", "\n", "=== 评估 200 个特征的选择方法 ===\n", "评估方法: lgb_importance\n", "  AUC: 0.9999 ± 0.0001 (改进: +0.0008)\n", "评估方法: rfe\n", "  AUC: 0.9990 ± 0.0010 (改进: -0.0001)\n", "评估方法: statistical\n", "  AUC: 0.9993 ± 0.0007 (改进: +0.0002)\n", "\n", "=== 特征选择性能评估结果 ===\n", "                  method  n_features   cv_mean    cv_std  improvement\n", "1      lgb_importance_50          50  0.999869  0.000118     0.000812\n", "7     lgb_importance_200         200  0.999863  0.000133     0.000805\n", "2                 rfe_50          50  0.999508  0.000743     0.000451\n", "4     lgb_importance_100         100  0.999440  0.000844     0.000383\n", "9        statistical_200         200  0.999307  0.000735     0.000250\n", "3         statistical_50          50  0.999290  0.000678     0.000232\n", "5                rfe_100         100  0.999089  0.001025     0.000032\n", "0  baseline_all_features         331  0.999057  0.000959          NaN\n", "8                rfe_200         200  0.998950  0.000984    -0.000107\n", "6        statistical_100         100  0.998558  0.000785    -0.000500\n"]}], "source": ["# 特征选择验证和性能评估\n", "def evaluate_feature_selection(X, y, selection_results, cv_folds=5):\n", "    \"\"\"\n", "    评估不同特征选择方法的性能\n", "    \n", "    Args:\n", "        X: 原始特征数据\n", "        y: 标签数据\n", "        selection_results: 特征选择结果\n", "        cv_folds: 交叉验证折数\n", "    \n", "    Returns:\n", "        evaluation_results: 评估结果\n", "    \"\"\"\n", "    print(\"开始特征选择性能评估...\")\n", "    \n", "    evaluation_results = []\n", "    \n", "    # 基准模型（使用所有特征）\n", "    print(\"\\n评估基准模型（所有特征）...\")\n", "    lgb_classifier = lgb.LGBMClassifier(\n", "        objective='binary',\n", "        n_estimators=200,\n", "        learning_rate=0.05,\n", "        random_state=42,\n", "        verbose=-1\n", "    )\n", "    \n", "    cv_scores = cross_val_score(lgb_classifier, X, y, cv=cv_folds, scoring='roc_auc')\n", "    baseline_score = cv_scores.mean()\n", "    baseline_std = cv_scores.std()\n", "    \n", "    evaluation_results.append({\n", "        'method': 'baseline_all_features',\n", "        'n_features': <PERSON>.shape[1],\n", "        'cv_mean': baseline_score,\n", "        'cv_std': baseline_std\n", "    })\n", "    \n", "    print(f\"基准模型 AUC: {baseline_score:.4f} ± {baseline_std:.4f}\")\n", "    \n", "    # 评估各种特征选择方法\n", "    for n_features, methods in selection_results.items():\n", "        print(f\"\\n=== 评估 {n_features} 个特征的选择方法 ===\")\n", "        \n", "        for method_name, selected_features in methods.items():\n", "            print(f\"评估方法: {method_name}\")\n", "            \n", "            # 选择特征\n", "            if hasattr(X, 'columns'):\n", "                X_selected = X[selected_features]\n", "            else:\n", "                feature_indices = [i for i, name in enumerate([f'feature_{i}' for i in range(X.shape[1])]) if name in selected_features]\n", "                X_selected = X[:, feature_indices]\n", "            \n", "            # 交叉验证评估\n", "            cv_scores = cross_val_score(lgb_classifier, X_selected, y, cv=cv_folds, scoring='roc_auc')\n", "            mean_score = cv_scores.mean()\n", "            std_score = cv_scores.std()\n", "            \n", "            evaluation_results.append({\n", "                'method': f'{method_name}_{n_features}',\n", "                'n_features': len(selected_features),\n", "                'cv_mean': mean_score,\n", "                'cv_std': std_score,\n", "                'improvement': mean_score - baseline_score\n", "            })\n", "            \n", "            print(f\"  AUC: {mean_score:.4f} ± {std_score:.4f} (改进: {mean_score - baseline_score:+.4f})\")\n", "    \n", "    return pd.DataFrame(evaluation_results)\n", "\n", "# 执行特征选择性能评估\n", "evaluation_df = evaluate_feature_selection(X_balanced, y_balanced, selection_results)\n", "\n", "print(\"\\n=== 特征选择性能评估结果 ===\")\n", "print(evaluation_df.sort_values('cv_mean', ascending=False))"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 5. 结果可视化和分析\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 性能对比可视化\n", "def plot_performance_comparison(evaluation_df, figsize=(14, 8)):\n", "    \"\"\"\n", "    绘制不同特征选择方法的性能对比图\n", "    \n", "    Args:\n", "        evaluation_df: 评估结果DataFrame\n", "        figsize: 图形大小\n", "    \"\"\"\n", "    plt.figure(figsize=figsize)\n", "    \n", "    # 排序数据\n", "    df_sorted = evaluation_df.sort_values('cv_mean', ascending=True)\n", "    \n", "    # 创建颜色映射\n", "    colors = ['red' if 'baseline' in method else 'skyblue' for method in df_sorted['method']]\n", "    \n", "    # 绘制水平条形图\n", "    bars = plt.barh(range(len(df_sorted)), df_sorted['cv_mean'], \n", "                    xerr=df_sorted['cv_std'], color=colors, alpha=0.7, \n", "                    edgecolor='navy', capsize=5)\n", "    \n", "    # 设置y轴标签\n", "    plt.yticks(range(len(df_sorted)), df_sorted['method'])\n", "    \n", "    # 设置标题和标签\n", "    plt.title('特征选择方法性能对比 (AUC得分)', fontsize=16, fontweight='bold')\n", "    plt.xlabel('AUC得分', fontsize=12)\n", "    plt.ylabel('特征选择方法', fontsize=12)\n", "    \n", "    # 添加数值标签\n", "    for i, (bar, score, std) in enumerate(zip(bars, df_sorted['cv_mean'], df_sorted['cv_std'])):\n", "        plt.text(bar.get_width() + 0.001, bar.get_y() + bar.get_height()/2, \n", "                f'{score:.4f}±{std:.3f}', \n", "                ha='left', va='center', fontsize=10)\n", "    \n", "    # 添加网格\n", "    plt.grid(axis='x', alpha=0.3)\n", "    \n", "    # 调整布局\n", "    plt.tight_layout()\n", "    \n", "    # 保存图片\n", "    plt.savefig('feature_selection_performance_comparison.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "\n", "# 绘制性能对比图\n", "plot_performance_comparison(evaluation_df)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABKUAAAJOCAYAAABm7rQwAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAADjZklEQVR4nOzdd3wU1f7G8c+mk0pJAiQ06b0ldBQUEfQidkFAQSkJ6pWiKHpFsfzEjlcFExARFEW5glfxKogFpZPQpAtISQiEmgTSs/P7Y8wmmw4kG5I8b1/zgp05MzkL42R58j3nWAzDMBAREREREREREXEgp/LugIiIiIiIiIiIVD0KpURERERERERExOEUSomIiIiIiIiIiMMplBIREREREREREYdTKCUiIiIiIiIiIg6nUEpERERERERERBxOoZSIiEgVtnr1aoYOHUpUVFShbQYPHswDDzxwSddNTk6+0q5JIRYuXMjdd9/Nn3/+abf/+++/58Ybb2Tjxo12+0eMGEF4eHiZ9SctLY0LFy4U2Wb69OmMGzeuyDYfffQRgwYNIiYm5pK+/o8//sjXX3+N1WrNdywjI4PU1NRLul5umZmZRR6/kmsXx2q1sn37dt5++21uvfXWS/5zERERqQhcyrsDIiIiUn7++usvvvjiC4YOHUpoaGiBbbZs2YK/v3+Jr/n7778zePBg3nvvPUaMGHHFfdywYQNdu3bFycmJ999/n2effZbNmzfTrFkzkpKSiI+Pp0mTJgD07NmT66+/nv/7v/8r8fXnzZvHX3/9dUl9evDBB21fsyh33HEHhw8fZuPGjbi5uV3S1yjMjh07+Oqrr3jiiSdo1qyZbX9sbCw//fQTEydOtGu/Zs0aqlevXuQ1//rrL9LS0or92m5ubjRu3Nhu38iRI/niiy9ISUnBw8OjwPNWrVrF3r17mTNnTqHX3r17N999912xAVduhmEwceJEdu/ezbZt2+jQoYPd8QcffJBFixYV2bfCzJ07l3/961988803dO/ePd/xrKws2rRpw80338z7779/SdfOlp6eztmzZ4mLiyMmJobDhw+zf/9+/vjjD7Zt20ZCQgIA3t7eREZG8tJLL13W1xEREblaKZQSERGRUnP+/HkefPBBEhMTadq06RVfb8OGDfTq1YuOHTvy1VdfkZqaSkJCAllZWcTHx3Pbbbdx6NAhDh48iLe3N9u3b6devXqX9DU++eQTVq9efUnn9O3bt0Sh1NatW0lISCi1QCqvgwcP8uOPPwKwdu1awKyYyl1Vk5SURFZWFhEREQAEBQUxePBgu+vceuut7Nq1q9iv16RJE37++WduuOEGxowZw9SpUy+774cPH+bNN99k9OjRdOrU6bKusWzZMnbv3s2dd95Jhw4dOHXqFDVq1MDFpeiPuEuWLGHNmjX8+9//LrTNwIED+de//sWdd97J1q1bqV27tt3xr776ikOHDtGwYcNi+5mUlESfPn1ISUkhPT2dixcvcuHCBS5evFhg+1q1atG+fXt69OjBgAED6N27d5ndQyIiIuVJoZSIiEgV1KJFC+69994Cg5UvvvjCFnQAnDt3jpSUFMaMGWPb16xZM5566im78zIzMxk6dCgHDx7khRdeoHv37nTs2JHt27eXuF8hISF2Qwm7d+/OTz/9xODBg7n11lsZPnw4AHv37mXs2LFcuHCBr776Cm9vb86ePUtycjINGjQo8dfL5uXlxfLly4ttt2zZMt59990SXdNqtRIbG0urVq0uuT8ltXnzZsaPH2+3b/bs2fnanT171tauT58++UKp5557jrNnz/Lrr7/yxRdfMGHCBFq2bAnAv//9bw4cOMB7772Hn58f6enpHDx4kNOnT19R32NiYpg1axZ16tS5rFAqPT2dp59+GldXV2bMmMHvv//OoEGDGDFiBLNmzSrwnF9//ZWXXnqJn3/+GX9/fx5//PFC75f69evz2Wef0b9/fx555BH+85//2I5ZrVb+7//+j4CAAB5++OFi++rj40NQUBA//PADnp6eeHl50ahRI+rWrYu/vz/Lli3DarXywQcfMGjQoHwBmIiISGWlUEpERKSKOX/+PPv37+fAgQMFhlLr169n3rx5dvuSk5Pt9vXq1csulEpPT2fIkCGsWLGCoUOHMm3aNADuueeefEOfUlNTWbBgAcHBwQwaNMjuWO6qk8zMTA4fPky9evV47bXXSE9P58SJEwB4enoyYsQIrrvuOpo3b05CQoJtjqXLqdC6ePEi119//SWfV5Tjx4+TmZnJH3/8gcViueTzb7vtNr7++usi2/Ts2ZPPP/8cgF9++YU5c+bw5JNP2oU8//znP/Hy8uLVV18FIDAwMN917r33XsD8M//iiy8YNGgQN954IwD/+c9/+Ouvv2zzUh04cOCS30u2lJQUzp49S3BwMEeOHAHM8OdyvP766+zfv58nn3yS5s2b06RJE0JCQpg9ezbt2rWzm0fr5ZdfZunSpezZsweLxcLw4cN5/fXXCQoKKvJr3HjjjQwbNozPPvuMn376iX79+gHm/Fc7duzgww8/xMvLq0T9/eabb3Bysp/ONSMjg/vuu4+0tDRuuukmhg4darve22+/zdq1a5k3b16xwy9FREQqKoVSIiIiVcyGDRtsv+7cuROAF198kYiICJ599lneeecd3nnnHVv7evXq4e/vz7Zt2wq8XmxsLHfffTcbNmxg6NChfPLJJ1itVjIyMvjXv/6Vr/3p06dZsGABLVu2tA0pK0hMTIzdnEm5DRgwAMDWz/fee4+UlBQAIiIi7Kpasr311lv55hzK5uPjw5YtWwrtS7ZPP/2UF154odh2AEePHgXMoViXMidXttzDEBcuXMhDDz0EYJvQu3fv3gCMHj2ayMhILBYLK1asYMCAAdxwww22c99//318fX0ZOnToJffhSn388ccsX76c1NRU0tPTadu2LdWrV2fDhg224YJLlixh/fr1tvty+vTpdiFMcHCwLeQEc2LzVatW8d5772GxWDh//jzjxo0jJSWFjIwMAKZOncqQIUNs5/zf//0fderUYeLEiYSFhdGyZUtb29yOHz+er9KsWrVqAEyYMIHbb78dgFmzZuHh4cGhQ4d49tln7dpPnjyZmjVr5rt2diC1ZcsW2rRpw7lz5xg6dCirV69m+PDhfPTRR7Yhei+99BLPPfcc/v7+HDp0iM6dOxf/hy0iIlIBKZQSERGpYrKH5iUmJtqGYB04cICjR49y7ty5S76es7MzKSkpPPfcc0yfPh2LxcK//vUvvvrqK5YsWUK7du0uq581atSwTey8adMmvv32Wxo2bMiRI0eYMGECrq6u+Pn5AeYwv+yvff78ec6fPw/AmTNnuHDhAtdcc02RK6UlJSUVGoBdruxKoGnTpjFhwoQruladOnXo27cvAPv37+fYsWN07twZX19fWrRowRNPPMHp06fp27cvCxcuZOHChbZzsyvHRo0axWOPPZYv4Ni8eTORkZGAOdk4mFU6ixcvBmDPnj1kZmbahm9mD+sriT/++IPly5cTEhKCm5sbd999N6+//joLFy5k5cqVAHz33Xd253zxxRd2r9u0aWMXSp09e5bXX3/d9nrOnDl4enri6+uLr68vLVu2ZO/evXZt1q1bR/fu3e0q1lq0aMFDDz1kFyodP3680Enyd+3alW/erVdeeSVfu1GjRhUYSgEcO3aM6667Dh8fHwzDICEhgQ8++MBW1ZWZmcmECROYPXs2DRs2ZMWKFbRo0aLAa4mIiFQGCqVERESqkMzMTD777DOaNGnCgQMH+Pjjj3nwwQdZuHChrQrkUtWpU4fo6GicnZ0B+PLLL5kxYwYNGzYsdnhUUfz8/Hj22Wc5deoUH3zwAcHBwYwZM4Zp06bZgphp06YxdepUEhIS+PXXXzEMg127dtmGQN18883s2LGDQ4cO2V07LS2NU6dOAXDffffZAp+S8vX1JSYmBldX10Ln/8mulCqNUOGmm27ipptuAsxJyY8dO8a7775rGxrZqFEjWwhWlNtvvz1fKHXw4MF8wzW///77fOdmt8ldgVScX375hVatWtnCoGeeeYZ33nmHqVOncvr0aUaPHs2HH34IwBNPPMFbb73Fnj17igy+Bg8ezM6dO/Hx8bEFUbmHxWVmZtKlSxeaNGnCsWPHAOjUqZNdIHXmzBn++uuvfH9moaGhGIZR4NeNi4sjKCiIsLCwIiv8iuLl5cWkSZOYNWsW586do0aNGpw7d46kpCRSU1O55557WL16NT179mTp0qWaW0pERCo9hVIiIiJVyKJFizhx4gQzZswo8PiHH37I2LFj8+2PjY3NNy/S448/zptvvglgC6SWLVvG8OHD8ff3Z/ny5dSqVYvVq1ezbNkyHnzwwUKHzxXmzz//ZOjQoZw8eZIVK1awdetWAK699lqGDRvG008/jYeHB1ar1baS2f79+21zKm3bto02bdrku+4vv/zCzTfffEl9yS17CF+LFi3Yu3dvgW2yQ6nx48fj7u5eousGBQXx888/F3rcMAw2btwIkG+i8bp16xY6BPGDDz7gxRdfLPDY0KFDbUP73nnnHSZNmsTevXttYdqNN97ImjVrbJVmBw4cyFfNVJA9e/awdetWHnvsMaKjowEzaLztttts1VG5530qqWrVquHh4cFvv/1WaJv33nuP3r1721ZVjIqKsg13BDM4BXPS95LKDquuZBW8mjVr8tJLL/HUU08xZ84cZs6cyTPPPMMbb7yBp6cnsbGxjB49mtmzZ5OcnExiYiK+vr6X/fVERESudgqlREREqpCNGzcSEBDAI488ku/YhQsXsFgsjB49usBzjx07xsqVK+nevTtt2rShW7dudsffeecdHn/8caxWK6tWrbKFQVu3buXf//43vXv3vqRQ6uLFi1x33XWcO3eOzz//nH79+tlCKRcXFyIjI9m3bx+nT59m0aJF1KxZk7Nnz7J582Y6depEfHw8J06csE3inVunTp1YsmQJ586dIzY2tsR9yq1t27ZFBgbZodThw4dLfM3scK8wv/32m63CKzvcGThwIGBW8tStW7fEX6sgK1aswN3dvcAJ8C9V9nxfw4YNs4VS2a+XLFnCzTffTGho6GVd+5dffikwPM02cuRIevfuTWhoKJ9++inXXnttvjZ169blzjvvLPHXzMrKAihxwFgUb29vJk+ezCOPPGIL/TIzM/n0008ZPnw4J06coGvXrgDMnz/fNsG6iIhIZaNQSkREpAqZPXs2b7zxRr4Vw2bOnMnIkSOZNGmSbThVXj/88AMrV65k+PDhPProo7b9CQkJPPzww3z22We2fa1bty62Lz/99JNd9dU//vEPli9fbnvt5eXF4sWL8ff3p02bNnz88cf897//tR13cnLixx9/5IknnuDw4cPMnTuXl19+mdWrVzNu3DjWrVsHmCsF5lW7dm3uvvtuPvzwwxJPXJ5XRkYGLi6Ff5Q6evQofn5+tvmtimIYBk5OTvj4+BTZ7uOPP7b9vlq1agwfPtw2WX316tVtc3Dl9dNPPxW7kt/ChQv54YcfCAsLK/J9NW3a1G6IW/bE63k5Oztz88035wsvvby8uPHGG3n33XeL7E9JvPDCC7bwBswg8+6777a9fuyxx6hRowbr1q0jMzPTtt/Dw4Px48fj6elZ4q+VXSnm7e19xf0GM+QNCwtjzZo19OvXjzlz5tC4cWPAHBL78ccfc++99zJw4EBmzZrFuHHjSuXrioiIXE0USomIiFQx2YHU+fPnbcHNb7/9hqurK9dcc80lX+++++7j+++/57777uPcuXP88MMPJTovKCjIbghdQVVUuYdXvf/++xw5coS0tDTbEKoLFy7wxRdf0LNnTx566CHWrVvH0qVLSU9PZ+nSpVgslhIN0frXv/5lV7WTmprKfffdR58+fZg4caJd2xkzZrBp06Zir/mPf/yjyMnVc0tOTgYoMpQ6dOgQixYtws3NjfT0dF566SUmT57M888/D5ghWXZAldeJEycKvW5WVhbvvfcejz/+eL6V7gozc+ZMdu7cSVpaGj/++CMuLi75hrXlXcUuW//+/enfvz+TJk0iKSnJtj/7z3TatGm2CezBXBmysLnJ1q5da6tIA0hPT7c7/uqrr7JgwQJ++ukngoOD7Y5lr9aYbfr06SUKKKdPn8706dOLbPPGG2/wxBNP2O3buHEjc+fOtfXzq6++Ijk5mYCAABo1alTgpOnt27fnl19+ITw8HHd3d0aOHFls/0RERCoShVIiIiJVzJ49e3jvvfdYuHChbR6m4cOH8+qrrzJo0CBGjRpV5Pn//Oc/+ec//wmYFT4LFizg119/5Z577rGrUilOq1atCq3KAnOVteeeew4wA5fo6GiaNWvG5MmT7do98sgjjBw5EicnJwYMGMD8+fP5/PPP+fbbb+ndu3eJJosubMW11atX2+YlulQFhQyFSUxMBIoOpZ555hkyMjK4/fbb+frrr+nRowdDhgwhNDSUlStXcvHiRdsqeiWVlZVF3759WbNmDY0bN+b777/PF96MGzeOG2+8kczMTFsFVXBwsO3vwcXFhbFjx9pNNl4Sn3zyCWfOnMm3/z//+Y/d64kTJxYaSmWv4FeYkydPsm/fPjIyMmz7Ll68yKuvvkpkZCQ//vijLQwNDQ0lLCys0GutXLmSv/76i06dOrF161a6dOmSb9L4bB07dsy3z8PDI9+E8gCnTp0qcH9uhmEwbtw42rZtS0hISJFtRUREKhKFUiIiIlVIfHw8119/PSdPnqRJkyY0a9aMH374gbvvvpt69epx5513EhoaSkZGBq6urnbnxsTEsGLFCnr06GE3PC8gIIB77rmn1PuamJjIrFmz7Pb9+eef/Pnnn3b7brvtNlu10B133EFgYCATJkwgISGB+++/v0Rf691337WbdyglJYWePXtyyy235AusJk2axK+//lrgdUpabVOYZcuW2YY0TpgwwTYv03/+8x+++OILBgwYQIcOHWxD8RYvXgyQb2jX4MGDWbt2bYGhT27Ozs689957fPfddzz++ON4eHjka9O5c2eGDBnCiRMnbP259957C5yr61Lknai9pKvv5fbtt98yaNAg2+vz589To0aNAtsePXqUBQsWMGvWLE6ePEm7du3sjg8aNMjuWrmdOnWKzz77jA4dOrBs2TKaNGnChQsXmDVrVrHzgGXr0KGD3bDHI0eOUL9+fZycnDhw4ADNmjVj5MiRtiGa6enptuqzr776invvvZd169YplBIRkUpFoZSIiEgVEhgYyE8//cTJkye5/vrrWbBggd1wu+eee45p06Yxa9YsHn/8caZMmWL7h/EPP/zAihUrGDZsmN2cUmWlUaNGGIbBpk2b6N69OxMnTuTtt98GYN68eYwZM4a5c+cyZswY2zlubm6EhYXx0ksvUatWLUaMGFGir/XYY48VuP9///sf//vf/0rcZ39/f9uqdZciOTmZY8eOUbNmTQICAgBsFV6GYfD888/j7u7OzJkz86189/DDD9uG/2X77bffcHFxKbDqbfz48XbzPHXs2JG2bdsWuhJedhXXqlWr7P6sc+vfvz9Dhgwp2ZstRbNnz7abhyzv8L1sN954IwcPHgSgQYMGfPDBB4wePTpf8FqQ7CqlpKQkZsyYQcOGDRkxYgQLFizgww8/LLK6qjB79+6ld+/e9OzZk2+++Sbf8djYWHr27Em7du149tlnueuuu9i9e/dl3VsiIiJXM4VSIiIiVUybNm1sK+MVZMyYMRw6dIhnn32W7777jm+++QZ/f38H9jCHYRhMmDABwzBYuXIl77zzDoMHD+aFF14gICCA++67L985Fy5cAMyg5/Dhw7Rq1arQa4eGhjJz5sx8x9LT03nqqacICQkpNNjKPZwt26OPPnpZgd0333zDbbfdxuTJk/nXv/5ld8xisfC///2PDRs2FPhePvvsMxISEgq87oIFC/LtGzhwYL7JxzMzM4sdQrZr1y527dpV4DFvb+9yCaW+//77ErVzdnZm/Pjx3HHHHfTr1w8nJyeOHz+Ol5eX3fxVBZk8eTJff/01I0eOtM2B9vLLL7N06VImTpxIu3bt6NmzZ4n7/Msvv3DvvfeSkJBQaKDl5eVFeHg4b7/9Nj169GD48OGlMjG8iIjI1UahlIiIiNhp2LAhixYtok+fPowfP57XX3+d119/vVz6YrFYmDt3Lj/++COLFi1i0qRJTJo0CYDIyMh8qwh+8sknvPPOO9SrV4+YmBhuvfVW1q5dW+C8Utu3b6dTp05Ffv3o6Giio6MLPBYREcHevXsv853ZO3ToEAD16tUr8HjDhg1p2LBhgcdyr+63a9cubrjhBmrWrMltt93Ga6+9xltvvZVvHq68PDw87IaW5ZY9tCz3cMKrRUmH761YsYJGjRrZ7VuyZAnTpk3j119/LXBuqKSkJB5++GE+/fRTevbsSUREhO1YvXr1eO+99xg1ahS33XYb33//vd1E+QU5d+4czz33HB988AG+vr58//333HjjjQW2rV69Ok8//TTh4eE89dRTzJ07l9WrV7NkyRK6d+9e5NcRERGpSBRKiYiISIHGjRtHp06div3Hdllr27YtzZo1A2Dfvn22Sqjp06fTvHlz+vbtC8Dbb7/NlClTqFWrFj///DPz5s3jtdde47rrrmPlypX5Qp0GDRowf/78Ar9mWloa4eHh9OjRI998TdmKq7ApTEZGBj/++CNdunQhICDAVgWW/V4vx5kzZ/jggw945ZVXqFOnDsuWLaNp06Zs27aNxx9/nDVr1jBjxoyrYvjX6dOn861MBxAVFQXA008/ne/P1tXV1bZy3eXIysrKt2/16tVcvHiRBg0a5Dv2448/MnbsWI4cOUKfPn349ttv8823NXLkSA4fPsz06dPp3bs3M2fOZPz48fmuZRgGkydPZu7cuVy8eJFBgwbx/vvvFxoy5lajRg3mzJlDv379eOihh7jhhhv4+eefFUyJiEjlYYiIiEiVNX/+fAMwli1bVuDxzZs3G2FhYcbEiRONzp07G4CxaNGiQq931113GYCRlJRkAJe1Pf/884ZhGEZaWprx008/GVOmTDHq1atnAEbr1q2Nn376yfjkk08Mb29vo0aNGsaGDRuMgQMHGoDRqFEjY9euXYZhGIbVajXGjx9vAEZgYKCxevVqwzAM46+//rrsvhW09erV65L/3H18fGz9qlu3rgEYDRo0MLKysoo99/nnnzcAY/369cbChQuNQYMGGe7u7oaLi4vx4IMPGmfOnLG1zczMNJ555hnD09PTAIzevXsbL7zwghEbG2sYhmH8+eefpfpn0adPnwL73KtXL6NWrVqX/efv7u5ud725c+cagPHtt98ahmEY//73v41x48YZ/fv3NwBjwoQJhmEYxltvvWUARqtWrYwBAwbYtmuvvdYAjEGDBtmuabVaje+//974xz/+YQCGxWIx/vnPfxrp6elF/n188cUXhre3twEY119/vbF+/fp8bRYtWmTcdNNNxqpVqwq8Rvbfw8iRIwv9Or///rtx2223GampqUX2R0REpCJRpZSIiIgUqnXr1ixZsoSzZ8/i5+fHHXfcwR133FHseS4uLowePfqyvmb2UKpffvmFgQMHAubKZa+88grDhg2zrXbWvHlzLl68yJgxY9i5cyf33nsvkZGRVK9eHTCH/s2ePZv69eszbdo0jh07BoCPj89l960g2VVcl+Lxxx9nxYoV7Nu3j4SEBHr06MH777+Pk5PTJV2nevXqHDlyhKeeeooHHniAJk2a2B13dnbm//7v/5g8eTILFy7kl19+4ddff2Xq1KkA+Pr6MnLkyEvuf2FKsmpe9gT2pSk4OJgpU6bg6upKSEiIbeL6sLAwtm3bxpo1a9i5c6etvbOzM3fccYfd6o7h4eHMmTMHgK5du/LGG29w3XXXFfu17733Xjp27MjUqVP5+uuv6dGjB19++aXdipTDhg1j2LBhV/Qee/fuTe/eva/oGiIiIlcbi1HanwpERESkUsnMzMTJyemSA5PS8Pnnn9O8eXNCQkIKbXPkyBF27drFLbfcUmibgwcP5gtspOJKTEwkPj6eoKAgPD09S+2ab7/9NgMHDrzs4XE7duxg7dq1BQ7jExERkfwUSomIiIiIiIiIiMM5/keeIiIiIiIiIiJS5SmUEhERERERERERh1MoJSIiIiIiIiIiDqdQSkREREREREREHM6lvDtQmVitVo4fP46Pjw8Wi6W8uyMiIiIiIiIi4nCGYZCUlERQUFCRKzgrlCpFx48fp379+uXdDRERERERERGRcnfs2DHq1atX6HGFUqXIx8cHMP/QfX19y7k3cqWsViunTp0iICCgyGRXqg7dE5KX7gnJS/eE5Kb7QfLSPSF56Z6QvCrLPZGYmEj9+vVtOUlhFEqVouwhe76+vgqlKgGr1Upqaiq+vr4V+mEgpUf3hOSle0Ly0j0huel+kLx0T0heuickr8p2TxQ3tVHFf4ciIiIiIiIiIlLhKJQSERERERERERGHUyglIiIiIiIiIiIOp1BKREREREREREQcTqGUiIiIiIiIiIg4nEIpERERERERERFxOIVSIiIiIiIiIiLicAqlRERERERERETE4RRKiYiIiIiIiIiIwymUEhERERERERERh1MoJSIiIiIiIiIiDqdQSkREREREREREHE6hlIiIiIiIiIiIOJxCKRERERERERERcTiFUiIiIiIiIiIi4nAKpURERERERERExOEUSomIiIiIiIiIiMMplBIREREREREREYdTKCUiIiIiIiIiIg7nUt4dkHISF2dul6puXXMTEREREREREbkCCqWqqshIeOGFSz/v+edh+vRS746IiIiIiIiIVC0KpaqqsDAYPDj//oED4dQpCAiAH37If1xVUiIiIiIiIiJSChRKVVWFDcNzc8v5tXNnx/ZJRERERERERKoMTXQuIiIiIiIiIiIOp1BKREREREREREQcTqGUiIiIiIiIiIg4nEIpERERERERERFxOIVSIiIiIiIiIiLicAqlRERERERERETE4RRKiYiIiIiIiIiIwymUEhERERERERERh1MoJSIiIiIiIiIiDqdQSkREREREREREHE6hlIiIiIiIiIiIOJxCKRERERERERERcTiFUiIiIiIiIiIi4nAKpURERERERERExOEUSomIiIiIiIiIiMMplBIREREREREREYdTKCUiIiIiIiIiIg6nUEpERERERERERBxOoZSIiIiIiIiIiDicQikREREREREREXE4hVIiIiIiIiIiIuJwLuXdARERkateXJy55WW14nL2LNSsCU4F/Jynbl1zExERERGRfBRKiYiIFCcyEl54Id9uJ8C/qPOefx6mTy+jTomIiIiIVGwKpURERIoTFgaDB+fbbQwciOXUKYyAACw//JD/PFVJiYiIiIgUSqGUiIhIcQobhufmlvNr586O7ZOIiIiISAWnic5FRERERERERMThFEqJiIiIiIiIiIjDKZQSERERERERERGHUyglIiIiIiIiIiIOp1BKREREREREREQcTqGUiIiIiIiIiIg4nEIpERERERERERFxOIVSIiIiIiIiIiLicAqlRERERERERETE4VzKuwMiIiIiIiIiIlVJXFIccRfi8u23Wq2cPXuWmlk1cXLKX0dU17sudX3qOqKLDqFQSkRERERERETEgSKjI3lh9QuXfN7zfZ5net/ppd+hcqJQSkRERERERETEgcJCwhjcYnC+/QM/Hcip5FMEeAbww4gf8h2v6115qqRAoZSIiIiIiIiIiEPV9Sl4GJ6bs5vt1851Ozu6Ww53VU10np6eTuvWrfn1118LbbNlyxZCQ0Px8PCgU6dObNy40e74p59+SqNGjfD09OTWW2/l5MmTtmNWq5Unn3ySmjVrUqNGDSZNmkRWVpbt+OnTp7njjjvw9PSkYcOGfPTRR6X+HkVERERERERE5CoKpdLS0hg+fDh79uwptM2FCxe45ZZbGDhwIPv27WPgwIEMGjSIpKQkADZu3MjYsWN5++232bFjB+np6TzwwAO2899++22WLFnCqlWr+OGHH1i2bBmvv/667fioUaNIS0tj586dvPHGG4wfP55169aV3ZsWEREREREREamiropQavfu3XTv3p0DBw4U2W7JkiVUq1aNl156iYYNG/LKK6/g6+vLkiVLAJg9ezZDhgzhzjvvpGnTpsyZM4cff/yRQ4cOAfD+++/z/PPP07lzZ7p168aLL77IBx98AMBff/3Fd999x+zZs2ncuDH33nsvw4cPJyIiomzfvIiIiIiIiIhIFXRVhFK//fYb119/PWvWrCmy3YYNG+jduzcWiwUAi8VCz549bUP4NmzYwLXXXmtr37BhQ4KDg9m4cSNxcXEcOXLE7nivXr04duwYcXFxbNiwgXr16tGoUSO743mHB4qIiIiIiIiIyJW7KiY6Dw8PL1G7mJgY2rVrZ7cvKCiIXbt22Y4HBwfnOx4bG0tMTAyA3fGgoCAA2/HCzi1MWloaaWlptteJiYmAOXeV1Wot0Xu62lj+3gzAqKDvobRYrVYMw6iwf5dS+nRPSF6WXL/XfSGg54TY0/0geemekLx0T0hRKvJ9UdK+XxWhVEmlpqbi7u5ut8/d3Z2UlJRij6emptpe5z4G2I4Xde2CzJgxgxdeeCHf/lOnTtm+XkUTYLXijHkDnYqPL+/ulCur1UpCQgKGYeDkdFUUFUo50z0heQVkZZnPzKysKv/MFJOeE5Kb7gfJS/eE5KV7QvLKsmbZfo2vwJ8vs+f+Lk6FCqU8PDzyhT1paWlUq1at2OMeHh6AGVxlt8+ucso+XtS1C/L0008zefJk2+vExETq169PQEAAvr6+l/kuy5fl7wehk5MTgYGB5dyb8mW1WrFYLAQEBOgbhAC6JyQ/i7MzAE7OzlX+mSkmPSckN90PkpfuCclL94Tk5ezkbPu1In++zM5gilOhQqng4GDi4uLs9sXGxtqG3RV1PLtNXFwcjRs3th3LPq+4axfE3d09X3UVmIFORX+gWMgJqKoyi8VSKf4+pfTonpDcjFy/1z0h2fSckNx0P0heuickL90TUpiKfE+UtO8V6h12796dNWvWYBjmPwMMw2Dt2rV0797ddvz333+3tf/rr7+IjY2le/fuBAUFUb9+fbvjv//+Ow0aNKBu3bp069aNmJgYDh8+bHc8+9oiIiIiIiIiIlJ6rvpQ6vTp07ZhdnfffTdJSUlMmzaNo0ePMm3aNJKTk7nnnnsACAsL4/PPP+err77i0KFDPPLII9x8881cc801AIwfP57p06ezefNmoqOjefHFF3nkkUcAaNKkCf379+fhhx/m4MGDLF26lC+++ILx48eXzxsXEREREREREanErvrhewEBAcyfP59Ro0bh6+vL8uXLCQ8P54033qBt27Z89913eHt7A9CrVy9mz57NpEmTOHXqFDfddBMffvih7VpTpkzh5MmT3HTTTQCMHj2aJ554wnZ84cKFjB49mrZt2xIYGMicOXMqbaVUXFIccRfi8h+olQ7Wv3+N25LvcF3vutT1qVv2HRQRERERERGRSs1iZI+FkyuWmJiIn58fCQkJV/1E59N/nc4Lq/OvHFic5/s8z/S+00u/Q1chq9VKfHw8gYGBFXosr5Qe3ROSl1GvHpbYWIzgYCwxMeXdHbkK6Dkhuel+kLx0T0heuickr3pv1yM2KZZgn2BiJlfcz5clzUeu+kopKRthIWEMbjE43/6B74RyytMgINnCDxOj8h2v660qKRERERERERG5cgqlqqi6PgUPw3OzWgADN6uFznU7O75jIiIiIiIiIlIlqD5QREREREREREQcTqGUiIiIiIiIiIg4nIbviYiIiIiIiIg4UlycueWVkZHz65Yt+Y/XrWtulYRCKRERERERERERR4qMhBdeyL9/MuALxMdDSEj+488/D9Onl3HnHEehlIiIiIiIiIiII4WFweDB+fd/3gWwgpMTRG/Of7wSVUmBQikREREREREREccqbBje57l+37mzw7pTXjTRuYiIiIiIiIiIOJxCKRERERERERERcTiFUiIiIiIiIiIi4nAKpURERERERERExOEUSomIiIiIiIiIiMMplBIREREREREREYdTKCUiIiIiIiIiIg6nUEpERERERERERBxOoZSIiIiIiIiIiDicQikREREREREREXE4hVIiIiIiIiIiIuJwCqVERERERERERMThFEqJiIiIiIiIiIjDKZQSERERERERERGHUyglIiIiIiIiIiIOp1BKREREREREREQcTqGUiIiIiIiIiIg4nEIpERERERERERFxOIVSIiIiIiIiIiLicAqlRERERERERETE4RRKiYiIiIiIiIiIwymUEhERERERERERh1MoJSIiIiIiIiIiDqdQSkREREREREREHE6hlIiIiIiIiIiIOJxCKRERERERERERcTiFUiIiIiIiIiIi4nAKpURERERERERExOEUSomIiIiIiIiIiMMplBIREREREREREYdTKCUiIiIiIiIiIg6nUEpERERERERERBxOoZSIiIiIiIiIiDicQikREREREREREXE4hVIiIiIiIiIiIuJwCqVERERERERERMThFEqJiIiIiIiIiIjDKZQSERERERERERGHUyglIiIiIiIiIiIOp1BKREREREREREQcTqGUiIiIiIiIiIg4nEIpERERERERERFxOIVSIiIiIiIiIiLicAqlRERERERERETE4RRKiYiIiIiIiIiIwymUEhERERERERERh1MoJSIiIiIiIiIiDqdQSkREREREREREHE6hlIiIiIiIiIiIOJxCKRERERERERERcTiFUiIiIiIiIiIi4nAKpURERERERERExOEUSomIiIiIiIiIiMMplBIREREREREREYdTKCUiIiIiIiIiIg6nUEpERERERERERBxOoZSIiIiIiIiIiDicQikREREREREREXE4hVIiIiIiIiIiIuJwCqVERERERERERMThFEqJiIiIiIiIiIjDKZQSERERERERERGHUyglIiIiIiIiIiIOp1BKREREREREREQcTqGUiIiIiIiIiIg4nEIpERERERERERFxOIVSIiIiIiIiIiLicAqlRERERERERETE4RRKiYiIiIiIiIiIw7mUdwekHB09CqdP2+1qe9IgMBECUwzYssW+vb8/NGjgwA6KiIiIiIiISGWlUKqqOnoUWrSA1FS73T/YfmfAJyH253h4wL59CqZERERERERE5Ipp+F5Vdfp0vkCqWKmp+SqrREREREREREQux1URSqWkpPDQQw/h4+ND7dq1eeWVVwptu3TpUpo1a4aHhwc33ngjBw4csB0zDIMXXniBwMBAfH19GT16NBcvXrQdT0hIYNiwYXh7exMUFMQbb7xhd+333nsPi8Vitz3xxBOl/4ZFRERERERERKq4qyKUmjJlCjt27GDTpk0sWrSIN954g88//zxfu927dzNkyBDuv/9+du7cSZcuXbjhhhtISkoCYO7cubz55pvMmjWLTZs2ERsby4gRI2znjx8/ni1btvDzzz/zn//8h9mzZ/Pvf//b7vrh4eGcOnXKtr344otl/wcgIiIiIiIiIlLFlHsolZKSwrx583jzzTdp1aoVN954I5MmTeKDDz7I1zYiIoIePXrw3HPP0bRpU1555RW8vLxYvHgxAO+//z6TJk3innvuoWXLlnz00Uf897//5c8//+T06dMsXryY2bNn07VrV3r27MmMGTPsqqX27NlDu3bt8Pf3t22enp4O+7MQEREREREREakqyj2U2rZtGxkZGfTs2dO2r1evXmzevBnDMOzaHjx4kO7du9teWywW2rdvz/r16ws8HhQUREBAAOvXr+evv/7CMAy74x06dCA2NpajR48CZqVUs2bNyuR9VhqHDoHVWt69EBEREREREZEKrtxX34uJicHf3x83NzfbvqCgIFJTUzlz5gz+/v62/YGBgcTFxdmdf/ToUWrUqFHg8QsXLnD27FlOnDhBnz59AIiLi6NJkya2cwFOnDiBl5cXp06dYunSpTzyyCO4ubkxduxYHnvsMSwWS4F9T0tLIy0tzfY6MTERAKvVivVqD26s1stLJO+5B6NGDejZE6NXL+jZE7p0MVfmq2SsViuGYVz9f5fiMLonJK/c3x10XwjoOSH2dD9IXronJC/dE1KUinxflLTv5R5Kpaam4u7ubrcv+3VKSord/qFDhzJ48GBGjBhB//79WbRoEVFRUbbAaejQocyYMYM+ffpQv359pkyZAkB6ejoNGzakR48eTJw4kUWLFpGWlsZLL71kO753714AqlevzpIlS9i9ezePPPIIhmEwceLEAvs+Y8YMXnjhhXz7T506ReqlrmznYC5nz+JffLMCWc6dg+++w/LddwAYrq5kdOhARpcupP+9Gf6Xe/Wrh9VqJSEhAcMwcHIq96JCuQronpC8ArKycAasWVmcio8v7+7IVUDPCclN94PkpXtC8tI9IUWJr8CfL7Pn/i5OuYdSHh4e+QKc7OqjatWq2e0fMGAAL774InfccQcZGRn069eP4cOHk5CQAMC0adM4cuQILVu2xM3NjfHjx9OmTRt8fHwA+PTTTxkyZAg1a9bEz8+Pl156ibVr1+Lj40OHDh1ISEjA19cXMIf2xcXFMXv27EJDqaeffprJkyfbXicmJlK/fn0CAgJs17lq1ax5WacZ110Hu3djOX3ats+SkYFbVBRuUVF4/T0XmNG8eU41Va9e0Lw5FFJxdrWyWq1YLBYCAgL0DUIA3ROSn8XZGQAnZ2cCAwPLuTdyNdBzQnLT/SB56Z6QvHRPSFEq8udLjxKOpir3UCo4OJgzZ86QkZGBq6srALGxsXh4eFCrVq187Z966ikmT55MUlISNWvW5K677qJx48YAeHp68tlnnzFnzhwsFguenp4EBgbajjdu3JjNmzdz9uxZfHx82Lt3LxaLhWuuuQYgX5DUsmVLjh8/Xmjf3d3d81V5ATg5OV39D5TL7J9l5kzo1An+/BPWrIG1a81t3z77dvv3w/79WD7+2Nzh72+GU9lbSAgU8Gd3tbFYLBXj71McRveE5JZ75kPdE5JNzwnJTfeD5KV7QvLSPSGFqcj3REn7Xu7vsGPHjri4uLBu3Trbvt9//52uXbvmm8tp8eLFTJkyBVdXV2rWrElycjIrV67k+uuvB2Dq1KksWrQIb29vvLy8WLNmDQkJCfTu3Rur1crAgQPZs2cPNWvWxNXVla+//prOnTvj6+vLggULaNy4sd3k6lu3bqVVq1aO+YOoSCwWs/LpoYdg3jzYuxfi4+Hrr2HKFOjRA/4OGG1On4b//heefNIMpfz84NprYepUWL4czp4tl7ciIiIiIiIiIuWj3CulPD09GTlyJI8//jgLFiwgPj6ed999l8jISABOnz6Nj48P7u7uNGvWjAcffJBrr72Wdu3a8eyzz9K4cWNuueUWAGrXrs1zzz1H69atsVgsPProo4wbN842Ebq7uztPPvkk7777Lnv27OGtt97iww8/BKBfv3489thjPPHEE/zzn/8kOjqa119/nfnz55fPH0xFExAAt91mbgCpqRAVlVNNtW6dffCUlmYeW7MGXnvN3NeqFfTunVNN1aRJhRvyJyIiIiIiIiIlU+6hFMBbb73Fww8/TLdu3fD29uaZZ57h3nvvBSAgIID58+czatQoQkJCmDVrFo899hjx8fFcf/31LF++3FYW9thjj3H48GH69++P1Wpl2LBhvPXWW7avExkZydixY2nXrh21atXilVde4e677wagXr16/O9//+PJJ5/kgw8+oHbt2rz++uvceeedjv8DcQR/f3PFvEuZkN3DwzyvpG179zY3AKvVrKjKHu63di0cOGB/zp495jZ3rvm6dm37IX+dOkGuVRpFREREREREpOKyGLnHq8kVSUxMxM/Pz27C9Kva0aPmsLpcBr4TSnw1g8AUCz9MjLJv7+8PDRqU3tc/ccKsoMquptqyBTIzC29frRp07WoGVL17m8MEq1cvvf7kYbVaiY+PJzAwsEKP5ZXSo3tC8jLq1cMSG4sRHIwlJqa8uyNXAT0nJDfdD5KX7gnJS/eE5FVvijOx3laCLzgR80ZWeXfnspU0H7kqKqWknDRokC9k2lnbQqy3QfAFC3TuXLZfv04duPNOcwNIToZNm3Iqqdatg79XVgQgJQVWrzY3MIf2tWljP+SvUSMN+RMRERERERGpABRKydXD0xP69jU3MIf87dqVE1KtWQOHD+e0NwzYudPcIiLMfUFBOQFV797QoQO46DYXERERERERudroX+ty9XJygnbtzC083Nx3/HhOQLV2LWzbBlm5ShqPH4clS8wNwMsLunXLqabq3h0qwtBKERERERERkUpOoZRULEFBcM895gZw4QJs3JhTTbV+PSQl5bS/eBF+/tncwAy62re3n0C9NOfJEhEREREREZESUSglFZu3N/TrZ25gVk398Yd9NdWxYzntrVazumrbNpg1y9xXv779kL927cDZ2dHvRERERERERKRKUSgllYuzM3TsaG6PPGLuO3o0p5Jq7VrYscMMp7IdOwaLF5sbgI+POcyvVy/cWreGAQM05E9ERERERESklCmUksove5XB++4zXycmwoYNOSHVhg3mML9sSUnw4484/fgjNQEjO+jKPeQvOLg83omIiIiIiIhIpaFQSqoeX1+46SZzA8jMhO3b7Yf8HT9ua27JyoLoaHN7911zZ6NGOcP9evWCNm3M+apEREREREREpEQUSom4uEBIiLk99hgYBhw5gvW330j96Seqbd2KZedOc3+2w4fNbdEi87WfH/TsmVNJ1bUreHqWx7sRERERERERqRAUSonkZbGYlVANGpB40014BAZiSUw0V/bLrqbatAlSUnLOSUiA7783NzCDrs6d7Yf81alTLm9HRERERERE5GqkUEqkJKpXh5tvNjeA9HRzBb/s4X5r18LJkzntMzPN4GrTJpg509zXpIn9kL+WLTXkT0RERERERKoshVIil8PNzRyi17UrTJ5sDu07eNB+lb/du+3POXjQ3BYuNF/XqGFfSdWlC3h4OP69iIiIiIiIiJQDhVIipcFigaZNzW3kSHPfmTP2Q/42b4a0tJxzzp2D5cvNDcDVFUJD7YOqgADHvxcRERERERERB1AoJVJWatWCQYPMDcxAKjravprq9Omc9hkZZoi1fj28+aa5r3lz+yF/zZubAZiIiIiIiIhIBadQSsRR3N3NFfp69oQpU8whf/v35wRUa9aYr3Pbv9/c5s83X/v721dShYSY1xURERERERGpYBRKiZQXiwVatDC3hx4y9506BevW5UygHhVlVlBlO30a/vtfcwMzkOrSJaeaqmdPqFnT8e9FRERERERE5BIplBK5mgQEwG23mRtASooZTOUe8nfuXE77tDQzwFqzBl57zdzXqlXOcL9evcxV/zTkT0RERERERK4yCqVErmbVqsG115obgNUKe/fmDPdbu9Zc0S+3PXvMbe5c83Xt2vZD/jp1MlcPFBERERERESlHCqVEKhInJ2jd2tzGjjX3nThhX0m1ZQtkZuacc/IkLF1qbmAGXV275gz569EDqld3+FsRERERERGRqk2hlEhFV6cO3HWXuQEkJ8OmTTkh1bp1kJCQ0z4lBVavNjcwh/a1aWM/5K9RIw35ExERERERkTKlUEqksvH0hL59zQ3MIX+7dtkP+Tt8OKe9YcDOneYWEWHuCwrKCah694YOHcBFjwsREREREREpPfpXpkhl5+QE7dqZW3i4uS821n7I37ZtkJWVc87x47BkibkBeHlBt2451VTdu4Ovr8Pfikh5iUuKI+5CXL79Rq10LNa/f43bku94Xe+61PWp64guioiIiIhUOAqlRKqi4GC4915zA7hwATZuzKmm2rABkpJy2l+8CD//bG5gBl3t29tPoN6ggePfh4iDREZH8sLqF/IfuDP7N6dgTki+w8/3eZ7pfaeXZddERERERCoshVIiAt7e0K+fuYFZNfXHHznD/dauhWPHctpbrWZ11bZtMGuWua9+ffshf+3agbOzo9+JSJkICwljcIvB+fYPfCeUU54GAckWfpgYle94XW9VSYmIiIhIyaU6G3a/VnYKpUQkP2dn6NjR3B591Nx39GhOQLVmDezYYc5Hle3YMVi82NwAfHzMYX7ZQ/66dTPDL5EKqK5PwcPw3KwWwMDNaqFz3c6O75iIiIiIVBqGYZDoZv4bK9HNwDAMLJV8ASqFUiJSMg0amNt995mvExPNYX7Z1VQbNpgr/2VLSoIffzQ3yAm6cg/5Cw52+NsQERERERG5Gq08uJKMvwebZDibrwc0HVC+nSpjCqXETlUrFZQr4OsLN91kbgAZGbB9u301VVyuiaGzsiA62tzefdfc16hRznC/Xr2gTRtzvioREREREZEqxDAMpv0yDQzALMZn2i/TuKnJTZW6WkqhlNhUxVJBKUWurhAaam4TJphD+w4ftg+pdu2yH/J3+LC5LVpkvvbzg549cyqpunYFT89yeDMiIiIiIiKOcT71PB9EfcDm45vNQArAApuPb6701VIKpcSmKpYKShmyWOCaa8xtxAhz3/nzsH59zpC/TZsgJSXnnIQE+P57cwNwcYHOne2H/NWp4/C3IiIiIiIiUhqSM5LZGreVzcc3m1vsZv48+2eBbZ0tzpW+WkqhlABVt1RQHKx6dbj5ZnMDSE+HrVvtq6ni43PaZ2aawdWmTTBzprmvSRP7IX8tW2rIn4iIiIiIXHUysjLYGb/TFj5tOr6JXfG7yDKySnR+lpFV6aulFEoJYFZFVcVSQSlnbm7mqnzdusHkyebQvoMHcwKqtWthzx77cw4eNLeFC83XNWrYV1J16QIeHo5/LyIiIiIiUmVZDSv7z+xnc+xmWxXUthPbSM1MLfI8VydX3JzdSM5IxiD/3M6VvVpKoZTYqqScLc75Etux345ly7gt+Hv5l1PvpEqxWKBpU3MbOdLcd+YMrFuXU021eTOkpeWcc+4cLF9ubpAzt1XuoCogwPHvRUREREREKiXDMDiacNRWAbX5+Gai46JJTEss8jwnixNtAtrQJagLXYK70CWoC3EX4rj181sLPaeyV0splJKcKqkCHEs8RtDbQQxpO4TwkHB61u9ZKdNZuYrVqgW33mpuYAZS0dH21VRnzuS0z8gw561avx7efNPc17y5/ZC/5s3NAExERERERKQY8Rfj7SqgNsdu5lTyqWLPa1qzqRlA/R1CdarTCS83L9txwzDo9mG3AgtEcqvM1VIKpaq4oqqksmVYM/h0x6d8uuNT2ga2JSwkjPvb34+fh5+DeysCuLubK/T17AlTpphD/vbvzwmo1q41X+e2f7+5zZ9vvvb3t6+kCgkxrysiIiIiIlVaQmoC0XHRdiHU0YSjxZ4X5BNEl6AudA3uSpegLoQGhVKjWo0izymqQCS3ylwtpVCqiivp/wTZdsbv5J/f/5OnVj3FfW3vIywkjNCg0EqX1koFYrFAixbmNnq0uS8+3n7IX1SUWUGV7fRp+O9/zQ3MQKpLl5xqqp49oWZNx78XERERERFxmNTMVLad2Mam2E22Cqh9Z/YVe14Njxq24XfZVVBBPkGX9LWzC0SccMKKtdj2TjhVymophVJVWEmqpMAsFWzg14A63nVYH7MeMJexnLd1HvO2zqNz3c6EhYQxrN0wvN28HdV9kcIFBsLtt5sbQEqKGUxlD/lbt86ciypbWpq5f80aeO01c1+rVjnD/Xr1Mlf9ExERERGRCinTmsmu+F1280D9Ef8HmdbMIs/zcvWic93OdvNANa7R+IqDofSsdI4mHC1RIAVgxcqxxGOkZ6Xj7lJ5RnkolKrCLqVU8K/zf/HBPz4gyCeIyOhIPtnxiW0Sty1xWwhbHsYTK59gRPsRhIWE0aFOh7LuvkjJVasG115rbgBWK+zdaz/k7+BB+3P27DG3uXPN17VrY+nZE8/27WHAAHPIn5ubY9+HiIiIiIgUy2pYOXD2gN0QvK1xW0nJTCnyPFcnVzrU6WBXAdXKvxXOTs6l3kd3F3c2j80/N9XAd0I55WkQkGzhh4lRdscCvQIrVSAFYDEMI/+ag3JZEhMT8fPzIyEhAV9f3/LuTpGyJ1SLPh5d4lLBkKAQNo7ZiMVi4WL6RRbvXExEdARRx6Pyte9erzvhIeHc2+ZeqrlWK4u3UOasVivx8fEEBgbi5ORU3t2RsnbiRE5AtWYNbN0KmUX81KRaNejaNWfIX48eUL26w7orV4d6U5yJ9bYSfMGJmDcKrziVqkPfOyQ33Q+Sl+4JyUv3xJUzDIOYxBi7Cqio41EkpCUUeZ4FC60DWtsNw2tfu325hz6V5fNlSfMRVUpVUVdaKujl5sXozqMZ3Xk00cejiYyO5LM/PuNixkUANsRsYEPMBiaumMjIDiMJCwmjVUCrsnxLIlemTh246y5zA0hOhk2bcqqp1q+HhFzf2FJSYPVqcwNzbqs2beyH/DVqpFX+RERERERK0enk0/lWwjt58WSx511T/RrbJORdgrvQuW5nTT9zFVClVCmqSJVSAMcSjl1yqWA933qFXi8xLZFFOxYRER3BjpM78h2/ruF1hIeEc2erO8s9fS4J/dRC7GRlYd25k6QffsB3xw4s69bB4cNFnxMUlBNQ9e4NHTqAi34WUJlUlp9kSenR9w7JTfeD5KV7QvLSPVG0pLSkfCvhHT5/uNjz6njXsRuCFxoUir+nf9l3uBRUls+XqpSSYtX3q099v/p2+9ysFsDAzWqhc93Ol3Q9X3dfxncZT3hoOBtjNxIRFcEXu74gNTMVgN+O/MZvR37D39OfBzs+yLiQcTSt2bS03o5I2XJ2hnbtSKldG5/AQCxOThAbaz/kb9s2c76qbMePw5Il5gbg5QXduuVUU3XvDhUgwBYRERERKWtpmWlsP7ndLoDac2oPBkXX0VT3qE5oUKhdCBXsE1ypVqirzBRKSamzWCx0r9ed7vW68/aAt/lk+ydEREew9/RewCy3fGPdG7yx7g1ubHwj4SHhDG4xGFdn13LuucglCg6Ge+81N4ALF2DjRvshfxcu5LS/eBF+/tncAJycoH37nGqqXr2gQQPHvw8REREREQfKsmax+9Ruu3mgdpzcQYY1o8jzqrlUy7cSXtOaTRVAVWAKpaRM1axWkwndJ/BYt8f47chvREZH8p/d/7E9bFYdWsWqQ6uo412H0Z1GM7bzWBpWb1jOvRa5TN7e0K+fuYE5Ufoff9hXU8XE5LS3Ws3qqm3bYNYsc1/9+vZD/tq1M6u0REREREQqIMMwOHjuoF0F1Ja4LSRnJBd5nouTC+1rt7ergGod0BoXJ8UYlYn+NsUhLBYLfRr1oU+jPrwz8B0+3vYxc6LncPDcQQBOXDjB//3+f7zy+yvc0uwWwkLCuKXZLWWy9KaIw7i4QKdO5vboo+a+o0dzAqq1a2HHDsg9td+xY7B4sbkB+PiYw/yyh/x162aGXyIiIiIiV6HYxNh8K+GdSz1X5DkWLLT0b2m3El6HOh3wcPFwUK+lvCiUEocL9ArkyV5P8kTPJ/jp0E9ERkfy9d6vyTKyMDD47s/v+O7P76jvW58xnccwutNogn2Dy7vbIqWjQQNzu+8+83VCAmzYkFNNtWGDufJftqQk+PFHcwOzaqpjR/shf8H6/0NEREREHO9sytl8K+HFXYgr9ryGfg3pEtyFrkFdbSvh+bprrtWqSKGUlBsnixP9m/Snf5P+HE86zkdbP2LulrkcTTgKwLHEYzz/6/O8uPpFBrcYTFhIGP2b9MfJolUppBLx84MBA8wNICMDtm+3r6aKy/WNPSsLoqPN7d13zX2NGuUM9+vVC9q0MeerEhEREREpJRfTL7IlbotdAJU98qUogV6BdkPwugR1IcArwAE9lopAoZRcFYJ8gnj2umd5uvfT/HDgByKiI/jfn//DaljJMrJYtncZy/Yuo3GNxozrPI4HOz1IoFdgeXdbpPS5ukJoqLlNmGAO7Tt8OCegWrsWdu2yH/J3+LC5LVpkvvbzg549cyqpunYFT89yeDMiIiIiUhGlZ6Wz4+QOuyqo3ad2YzWsRZ7n6+6bbyW8+r71NRG5FEqhlFxVnJ2c+Ufzf/CP5v/gaMJRPtzyIR9u+dBWAnro3CGm/jSVab9M485WdxIWEkbfRn31kJPKy2KBa64xt/vvN/edO2eu7JcdUm3cCKmpOeckJMD335sbmHNbde5sP+SvTh3HvxcRERERuepkWbPYe3qv3TxQ209uJz0rvcjzPFw86FSnk10FVLNazTSyRS6JQim5ajXwa8CL17/ItOumsXz/ciKiI1h5cCUAGdYMvtj1BV/s+oIWtVoQFhLGyI4jqVmtZjn3WsQBatSAW24xN4D0dNi61X7IX3x8TvvMTNi0ydxmzjT3NWliP+SvZUsN+RMRERGp5AzD4K/zf+VbCe9C+oUiz3O2ONOudju7Cqg2AW1wdXZ1UM+lslIoJVc9V2dX7mh1B3e0uoODZw8yd8tcPtr6EaeSTwGw78w+Jq+czNM/Pc29be4lLCSMnvV7qnpKqg43N3NVvm7dYPJkc2jfwYP2Q/727LE/5+BBc1u40Hxdo4Z9JVWXLuCh1U5EREREKrK4pLh8K+GdSTlT7HnNazWnS1AXugZ3pUtQFzrW6Ug112oO6LFUNZcVSh08eJDAwEB8fHxKuz8iRWpSswmv3vgqL/R9ga/3fk1EdAS/Hv4VgLSsND7Z8Qmf7PiEtoFtCQ8JZ0T7Efh5+JVvp0UczWKBpk3NbdQoc9+ZM7BuXU411ebNZoVVtnPnYPlyc4Ocua1yB1UBmpBSRERE5Gp1PvU8UcejbAHUpthNxCbFFntefd/6tuF3XYK6EBIUQnWP6mXfYREuM5QaNWoUfn5+LM/+x4uIg7m7uDOk7RCGtB3C3tN7mRM9h4+3fcy51HMA7IzfyaPfP8qTq57kvrb3ER4aTmhQaDn3WqQc1aoFt95qbgBpaeYKfrmrqc7k+qlZRoY5b9X69fDmm+a+5s3th/w1b24GYCIiIiLiUMkZyWyN22q3Et6fZ/8s9jx/T/98K+HV9q7tgB6LFOySQ6n333+fTZs2sX79+rLoj8gla+nfkrcHvM3/3fB/LNm9hMjoSNYdWweYD+t5W+cxb+s8OtftTHhIOPe1uw9vN+9y7rVIOXN3N1fo69nTfG0YsH9/Tki1Zg38meeDzf795jZ/vvna39++kiokxLyuiIiIiJSajKwM/oj/w24eqF3xu8gysoo8z8fNh5CgELsQqqFfQ01zIleVSwqlPv74YyZNmsR7773HsmXLWLVqFT4+PgQEBFCnTh3q1atHw4a6yaV8VHOtxgMdHuCBDg/wx8k/iIyOZOH2hSSlJwGwJW4L45aP4/GVjzOi/QjCQ8NpX7t9Ofda5CphsUCLFuY2erS5Lz7eHPKXHVRFR5sVVNlOn4b//tfcwAykunTJqabq2RNqavEBERERkZKyGlb2nd5nNw/UthPbSMtKK/I8d2d3OtbpaFcB1cK/hVbCk6tesaFUYmIiv//+O5GRkaxatYr33nuP8PBwnJycCA4OJjk5mYSEBKxWKxaLBQ8PD7p27crLL79Mr169HPEeRPJpV7sd79/yPq/e+CqLdy4mMjqSqONRACSlJ/FB1Ad8EPUBPer1ICwkjHvb3KuJ+0TyCgyE2283N4CUFIiKygmp1q0z56LKlpZmHluzBl57zdzXqlXOcL9evcxV//SDCxEREREMw+Dw+cP8dPAn9u/YT1RcFNHHo20/VC+Mk8WJtoFt7Sqg2ga2xc3ZzUE9Fyk9xYZSGzdu5M477+SOO+5gx44dNG3a1Hbsp59+onnz5hiGwZkzZ4iJiWHXrl18/PHH3H///Rw6dKhMOy9SHG83b8Z0HsOYzmOIOh5FZFQkn+38jOSMZADWx6xnfcx6Jq6YyMgOIwkLCaNVQKty7rXIVapaNbj2WnMDsFrNVf2y56RaswbyPvf37DG3uXPN17Vr2w/569TJXD1QREREpJI7eeFkvpXwslcUL0rTmk1tAVTX4K50qtsJT1dPB/RYpOwVG0r17duXo0ePUrt24ZOfWSwW/P398ff3p2PHjrRu3VpVUnLVCQ0KJXRwKG/e9CaL/lhERFQEf8T/AZgrVfx747/598Z/06dhH8JCwri9xe3l22GRq52TE7RpY27jxpn74uLsh/xt3QqZmTnnnDwJS5eaG5hBV9euOUP+evSA6tUd/lZERERESlNCagLRcdF280AdTTha7HnBPsF2K+GFBoVSo1oNB/RYpHwUG0q5uroWGkgVNnfUH3/8Qb169a6sZyJlxM/Dj4e7PMz40PFsiNlARHQEX+76ktTMVABWH1nN6iOr8ff0Z0izIUzoPYFm/s3KudciFUTdunDXXeYGcPEibNqUU021bh0kJua0T0mB1avNDcyhfW3a2A/5a9RIQ/5ERETkqpWSkcK2E9vsVsLbd2ZfsefVrFaT0KBQWvu1pk/TPnSr1426PnUd0GORq8clr76XW8eOHWnZsiV9+/ZlyJAhdO3aFYAdO3YwatSo0uifSJmxWCz0qN+DHvV7MHPATBZuX0hEVITtG8jp5NPM2j6LWdtn0b9xf8JDw7m1+a24OruWc89FKhAvL7j+enMDyMqCXbtyhvutXQtHjuS0NwzYudPcIiLMfUFBOQFV797QoQO4XNG3LxEREZHLkpGVwa5Tu+wqoHbG7yTTmlnkeV6uXvlWwrum+jUYhkF8fDyBgYE4OWlScql6SvSp/rXXXuO+++6jQYMGdvtfeuklzp07x6pVq5g5cybXXXcds2fP5s033yyTzoqUlZrVajKx+0QmdJvAb0d+IyI6gq92f0WG1Vxp7MdDP/LjoR+p412HMZ3GMDZkLA38GhRzVRHJx9kZ2rc3t/HjzX2xsfYh1bZt5nxV2Y4fhyVLzA3MoKtbt5xqqu7dwdfX4W9FREREKjerYeXPM3/azQO19cRW2wiLwrg5u9Ghdge7lfBa+rfE2ck5X1vDMMqq+yIVgsUo5v+CmJgYbrnlFvbt28dzzz3HM888g8ViwdnZmT179tC8eXMAdu/ezdSpU/n555/57LPPGDx4sEPewNUkMTERPz8/EhIS8K2g/0CqN8WZWG8rwReciHkjq7y7U65OJJ1g1tpZfLb/Mw6ds5+82cnixM1NbyY8NJybm95c4DcYqXysVqt+kuUISUmwcWPOkL/16+HChcLbOzmZIVfuCdQbOCY01jNT8tJzQnLT/SB56Z64ehmGwbHEY3YVUNHHo0lISyjyPCeLE638W9EluAtdg7rSJbgL7QLb4e7iXqKvq3tC8qosny9Lmo8UWylVr149duzYwUcffcSECRPYsGEDX331Vb5Et3Xr1nzzzTe88cYbDBkyhJ9//pkePXpc+TsRKSeBXoE80vERnu//PL8c/oWI6Aj+u/e/ZBlZWA0r3/35Hd/9+R31fesztvNYRnceTZBPUHl3W6Ti8/GBG280NzAnSv/jj5xKqrVrISYmp73ValZXbdsGs2aZ++rXtx/y166dWaUlIiIigjlVR+4AanPsZk5ePFnseY1rNLYbgte5bme83bwd0GORyqnEk3I89NBDdOvWjX79+nHHHXdw8uRJatWqla/dlClTiImJYeTIkezatQtXV82/IxWbk8WJ/k36079Jf44nHeejrR8xJ3oOxxKPAXAs8RjP/focL6x+gcEtBhMeGs6NjW/EyaKfdIiUChcX6NTJ3P75T3Pf0aP2IdWOHeZ8VNmOHYPFi80NzKCre/ecIX/duoH3JX6APHoUTp+229X2pEFgIgSmGLBli317f3+HVWyJiIhI4ZLSkvKthHf4/OFiz6vrXTffSni1PPP/G1hELl+xw/fy2rVrFz169ODRRx/llVdeKbDNxYsXadKkCS+//DJjxowplY5WBBq+V7kUVUqbZc3i+wPfExkdyXf7v8PA/n+jxjUaM67zOB7s9CCBXoGO7LaUIZVXX8USEmDDhpy5qTZuhOTkwts7O0PHjvZD/oKDC29/9Ci0aAGpRc8hYcfDA/btUzBVxeg5IbnpfpC8dE+UvdTMVLaf2G5XAbX39N58n9fzqu5R3a4CqktQF4J9i/hsUEp0T0heleXf5KU2fC+vNm3aMHv2bPbu3VtoGy8vL95880369u17qZcXqRCcnZwZ1HwQg5oP4mjCUT7c8iEfbvmQuAtxABw6d4ipP01l2i/TuLPVnYSHhtOnYR8sWtZepGz4+cGAAeYGkJEB27fbV1PFxeW0z8qC6Ghze/ddc1+jRjnD/Xr1gjZtzPmqwKyQupRACsz2p08rlBIRESkjmdZMdp/abVcB9cfJP2yLFRXG09WTznU724VQTWo00Wd1kXJwyZVSUjhVSlUul/pTi4ysDL7d/y2R0ZGsPLgy3/EWtVoQFhLGyI4jqVmtZll0WcqYfpJVgRkGHD6cE1KtWQO7dhV9jp8f9OxpBlQBARAWdulfNzoaOne+rC5LxaTnhOSm+0Hy0j1x+QzD4MDZA/lWwkvOKKIyGnB1cqV97fZ2FVCtAlrh4nTJ9RllQveE5FVZ/k1eZpVSIlIwV2dX7mx1J3e2upODZw8yJ3oO87fN51TyKQD2ndnH5JWTefqnpxnSdghhIWH0qNdDP5ERcQSLBa65xtzuv9/cd+6cubJfdlC1aZN9NVRCAnz/vbmJiIiIQ8UmxtoFUJuPb+Z86vkiz7FgoaV/S1v41DW4K+1rt8fDxcMxnRaRS3ZFodQ333zDjTfeyLFjx2jdujVZWRU3xRMpTU1qNuG1/q/x4vUvsmzvMiKiIlh9ZDUAaVlpLNy+kIXbF9IusB1hIWGMaD8CPw+/cu61SBVTowbccou5AaSnw9at9kP+4uOv7GvMmQNNm4KnZ85WrZr967zH3N3NEE1ERKSKOJN8hqjjUXbzQGVPi1GURtUb5VsJz9e9Yo5YEamqig2lzp07x9y5c+32WSwWRowYwR133GGbW0qjAEXyc3dxZ2jboQxtO5Q9p/YwJ3oOC7Yv4FzqOQD+iP+DR79/lCdXPcmwtsMICw0jNCi0nHstUkW5uZmr8nXrBo8/bg75O3DADKf++1/4+utLv2Zk5KWfY7EUHlgVFWZd6jE3N4VfIiLicBfSL7AlbotdBdShc4eKPa+2V+18K+EFeAU4oMciUpaKDaXi4+OZOnUqHTt2ZPv27bRv355du3bRp08fDMPg/r+HQVgsFm644QYA3NzcmDhxIgMHDizb3otUIK0CWjFz4Exe6fcKS3YvISIqgvUx6wFIzkjmw60f8uHWDwmpG0J4aDhD2w7F2+0Sl6wXkdJjsUCzZubWvv3lhVKXwzDg4kVzK0tOToUHVqUZgCn8EhGpstIy09hxcoddBdSe03uwGtYiz/Nz9yM0KNRuHqh6vvU07YVIJVSi4XsWi4VffvmFGjVq8MMPP9CyZUtbZVTnvydw3bx5M7169QLgwIEDPPzwwxw6VHziLVLVVHOtxgMdHuCBDg+w4+QOIqMi+WTHJySlJwEQHRfN2G/HMnnFZO5vfz9hoWG0r92+nHstIpfl3/+GoCBISYHk5IK3khy7eNFcMbA0Wa2OC7+KCrNKKwBT+CUiUq6yrFnsOb3HrgJqx8kdpGelF3meh4tHvpXwmtZsipNFk36LVAUlnlPKYrHYbdkmTpyI1WolMjKSl156CYA9e/Zw3XXXlX5vRSqZ9rXbM+sfs3it/2ss3rmYiKgIouOiAUhKT2J21GxmR82mR70ehIeGc0/re6jmWq2cey0iJda7d+mtvpeRUbIw60oCsLIKvy5cMLeylB1+XW7QVdJzFH6JiGAYBn+d/8sWQG2K3cSWuC1czCj6Bx3OFud8K+G1CWxz1ayEJyKOV+L/+7MrowzDyDd/VO6QauPGjaSnp/P000+XUhcrntTUVNzc3PLtd3JystufmnuVp1Jsm5aWVugcXxaLBXd390LbGpkGZJi/pqWl2bVNT0/Hai281NbDw6Pc27q7u9vux4yMjCIn3y+urdVqJTU1ldTUVKpVq2Zrm5mZSWZmZomuW1xbNzc3nJyc8HbzZlT7UYxoPYLouGjmbZvHF3u+sC1xuz5mPetj1jPxh4mM7DCS0R1H07R600Kv6+rqirOzc4n6kLttVlYWGRkZhbZ1cXHBxcXlkttarVbS0wv/KdnltjUM8z4tjbbOzs64uroW2Tb7nsjIyLD9v1HcdR31/72jnhFFta3Uz4jMTFwLbVm4tLQ0jDx/N5fzjADItFjIdHc3J0KvUaPotpfw/32+trnCL0tKCi7p6Tinp0NyMllJSWQlJUFKCpbsICv798nJOKWm4pSaCsnJGMnJGBcv2oVhllyhmKUCh1/G3wGVUa1aTmBVrRrG3/t8nJ2x1Kpla5Pp7m5rZ2SHXH+/tnh54ernZwu9UrPnFCsg/NIzouzblubnCMj5vpH7z6gsPkeUdlt9jrj0tiX5HJHdv9zvu6J8jjiTdsY2/G5jzEa2nNjCmZQzhZ6TrXnN5oTUDSGkTgghdUPoENjB9gNWPSNMVqtVz4i/6RlhMrJy/fu8gjwjCvocUdQ5uV1yJF3UON7z589z9913869//YvJkydf6qUrjQceeMB2w+UWGhrK888/b3s9YsSIQm+wtm3bMmPGDNvr0aNHk5iYWGDbZs2a8fbbb9teP/zww8QXsmJU/fr1mT17tu31pEmTOHbsmO312d0GuMDZTIOHEx9m3rx5tmNTp07lzz//LPC6vr6+LFq0yPb6+eefZ+fOnQW2dXd35z//+Y/t9YwZM4iKiiqwLcC3335r+/3bb7/N2rVrC227ZMkS2zeWWbNm8dNPPxXa9tNPP8XPz1zx7sMPP+R///uf3XHDMEhPT8fNzY2PPvqIwMBAABYuXMiyZcsKve6sWbNo0KABAF9++SWff/55oW3ffvttmjVrBpirWc6fP9927Frna4mtGcuRgCMkeZpD+86lnuOdje/wzsZ3qJlUk4bxDalzvg7OhrPddZ977jm6dOkCwOrVq3nnnXcK7cNTTz1F7969AVi/fj2vvfZaoW0nTpxIv379ANiyZQsvvvhioW3Dw8P5xz/+AcCuXbt45plnCm374IMPcueddwJw8ODBIp8f9913H8OGDQPg2LFjPPLII4W2veOOO3jooYcAOHXqFKNHjy607S233ML48eMBSExMZMSIEfnaZN8TN998M5MmTQLMh+4999xT6HV79erF1KlTba+LalsRnhG5BQYGVplnxNfLllH431zhnnrqKQ762a+sWVrPiLxeeeUV2rVrB8CKFSuIiIgotO1lPyPWrOG1OXMKbZv7GRG1ebP5jPAreGXR8WPGcEvfvpCczL6tW3nvtddwz8rCLSsL9+zNasU9K4trO3emfdOmkJLCuePH2fjzzzltcp9jtVLLwwNvJydbMFYW4Zfl7/CrsE9EuT9cWeCSAs3sfxplAWnOzqQ5O5P+968uPj4E5VrNcVNUFMkWi9nGycnWPs3ZmYCGDbln5EhbYDbjtdc4m5pq1ybNyYlMJyeaNW+uZ8TfSvNzBOR831i4cCF16tQBHPc5Ii+HPCP0OQIo/HMEmPdE165defbZZ4Gr83NEunM6CV4JnPc8z3mv81z0u8gFp+ID/2pp1ahr1GXcP8bRJbgLIXVDeHry0xxbeYxf//4vNz0jTIZh8Oqrr1K7dm1Az4iq/oyAv2dW6GP+/mp8RuRV2L81igoVcytxKLV9+3bbr9npce6AyjAM5syZQ5s2bQgPDy/pZUWkEK5ZrjQ61YiGpxpyzusctQbUYmXsStKyzAfHWZ+znPU5i1uGG/VP16fB6QZ4pXmVc69FRErGcHExAys/P9JPn+aIb+FLeNcdPJj2f3+YPP3nn8w6fbrQtnYfJo8e5bHx4+0CrNxb327duKl3b0hOJvHkSb78+ONC29arWZMG/v5mxdjFi5w6ciTneGYmzoX26PI4A55ZWXjmDtUuXIC4nCXSi5woYedO+O4728sXCmmWBWSsXAmLFtnCrmfj47lotdoHWH9v7rGx8NJLtrbd9u6lQUJCvgAtzdkZn+RkOH/ebFvAD+tEpPxdTL/I1hNb2Vt9LydqneC813mSPZKLPc8tw43qF6vjl+xH9YvVqX6xOu6Z7tSvX5+nej/lgJ6LSGVhMQqruf7bvn37aNWqlf1JFgvr1q2jR48e7N27F8MwaNWqFXfddRcTJ060TXhe1SQmJuLn58fJkyfxLeDDdUUou2/ytCfHvQyCLlo49GpKlS6ptVqtxMfHExgYWObD90ra9nzaeRZsW0BkdCT7zuzL16Zfo36M6TiG21vdjoebR4muq5JaU0mH78XHx1O3bl0N3yugbaV+Rhw8iEubNliK+LvOy3B3J23HDvj7p5kFXVdl95XkGWEY5rDHlBSsFy5w/vhx6vj64pSainHxIhkJCfmGOmb/3pKSYhseSXIyWRcu2LUhJSVn2OPFi1iKuL+vas7O5nDH7GGPuebqMjw9wcMDZx8fW9iV5e6OtVo18PCwH/b493G36tVtr9NdXLD+3Q5X13zDHst7+F58fDz169cv8f9zekbkb1vhnxG5WK1Wzpw5Q3BwME5OTg79HJGelc7OUzuJjotmy4ktRJ+IZtepXcWuhOfj5mO3El57//bU96lf4AgafY649LZWq5WEhARq166Nk5OTnhFV/BkB0OQZT477GQRfcOLY65kV9t8aiYmJ1K5dm4SEhALzkWzFhlLZF8sWFxdHUFAQ3t7evPHGG4SFhXHy5ElatWpFYmIi3t5Vdwn77FCquD/0q1m9Kc7EelsJvuBEzBulPOShgskdSmU/oK8WhmGw+shqIqIiWLpnKRlW+wd2Xe+6jO40mrEhY2ng16CQq8iluprvCXGAo0chT4XOwHdCia9mEJhi4YeJeYYG+PvnC6Sk8ivT50R2+HWlE9qXZLXHChx+leqqjoXtLyD8Koi+b0hejronrIaVfaf32SYh33x8M9tPbLdV3BfG3dmdTnU72a2E17xWc62EV4b0nJC8Ksu/yUuajxQ7fG/48OF06dKFSZMmkZKSwoABA2jVqhUff/wxTz1llmY6OTkRERFRpQMpEUeyWCz0bdSXvo36En8xnvlb5zNnyxwOnTsEQNyFOF7+/WVeWfMKtzS7hbCQMG5uejPOTqU9wESkCmnQIF/ItLO2hVhvg+ALltJbZU+kMBaLOQG6mxtUr152Xyd3+FWWAVhZhF9ZWZCUZG5lKTv8Kibksnh44ANY/P3By+vSw7EShl9StRmGwZGEI7aV8DYf30z08WiS0ov+/8DZ4kybwDZ2AVS7wHa4Omu4rYg4TrGh1NChQ5kwYQLLly/nyy+/ZOPGjUydOpXmzZvj7+9v1/bVV1+1e33o0KHS7a2I5BPoFchTvZ9iSq8prDq0ioioCL7Z9w1ZRhZWw8ry/ctZvn85DfwaMLbzWB7q9BBBPkHl3W0REblalVf4VVyYdbkBWHJyuYVfFuCKZnvMHX6VRQVY9jGFXxXKyQsnbSvhZYdQp5MLn2svW7OazegS3MUWQnWq2wlPV08H9FhEpHDFhlKDBw/m2muv5Z577mHgwIH8+uuvfPzxx9x4442MHj2aQYMGMXjwYEf0VUSK4GRx4qYmN3FTk5s4nnSceVvmMXfLXI4lmqsiHU04yrRfpjH91+nc1vI2wkLCuLHxjSrHFhGR8lHe4VdZBGCVofLrcoYzlvSYJry/ZAmpCUQdj7KFT5tjN9s+2xWlnm89uwqokLoh1KhWwwE9FhG5NCVafa9GjRosX76cm266iQceeICvvvqKESNG4O3tzT333EPHjh2ZNm3aZXciJSWFRx55hCVLluDp6cmECRMKXdJx6dKlPPXUUxw7dozevXsTERFB06ZNAbN09cUXX2TWrFmkpqZyzz338O677+LlZf6MKiEhgfHjx/PNN9/g6+vLpEmTmDJliu3ap0+fZuzYsaxYsYKAgACef/552xKPIhVJkE8Q0/pM45lrn+H7A98TERXB//78HwYGWUYWS/csZemepTSu0ZiwkDBGdRxFoFdgeXdbRESk9JVj+GW9cIFzsbHUcHfHKS3t0oOuwvZXtvCrtOb6quDhV0pGCltPbLWrgNp/Zn+x59WqVsuuAqpLcBfqeNdxQI9FRK5ciUIpMFcl+PLLL+2G5N1+++288sorXHPNNVfUiSlTprBjxw42bdpEbGws99xzD9dccw333XefXbvdu3czZMgQpk2bxrBhw5g3bx433HADu3btwsfHh7lz5/Lmm2/y0Ucf0a5dOyZOnMiIESNYtmwZAOPHj2fLli38/PPPZGZmMnz4cNzc3JgwYQIAo0aNwmq1snPnTqKiorj//vtp2bIlPXv2vKL3J1JenJ2cGdR8EIOaD+LI+SN8uOVDPtz6IScunADg0LlDPLXqKZ79+Vnuan0X4SHhXNfwugJXUxEREZEiFBR+Wa1k1KkDgYFQWhMYGwakp5ftZPcVPfxycSm7oY6lFH5lZGWwJ36POQn53yHUzvidZBlFT2rs7eZNSN0QW/jUJagLjao30mc3Eamwil19r6ylpKRQs2ZNvv/+e/r27QvAiy++yKpVq/jtt9/s2j722GNs27bNtt8wDFq3bs3kyZMZO3Ys7du35/bbb+fFF18E4Pjx49SrV499+/ZRo0YNAgMDWbVqFTfccAMAixcv5oknniAmJoa//vqLxo0b89dff9GoUSMAHnroITIzM1m4cGGJ3otW36tcKutKGBlZGXy7/1sioiL48dCP+Y639G9JWEgYD3R4gJrVapZDD69elfWekMunZ6bkpeeE5Fah74fs8KusV3ssi/DLUVxcShRkWT2r8ad3Gps9z7PJNZ6NRgw7OEkqhS93D+Dm7EaHwPZ0rdfNFkK1qNVCC9dUMhX6OSFlorJ8viy11ffyOnfuHAkJCbbgJtusWbMYMmRIvsnPi7Nt2zYyMjLsqpF69erFjBkzMAzDLvU/ePAg3bt3t722WCy0b9+e9evXM3bs2HzHg4KCCAgIYP369bRq1QrDMOyOd+jQgdjYWI4ePcqGDRuoV6+e3fvq1asXr7/++iW9H5GrnauzK3e2upM7W93JgbMHmBs9l4+2fWSbIHPv6b1MWjGJp396mnvb3Et4SDjd63XXT+BERESqEosF3N3NrUYZzkWUO/wq69UeS/tn8ZmZkJhobtlvBzjmB5uDYHMwbPaCqJqQ6JGrQQGcrND6FHQ5Dl1izV/bn0zHLSsKXLaB5ydlN9dX7tUeRUQc7JJCqc8++4zw8HDGjRvHm2++adt/7Ngx/vnPf/Lkk0+yaNEibr/99hJfMyYmBn9/f9zc3Gz7goKCSE1N5cyZM3YhV2BgIHFxcXbnHz16lBp/f6PMe/zChQucPXuWEydO0KdPHwDi4uJo0qSJ7VyAEydOEBMTQ3BwsN21g4KCiI2NLbTvaWlppKWl2V4n/v0NyWq1Yq2oP/HJpTK8hythtVoxDKNS/zk0rt6YGf1mML3PdJbuXcrcLXNZfWQ1AKmZqSzcvpCF2xfSLrAdYSFhDG83HF/3ilkFWBqqwj0hl0/3hYCeE2JP90MJubqCn5+5lZW84Vdxc3n9fcxSzPFTWYls9kpgs98Fomqlsbl2FvHexXenydmc8KlLLHQ6Ad7phTQuIPwqC0Z25VdBgVVBoVa1ahgFhV4lWe1RbPSckKJU5PuipH0vcSi1YcMGRo4cyQ033MD9999vd6x+/fps2rSJZ555hnvuuYeNGzfSuXPnEl03NTUVd3d3u33Zr1NSUuz2Dx06lMGDBzNixAj69+/PokWLiIqKsgVOQ4cOZcaMGfTp04f69evbJjFPT0+nYcOG9OjRg4kTJ7Jo0SLS0tJ46aWXbMcL60fePuQ2Y8YMXnjhhXz7T506RWpqaone/9UsPj6+vLtQrqxWKwkJCRiGUSVKafsF9qPfwH7sP7efT/d8ypf7viQhPQGAP+L/4NHvH+XJH5/k9qa380DrB+gQ0KGce+x4Ve2ekEtT1Z+ZYtJzQnLT/XCVuswQLCk9iR2ndrDt1DZzi/+LmAsxxZ5Xx60Wnao1pZP7NXSy1KPFRT/q+XrhVDcNS0oKlr9Drot//95uS021e03uY6Vc+WW5jPDrcuroDRcXjGrV8m14eBS436hWDaOIYxS23+WSBwWVCz0npCgV+fNlUgnnDyzx/6mvvfYa1157LT/88EOBw3hCQ0NZsWIFvXv35pVXXuE///lPia7r4eGRL8DJrj6qVq2a3f4BAwbw4osvcscdd5CRkUG/fv0YPnw4CQnmP5ynTZvGkSNHaNmyJW5ubowfP542bdrg4+MDwKeffsqQIUOoWbMmfn5+vPTSS6xduxYfH59C+5G3D7k9/fTTTJ482fY6MTGR+vXrExAQUGHnlMotMLBqr8ZmtVqxWCwEBARUqW8QgYGB9G7Rm5kZM/ly95fM3TKX9THrAUjOTOazvZ/x2d7PCKkbwriQcdzX5j683LzKudeOUVXvCSmZqv7MFJOeE5Kb7oeKKzUzle0nt9smIY86HsW+M/swCht/97caHjUIDQqlS1AX269BPkG241arlVOnTuFzhfeEYRgYeSu/CqsCy/V7S2EVYrmqv/JuZRF+WRww4X2BlV/Zm4dHgfuN7GquSxkieYXhl54TUpSK/PnSw8Oj+EZcQii1efNm3nnnnSLnlbFYLIwfP55nnnmmpJclODiYM2fOkJGRgevfpZyxsbF4eHhQq1atfO2feuopJk+eTFJSEjVr1uSuu+6icePGAHh6evLZZ58xZ84cLBYLnp6eBAYG2o43btyYzZs3c/bsWXx8fNi7dy8Wi4VrrrmG4ODgfEMDY2Nj8w3py83d3T1fdRWAk5NTpXigVIb3cKUsFkul+fu8VF7uXjzY6UEe7PQg209sJzI6kk93fEpSuvkBIjoumrDlYUz5cQr3t7+fsJAw2tVuV869LntV+Z6QoumekGx6Tkhuuh+ufpnWTHaf2m0LoDYf38yOkzvItGYWeZ6nq2e+lfAa12hc7DycpXZPZIcnBfybqdQYBqSllf5cXwXtr6CVX7i6XtlcXx4eeGRk4FS3Lk7e3oWfV0Eqv+QSHT0Kp0/b7Wp70iAwEQJTDJy2bbNv7+8PDRo4rn9XoKTPuBLf2WfOnKF27drFtmvQoAGnTp0q6WXp2LEjLi4urFu3zjYM7/fff6dr1675HuiLFy8mOjqaN954g5o1a5KcnMzKlSv5/PPPAZg6dSrt2rVj+PDhtuskJCTQu3dvrFYrt9xyCzNnzqRVq1YAfP3113Tu3BlfX1+6detGTEwMhw8ftk12/vvvv9tNjC5SVXWo04HZ/5jN6/1f5/M/PiciOoItcVsASExLZNbmWczaPIue9XsSFhLGPa3voZpr4VWGIiIiIuXBMAwOnD1ghk9/h1Bb4raQkln4lB0Ark6utK/d3i6AahXQChenSh4UWCxmVZGHR9lPeJ+WVjaT3efdX9oT3mdkQEKCuV0GJ6BEf7LZ4VdZTnav8Muxjh6FFi0gz4itH2y/M+CTEPtzPDxg374KE0yVRInvuHr16vHnn39y7bXXFtnu0KFDl1Ri5unpyciRI3n88cdZsGAB8fHxvPvuu0RGRgJw+vRpfHx8cHd3p1mzZjz44INce+21tGvXjmeffZbGjRtzyy23AFC7dm2ee+45WrdujcVi4dFHH2XcuHG2idDd3d158skneffdd9mzZw9vvfUWH374IQBNmjShf//+PPzww7z33nts376dL774gl9//bXE70WksvN282ZsyFjGhowl6ngUEVERfL7zc5IzkgFYd2wd646tY+IPExnVcRTjQsbR0r9lOfdaREREqiLDMIhNirWrgIo6HsX51PNFnmfBQquAVmYA9XcI1aF2B9xd8o+QkFKSO/wqS7nDr7Jc7fEqDL9KLHf4VVpBV0H7FX6ZFVKXOhd1aqp5XlUMpQYMGMD777/P/fffbxtml1daWhrvvPMOffv2vaROvPXWWzz88MN069YNb29vnnnmGe69914AAgICmD9/PqNGjSIkJIRZs2bx2GOPER8fz/XXX8/y5cttZWGPPfYYhw8fpn///litVoYNG8Zbb71l+zqRkZGMHTuWdu3aUatWLV555RXuvvtu2/GFCxcyevRo2rZtS2BgIHPmzFGllEghQoNC+XDwh7x101t8uuNTIqIj2Bm/E4BzqeeYuWEmMzfMpG+jvoSFhHFHyzv0YU5ERETKzJnkM3YVUJuPb+bEhRPFnndN9Wts1U9dgrrQuW5nfNx9HNBjcbjc4VfNmmX3dfKGX0UEWdaLF7lw6hTeTk44FTbfV1EBWGUJv8oqAFP4ddWzGEbJ7uKYmBjat29P27Ztee211+jRo4fd8e3btzNhwgQ2btxIdHQ0rVu3LpMOX80SExPx8/MjISGhwk50Xm+KM7HeVoIvOBHzRlZ5d6dcWa1W4uPjCQwM1DwQJWAYButj1hMRFcGXu74kLSvN7niAZwAPdXqIcSHjaFyjcTn18sronpC89MyUvPSckNx0P5SdC+kXiD4ebQufNsdu5q/zfxV7Xh3vOnYVUKFBofh7+jugxybdE5LXFd0TBYVfZVUBVlEVFH6VdgB2ueHXli0QElJ8u7yio6Fz50s/z8FKmo9c0vC9r7/+mrvuuovevXtTo0YNrrnmGlxdXTly5AgnTpygVq1aLFu2rEoGUiJVncVioWf9nvSs35OZA2aycPtCIqIj2H9mPwCnkk/x2trXeG3ta9zU5CbCQ8IZ1HwQrs4FV16KiIiIAKRlprHj5A67AGrP6T1YDWuR51X3qG5bAS87hAr2CS52InKRCqM8K7/KKgArbY6q/HJzu/Qw69y5su1TBXFJcd51113Hvn37iIyM5OeffyYmJgYnJyc6duzITTfdxMiRI6levXoZdVVEKopanrWY1GMSE7tPZPWR1URERbB0z1IyrBkArDy4kpUHV1LXuy5jOo9hTOcxNPCrPOOiRURE5PJkWbPYc3qP3RC87Se22z5DFKaaSzU61e1kC6C6BnelSc0mOFlUjSRyxRwZfqWmlt6KjkUdK23p6eZW1uFXJXTJNWY1a9bk6aef5umnny6L/ohIJWKxWOjbqC99G/Ul/mI887fOJzI60lZeH3chjpd+e4n/+/3/uKXZLYSHhDOw6UCcnZzLueciIiJS1gzD4NC5Q/lWwruYcbHI81ycXGgX2M5uJbw2gW0q/0p4IpWdxWJWFFWr5pjwyxGrPUqx9OQWEYcI9Arkqd5PMaXXFH48+COR0ZF8s+8bsowsrIaV5fuXs3z/chr4NWBs57GM7jSauj51y7vbIiIiUkqOJx3PtxLe2ZSzRZ5jwUIL/xb5VsKr5lrNQb0WkUond/hVlnKHXwUFVn/8AZMnl20fKoASh1KNGxc8MbHFYsHLy4uAgAD69evHo48+WmEn+RaRsudkcWJA0wEMaDqA2MRY5m2dx9wtc4lJjAHgaMJRpv0yjRdWv8DgFoMJDwmnX+N+Kr8XERGpQM6mnCXqeJRdCHU86Xix5zX0a2i3El5IUAi+7vq3hYhUQLnDr1q18h8vy2qwCqTEoVSDBg0KnRQwLS2NgwcP8ssvv7BgwQI2bNhAjRo1Sq2TIlI5BfsG81yf53jm2mf4/s/viYiO4Ps/v8fAINOaydI9S1m6ZylNajRhXMg4Huz4IAFeAeXdbREREcnlYvpFtp7YahdAHTh7oNjzAr0C862EF+gV6IAei4jI1aLEodSvv/5abJvo6GhuvfVWXnzxRWbOnHkl/RKRKsTFyYVbW9zKrS1u5cj5I8zdMpd5W+dx4sIJAA6eO8hTq55i2i/TuLPVnYSHhHNdw+u0eo6IiIiDpWel88fJP+zmgdp1alexK+H5uvsSUjeErsFdbSFUfd/6+l4uIlLFleqcUiEhITz11FO89957CqVE5LI0rN6Ql294mef7PM83+74hIjqCVYdWAeYH4cU7F7N452Ja+rckLCSMBzo8QM1qKn0VEREpbVnWLPad2WdXAbXtxDbSs9KLPM/d2d1uJbwuwV1oXqu5huKLiEg+pT7Refv27Tl+vPjx4iIiRXF1duWu1ndxV+u7OHD2AHOi5zB/23xOJ58GYO/pvUxaMYmnf3qaIW2GEBYSRvd63fUTVxERkctgGAaHzx+2q4CKjovmQvqFIs9ztjjTNrCt3Up4bQPb4urs6qCei4hUUP7+4OFhToZeUh4e5nmVSKmHUqmpqbi7u5f2ZUWkCmtasymv93+dl65/iaV7lhIRHcFvR34DIDUzlQXbF7Bg+wLa125PeEg4w9sP16SoIiIiRThx4US+lfCyf/BTlOa1mttVQHWs0xFPV08H9FhEpJJp0AD27YPT9s/ege+EEl/NIDDFwg8To+zP8fc3z6tESj2UWr58Oc2bNy/ty4qI4O7izn3t7uO+dvex+9Ru5kTPYcH2BZxPPQ/AjpM7ePh/DzPlxykMazeMsJAwQoJCyrfTIiIi5ex86nmij0ez+fhmNsVuYvPxzbZVb4tS37d+vpXwqntUL/sOi4hUFQ0a5AuZdta2EOttEHzBAp07l1PHHKfEodTRo0cLPZaRkUFcXByLFy8mIiKCWbNmlUrnREQK0zqgNe8MfIdX+r3Ckl1LiIiOYEPMBgAuZlxk7pa5zN0yl9CgUMJDwhnadihebl7l3GsREZGylZyRzLYT2+yqoPaf2V/sef6e/vlWwqvjXccBPRYRkaqsxKFUo0aNSjRXy7hx4wgPD7+iTomIlJSnqycjO45kZMeRbD+xncjoSD7Z8YltDoyo41GMOT6GySsnc3/7+wkLCaNd7Xbl3GsREZErl5GVwc74nXbzQO2M30mWkVXked5u3oTUDbGbB6pR9ZJ91hcRESlNJQ6lnnvuuQK/UVksFjw9PfH396d37940bdoUwzD0TU1EHK5DnQ7M/sdsXrvxNT7f+TkRURFsPbEVgMS0RGZtnsWszbPoWb8n4SHh3N36bqq5VivnXouIiBTPaljZf2Z/vpXwUjOLniDXzdmNjnU62lVBtajVAmcnZwf1XEREpHAlDqWmT59ebJu4uDhefPFF5s2bx5EjR66kXyIil83H3YdxIeMY23ksUcejiIyO5LM/PiMlMwWAdcfWse7YOiaumMjIDiMJCwmjhX+Lcu61iIiIyTAMjiYczbcSXmJaYpHnOVmcaBPQxq4Cql3tdrg5uzmo5yIiIpemVCY6X7FiBREREXz33XdkZmbSrFmz0risiMgVsVgs5ofy4C68edObfLrjUyKjI9kZvxOAsylnmblhJjM3zKRvo76Eh4RzR6s79OFdREQcKv5ifL6V8OIvxhd7XtOaTe0qoDrV6aT5E0VEpEK57FDq1KlTzJs3j7lz53L48GH8/Px46KGHGDlyJD169CjNPoqIXLHqHtV5tOujPNLlEdYdW0dkdCRf7vqStKw0AH49/Cu/Hv6VQK9AHuz4IONCxtG4RuNy7rWIiFQ2iWmJtpXwsiuhjiQUP8Ig2CfYbiW80KBQalSr4YAei4iIlJ1LDqV++eUXIiMj+frrr8nMzKRPnz4cPnyYb7/9ll69epVFH0VESo3FYqFXg170atCLmQNmsmD7AiKjI20rE8VfjOe1ta/x2trXuKnJTYSHhHNri1txcSqVwlIREalCUjNTiT4ZzcHDB4mKi2Lz8c3sO70PA6PI82pWq0loUChdgrrQNbgroUGhBPkEOajXIiIijlPif2XNnDmTOXPmsG/fPq655hr+9a9/MWrUKGrUqIGvr68mNheRCqeWZy0m95jMpO6T+PXwr0RER7BszzIyrBkArDy4kpUHVxLkE8SYTmN4qONDuONezr0WEZGrUaY1k13xu+zmgfoj/g8yrZlFnufl6kXnup3t5oFqXKOxPluLiEiVUOJQ6vHHHyckJIRVq1Zxww032PZfvHixTDomIuIoFouF66+5nuuvuZ6TF04yf9t8IqMjOXz+MADHk47z4m8v8vLvL3Njgxt5tMej3NLsFq1cJCJSRVkNKwfOHrCbB2pr3FbbghqFcXVypUOdDnbzQLXyb6XvJyIiUmWVOJS6//77+eqrr7jtttvo168fI0eO5NZbby3LvomIOFxt79pM7T2VJ3s9yY8HfyQiOoJv931LlpGF1bCy8shKVh5ZSQO/BozrPI6HOj1EXZ+65d1tEREpI4ZhEJMYY1cBFXU8ioS0hCLPs2ChdUBr2tZoy7VNrqVrcFfa126Pu4sqbkVERLKVOJRasGABs2bNYvHixcyfP5+77rqLWrVqcdddd2GxWFRiLCKVipPFiQFNBzCg6QBiE2OZt3Uec7fMJSYxBoCjCUd59pdnmb56Ore1uI3w0HBuuOYGnCxO5dxzERG5EqeTTxN1PMquCurEhRPFnte4RmO7CqjOdTvj6eJJfHw8gYGBODnp+4OIiEhelzRzr7e3N2PGjGHMmDHs3buX+fPn8+mnn2IYBiNGjGDYsGEMHTqUNm3alFV/RUQcLtg3mOf6PMfUXlNZHLWYxQcX88OBHzAwyLRm8tWer/hqz1c0qdGEsJAwRnUcRYBXQHl3W0REipGUlsSWuC12K+H9df6vYs+r412HrsFd7VbCq+VZK187q9VaFt0WERGpNCyGYRS9/EcxrFYr//vf//joo4/47rvvyMzMpG3btmzfvr20+lhhJCYm4ufnR0JCAr6+vuXdnctSb4ozsd5Wgi84EfNGVnl3p1xZrVb9dFPs5L4njiYe5cMtH/Lhlg85efGkXTs3ZzfuanUX4aHhXNvgWlWSVmJ6Zkpe+t5x9UrLTGP7ye12FVB7Tu0pdiW86h7VbSvhZVdBBfsEl+jZrvtB8tI9IXnpnpC8Ksvny5LmI1e8xrmTkxODBg1i0KBBnD59moULF/Lxxx9f6WVFRK5qjao34uUbXub5Ps/z333/JTI6klWHVgGQnpXO5zs/5/Odn9PKvxVhIWE80OEBalSrUc69FhGpGrKsWew+tdtuHqgdJ3fYVlctTDWXavlWwmtas6l+uCAiIlJGrjiUys3f35/JkyczefLk0rysiMhVy9XZlbtb383dre/mzzN/Mid6DvO3zedMyhkA9pzew8QVE5n601SGtBlCeGg43YK76R84IiKlxDAMDp47aFcBtSVuC8kZyUWe5+LkQvva7e0qoFoHtMbFqVQ/HouIiEgR9F1XRKSUNKvVjDdueoOXbniJpXuWEhkdyW9HfgMgNTOVBdsXsGD7AjrU7kBYSBjD2w/H171iDvUVESkvx5OOszl2M5tiN9lWwjuXeq7IcyxYaOnf0lb91CWoCx3qdMDDxcNBvRYREZGCKJQSESllHi4eDGs3jGHthrH71G4ioyJZuGMh51PPA7D95HYe/t/DTPlxCsPaDSM8NJzOdTuXb6dFRK5CZ1PO5lsJ73jS8WLPa1S9Ub6V8PRDABERkauPQikRkTLUOqA1/77538y4cQZf7vqSiKgINsZuBOBixkXmbpnL3C1z6RLUhbCQMIa2HYqXm1c591pExPEupl/MtxLewXMHiz0v0CvQLoDqEtRFK6CKiIhUEAqlREQcwNPVk1EdRzGq4yi2ndhGZFQkn/7xKRfSLwDY/hE2eeVk7m9/P2EhYbSr3a6cey0iUjbSs9LZcXKHXQXU7lO7sRrWIs/zdffNtxJefd/6mqdPRESkglIoJSLiYB3rdOSDQR/wev/X+Xzn50RERbD1xFYAEtMSmbV5FrM2z6JX/V6EhYRxT5t7NO+JiFRYWdYs9p7ea7cS3vaT20nPSi/yPA8XDzrV6WRXAdWsVjOcLFoyXUREpLJQKCUiUk583H0YFzKOsZ3Hsvn4ZiKjIvl85+ekZKYAsPbYWtYeW8vEFRMZ1WEU40LG0cK/RTn3WkSkcIZh8Nf5v/KthJddFVoYZ4sz7Wq3s6uAahPQBldnVwf1XERERMqDQikRkXJmsVjoGtyVrsFdeWvAW3y641MioiLYdWoXYE70+/aGt3l7w9tc3+h6wkPDub3l7bg5u5Vzz0Wkqjtx4YRdALU5djNnUs4Ue16LWi3sVsLrWKcj1VyrOaDHIiIicjVRKCUichWp7lGdR7s+yiNdHmHtsbVERkeyZNcS0rLSAPjl8C/8cvgXAr0CeajjQ4wNGUvjGo3LudciUhWcTz2fbyW8mMSYYs+r71ufLsFd6BrUlS7BXQipG4Kfh58DeiwiIiJXO4VSIiJXIYvFQu8GvendoDczB8xkwbYFREZH8ufZPwGIvxjPq2tf5bW1r3FTk5sIDw1nUPNBuDjpsS4iVy45I5mtcVvtKqCynz9F8ff0z7cSXm3v2g7osYiIiFRE+tdLVRUXZ25F2bIl/766dc1NRBzG39Ofx3s+zuQek/nl8C9ERkeydM9SMq2ZGBisOLiCFQdXEOQTxJhOYxjTeQz1/eqXd7dFpILIyMrgj/g/7CqgdsXvIsvIKvI8HzcfQoJC7EKohn4NtRKeiIiIlJhCqaoqMhJeeCH//sl//2q1QkhI/uPPPw/Tp5dlz0SkEBaLhRuuuYEbrrmBExdOMH/rfOZsmcPh84cBOJ50nBd/e5GXf3+ZfzT7B+Gh4QxoMgBnJ+fy7biIXDWshpV9p/fZrYS37cQ22xDhwrg7u9OxTke7CqgW/i20Ep6IiIhcEYVSVVVYGAwenH//jzdDajwEBkL09/mPq0pK5KpQx7sOT1/7NE/1foqVB1cSERXBt/u/xWpYsRpWvt3/Ld/u/5aGfg0Z23ksD3V6iLo++v9XpCoxDIOjCUftAqjouGgS0xKLPM/Z4kybwDZ2FVBtA9tqcQUREREpdQqlqqrChuH96gqpgKsrdO7s8G6JyKVxsjgxsOlABjYdSExiDPO2zGPulrnEJsUCcCThCM/+8izTV0/ntha3ER4azg3X3KDqBpFKKP5ifL6V8E4lnyr2vGY1m9mthNepbic8XT0d0GMRERGp6hRKicj/t3fv8TnX/x/HH9e1o9mcN3M+hxyynBJyDiWUikxRzl9SmA7OUZJTTtVGsgrpG4WEdCAUWQ7lHL4bNmNjzMaO1/X5/eG3z9e1g0NfrrE977fbdduu9+f0+lx77XLt5X2QPKJsobJMaDmBMY+MYd3RdQT/EcyGYxswMEi3p7Py0EpWHlpJ1WJVGVh/IH3q9aGEV4ncDltE/oH45Hh2Re9yKEKdjD95w+PK+JRxKEA1KN2AogWKOiFiERERkaxUlBIRyWNcra50rt6ZztU7E3ExgoW7FrJozyLOXj4LwLG4Y4z6YRRjfh7D0/c/zcD6A2levrkmJxa5SyWlJbH3zF6HHlBHzh+54XHFChTLshKehvGKiIjI3URFKRGRPKxikYq80+YdJrScwJojawj+I5ifwn8CINWWyrJ9y1i2bxk1S9RkUINBPF/3efWaEMlF6fZ09sfsd+gBtT9mP+n29OseV9CtYJaV8CoVqaRis4iIiNzVVJQSEckH3F3cefr+p3n6/qc5ev4oC3YtYPHexZxPOg/AoXOHeGXDK7z+4+v0qN2DgfUH0rhMY/1BK3IH2Q07x+KOORSgdkfvJjk9+brHubu480DJBxx6QNUoUUMrbYqIiMg9R0UpEZF8plrxakx/dDqTW0/m60NfE/xHMFtPbgUgOT2Z0L2hhO4N5YGSDzCowSAC6wTi4+GTy1GL3NsMwyDyUqTDSnh/nP6D+JT46x5ntVi53/d+hx5Qdfzq4OHq4aTIRURERO4cFaVERPIpT1dPetbpSc86PTkQc4AFuxbw6Z+fmn8k/3n2TwZ/N5igjUEE1glkUINBBJQKyOWoRe4N566cuzoR+TXzQGXM63Y9lYtWdihAPVjqQbzdvZ0QsYiIiIjzqSglIiLU8qvFnI5zeLftu/z7wL8J/iOY36N+B+By2mUW7F7Agt0LaFi6IYMaDKJ7re4UdC+Yy1GL3B0SUhLMlfB2Ru3k98jfOZVw6obHlfIulWUlvOJexZ0QsYiIiMjdQUUpERExebl50adeH/rU68Oe6D2E7Aph6b6lJKYmAlzt8bEmjOHfD+eFui8wsMFAavvVzuWoRZwnOT2ZP8/86dAD6vC5wxgY1z2uqGdRGpRu4DAPVJlCZZwUtYiIiMjdSUUpERHJVkCpAII7BTO93XSW7VtG8K5g9p7ZC8CllEvMD5vP/LD5NC3XlEENBvH0/U/j6eqZu0GL3Ebp9nQOxR4yi087T+9k39l9pNnTrntcAdcC1C9V/7+9oMo0pErRKlo4QERERCQTFaVEROS6fDx8GNhgIAPqDyDsdBjBfwSzfP9yktKTAPj11K/8eupXXtnwCn0e6MPABgO5r/h9uRy1yK0xDIPjF45nWQnvStqV6x7nZnWjbsm6ZvGpvn99ihvFKe1fGqvV6qToRURERO5NKkqJiMhNsVgsNCrTiEZlGjGr/Sw+//NzgncFczD2IABxSXHM2jGLWTtm0bpSawbWH0jXGl1xd3HP5chFsoq6FJVlJbwLyReue4wFCzV9azpMRF63ZF2HHoJ2u52YmJg7Hb6IiIhInqCilIiI3LIinkV4ufHLDG00lF9P/UrwH8F8dfArUm2pAPwc/jM/h/+MX0E/Xqr3EgPqD6BS0Uq5HLXkV3FJcQ49oMKiwohOjL7hcRWLVHQoQNUvVR8fDx8nRCwiIiKSP6goJSIi/5jFYqFZ+WY0K9+M2R1m8+neTwnZFcLRuKMAxFyOYeqvU3nv1/doX7U9A+sPpNN9nXC16p8fuTMSUxPZHb3boQj1nwv/ueFxJQuWzLISnm9BXydELCIiIpJ/6a8CERG5LUp4lWDkwyMZ3mQ4myM2E/xHMN8c/oZ0ezoGBhuObWDDsQ2U8SlD34C+9HuwH+UKl8vtsOUelmpL5a+zf5mTkIdFhXHo3CHshv26xxX2KJxlJbyyhcpqInIRERERJ1NRSkREbiurxUrrSq1pXak1ZxLPsHjPYhbsXkDExQgAohKimLRlEm9vfZtO93ViYP2BtK/SHherS+4GLnc1m93G4XOHHeaB+vPsn+aQ0ZwUcC1AQKkAh2F4VYtVxWrRJOQiIiIiuU1FKRERuWP8vf15s/mbvNb0NTYe30jIrhC+/ftb7IYdu2FnzZE1rDmyhgqFKzCg/gBeCngJf2//3A5bcplhGIRfDM+yEl5iauJ1j3O1ulLHr45DD6hafrU0XFRERETkLqVPaSIicse5WF3oWK0jHat1JPJSJB/v/piPd39MVEIUACfiTzDm5zFM2DyBrjW6Mqj+IFpVaqXeLPlEdEJ0lpXwziedv+Fx1YtXd5gHqp5/PQq4FXBCxCIiIiJyO6goJSIiTlW2UFkmtpzI2EfG8t3f3xGyK4QNxzZgYJBuT2fFwRWsOLiCqsWqMrD+QPrU60MJrxK5HbbcJheSLvDH6T8cVsLLKE5eT/nC5bOshFfYs7ATIhYRERGRO0VFKRERyRWuVle61OhClxpdCL8QzsLdC1m0ZxExl2MAOBZ3jFE/jGLMz2N4+v6nGVR/EM3KN9Nk1PeQK2lX2BO9h7DTYeyM2knY6TCOxR274XG+Xr4OPaAalmmIX0E/J0QsIiIiIs6kopSIiOS6SkUrMaXNFCa2nMjqw6sJ3hXMz+E/A1dXWFu2bxnL9i3jft/7GVh/IM/XfZ6iBYrmctRyrTRbGvti9jnMA3Ug5gA2w3bd43zcfbKshFe+cHkVH0VERETyARWlRETkruHu4s4ztZ7hmVrP8Pf5v1mwawGL9y4mLikOgIOxB3llwyu88eMbdK/dnUH1B9GoTCMVMJzMbtg5cu6IwzxQe8/sJcWWct3jPFw8sqyEd1/x+zR3mIiIiEg+paKUiIjcle4rfh8zHp3B263fZuXBlQTvCmbbyW0AJKUnEbo3lNC9odTzr8fA+gMJrBOIj4dPLked9xiGwYn4Ew49oHad3kVCasJ1j3OxuFDbr7ZDD6jafrVxc3FzUuQiIiIicrdTUUpERO5qnq6eBNYNJLBuIAdiDhCyK4TP/vyM+JR4APae2cvg7wYz6odR9Kzdk0ENBhFQKiCXo753nU08m2UlvNgrsTc8rlqxag7zQAWUCsDLzcsJEYuIiIjIvUpFKRERuWfU8qvF3I5zmdp2Kl/u/5LgXcHsjNoJQGJqIgt2L2DB7gU0KtOIQfUH0b12dxVGriM+OT7LSninLp264XFlC5V1GILXoHQDingWufMBi4iIiEieoqJUPhWdEE10YnSW9lRbqvl1d/TuLNtLeZeilE+pOx6fiMj1eLl58WLAi7wY8CJ7ovcQsiuEpfuWkpiaCMDOqJ3sjNrJ8O+H88IDLzCw/kBq+dXK5ahzV1JaEnvP7DULUDujdvL3+b9veFzxAsWzrITn7+3vhIhFREREJK9TUSqfCtkVwlu/vJXj9tgrsdRfUD9L+4QWE5jYcuIdjExE5NYElAoguFMw09tNZ9m+ZXz0x0f8efZPAOJT4pm3cx7zds6jWflmDKo/iG73d8PT1TOXo76z0mxpHIg94DAP1P6Y/aTb0697nLe7N/VL1XeYB6pikYqaSF5ERERE7ggVpfKpgfUH0rl651s+rpS3ekmJyN3Jx8OHgQ0GMqD+AHZG7SRkVwjL9y8nKT0JgG0nt7Ht5DZe2fAKfer1YUD9AdxX/L5cjvp/ZzfsHD1/1GEeqD1n9pCcnnzd49xd3KnnX8+hB1T14tVxsbo4KXIRERERye9UlMqnSvloGJ6I5E0Wi4XGZRvTuGxjZj46k8//+pyQXSEcjD0IwPmk88zcPpOZ22fSulJrBtUfRJcaXXB3cc/lyG/MMAxOXTqVZSW8jEnfc2K1WLnf934alm5IozKNaFi6IXVK1rkn7llERERE8i4VpUREJM8qWqAowxoP4+VGL7Pt5DaCdwWz4uAKc/68n8N/5ufwn/Er6EffgL70f7A/lYpWyuWo/yv2cqxDD6iw02HEXI654XFVilZxmAfqwVIPUtC9oBMiFhERERG5eSpKiYhInmexWGheoTnNKzRnToc5hO4NJWRXCMfijgEQczmGd7e9y9RtU2lftT2D6g/i8fsex9V6/X8mk10Mh6//i4SUBHZF7yIsKoydp3cSFhXGifgTNzyutE/pLCvhFStQ7H+OR0RERETkTlNRSkRE8pUSXiUIejiIEU1GsCl8E8G7gll1eBXp9nQMDDYc28CGYxso41OGfg/2o9+D/ShbqGyW8xiGwSX3q8WoS+4GhmHc9ITgyenJ/HnmT7P3U1hUGIfPHcbg+sWtop5Fs6yEV9qn9K2/CCIiIiIidwEVpUREJF+yWqy0qdyGNpXbcCbxDJ/s+YQFuxaYvZOiEqJ465e3mLxlMp3u68Sg+oN4tMqj5kTgG49vJO3/5wRPc7n6vH3V9lmuk25P52DsQYcheH+d/euGK+F5uXllWQmvctHKWglPRERERPIMFaVERCTf8/f2Z3Tz0bze9HU2Ht9I8K5g1v69Frthx27YWXNkDWuOrKFikYr0f7A/L9Z7kXGbxoEBWAADxm0aR7vK7Th+4XiWlfCupF257vXdrG7ULVnXnIS8YZmG1CxRUyvhiYiIiORR0QnRRCdGZ2lPtRrm193Ru7NsL+WdtxYtsxiG8b9PhCEAXLp0icKFCxMfH0+hQoVyOxz5H9ntdmJiYvDz88NqteZ2OHIXUE7kL6fiT7FozyIW7l7I6YTTDtusFit2w57lmIJuBbmcdvm657VgoaZvTYcheA+UfAAPV4/bGr/kDr1PyLWUD5KZckIyU07kXxM3T+StX9665eMmtJjAxJYTb39At9nN1kfUU0pERCQb5QqXY2LLiYx9ZCzf/f0dwbuC+f7Y9xgY2RakgGwLUpWKVMqyEp6Ph8+dDl9ERERE7mID6w+kc/XOWdqNDh2wxMZi+Ppi2bAhy/ZS3nmnlxSoKCUiInJdrlZXutToQpcaXQi/EM6bP73Jlwe+zHH/xmUa83i1x82V8Ep4lXBitCIiIiJyLyjlk/0wPOO8O5ZoMKzuWEo9mAuROZeKUiIiIjepYpGK/OfCf3CxuGAzbFm2u1hcsBt2xj4yVhOSi4iIiIjcgAatioiI3KSNxzcSdjos24IUgM2wEXY6jI3HNzo5MhERERGRe4+KUiIiIjfBMAzGbRqHi+X6K+K5WFwYt2kcWkdEREREROT6VJQSERG5CTfqJZVBvaVERERERG7OXVGUSkpK4qWXXsLHx4eSJUsyZcqUHPf9+uuvqVatGp6enrRt25Zjx46Z2wzD4K233sLPz49ChQrRt29fLl/+70pICQkJ9O3blxIlSuDv78/IkSNJTU01t8+bNw+LxeLwCAoKujM3LSIi94yMXlLWm/xn04pVvaVERERERG7grihKjRo1ir/++oudO3eydOlSpk+fzhdffJFlv4MHD9K9e3eef/559u/fT8OGDWndujUJCQkALFy4kBkzZvDBBx+wc+dOoqKi6NWrl3n8a6+9RlhYGD/88APr169nw4YNDgWwgwcPMmjQIGJjY83HpEmT7vwLICIid7VUWyon409ix35T+9uxc+rSKVJtqTfeWUREREQkn8r11feSkpJYtGgR69evp2bNmtSsWZPhw4fz0Ucf8dxzzznsGxwcTJMmTRg/fjwAU6ZMYdWqVSxfvpz+/fszf/58hg8fzjPPPAPAJ598QtmyZTl69CjVqlVjy5YtDB48mICAAAAGDRrEJ598wsSJEwE4dOgQzz77LCVKaPluERH5Lw9XD8L6hxF7JdahvcPsBsR6GfhesbDh1T8ctvkV9MPD1cOZYYqIiIiI3FNyvSi1d+9e0tLSePjhh822pk2b8u6772IYhsOS2sePH+ehhx4yn1ssFurWrcv27dvp379/lu2lS5fG19eX7du3U61aNQICAvj666954YUXAFi1ahWFCxc29z948CDVqlW7k7crIiL3qHKFy1GucDmHNne7BTBwt1t4sNSDuROYiIiIiMg9KteH70VGRlKiRAnc3d3NttKlS5OcnMz58+cd9vXz8yM6Otqh7eTJk5w5cybb7YmJicTFxZnb582bR1xcHMWKFaNYsWIcOXKE9957D4Dz588TGxvL119/zX333Uft2rWZM2eO5gMREREREREREbkDcr2nVHJyMh4ejsMbMp4nJSU5tPfo0YPOnTvTq1cv2rVrx9KlS/njjz9o0aKFuf3dd9+lRYsWlCtXjlGjRgGYk5kPGzaM9PR0Nm3aRHp6OhMmTODs2bMAHD58GIAiRYrw1VdfcfDgQYYMGYJhGLz66qvZxp6SkkJKSor5/NKlSwDY7Xbs9pubd0TuXna7HcMw9LMUk3JCrkd5IaD3CXGkfJDMlBOSmXJCMrNc8/29nBc3G3uuF6U8PT1JTk52aMso9BQoUMChvX379kyaNIknn3yStLQ02rRpQ2BgIPHx8QCMGzeOEydOUKNGDdzd3Rk8eDC1atXCx8eHqKgoli5dyvbt22ncuDEA7u7udOzYkdOnT9O0aVPi4+MpVKgQAA888ADR0dF8+OGHORal3n33Xd56660s7bGxsVnuSe49drud+Ph4DMPAas31ToVyF1BOyPXExMTkdghyF9D7hFxL+SCZKSckM+WEZOZrs+EC2G02Yu/hz5cZC9LdSK4XpcqUKcP58+dJS0vDzc0NgKioKDw9PSlevHiW/V9//XVGjBhBQkICxYoVo1u3blSuXBkALy8vli1bxoIFC7BYLHh5eeHn50flypWJiorCMAzq1q1rnisgIICEhAQOHTpEw4YNzYJUhho1anD69OkcY3/zzTcZMWKE+fzSpUuUK1cOX1/fLOeSe4/dbsdiseDr66t/IARQTsj1+fn55XYIchfQ+4RcS/kgmSknJDPlhGRmcXEBwOrick9/vvT09Lyp/XK9KFWvXj1cXV357bffzGF4W7dupVGjRg6TnAMsX76cXbt2MX36dIoVK8aVK1fYuHEjX3zxBQBvvPEGderUITAw0DxPfHw8zZo1w2azAXDgwAEaNGgA/HfInr+/P59++ilvvfUWx48fN6+7Z88eatasmWPsHh4eWYYeAlitVr2h5BEWi0U/T3GgnJCcKCckg94n5FrKB8lMOSGZKSfkWtfOan0v58TNxp7rd+jl5UXv3r0ZOXIkBw4cYNOmTcydO5chQ4YAcO7cOXM4X7Vq1Zg/fz5r1qwhPDyc/v37U7lyZR577DEASpYsyfjx49mzZw979+5l6NChDBgwgKJFi1KiRAkCAwMZMGAAu3btYvfu3QwYMIAOHTpQrlw52rRpw/nz5wkKCiIiIoKVK1cybdo03nzzzVx7bURERERERERE8qpcL0oBzJw5k/vvv5/GjRvz3HPPMXr0aJ599lkAfH19zZ5Q9evX54MPPmDYsGHUqlWLixcvsnbtWrMCN2zYMDp16kS7du1o3bo1zZs3Z+bMmeZ1QkJCaNKkCZ06daJjx47UrVuXZcuWAVC2bFnWrVvHjh07uP/++wkKCmLatGk89dRTTn41RERERERERETyPothGMaNd5ObcenSJQoXLuwwYbrcu+x2OzExMfj5+d3T3Sbl9lFOSGZlR7kQ5W2nTKKVyOm23A5H7gJ6n5BrKR8kM+WEZKackMyMsmWxREVhlCmDJTIyt8P5x262PqKsFxERERERERERp1NRSkREREREREREnE5FKRERERERERERcToVpURERERERERExOlUlBIREREREREREadTUUpERERERERERJxORSkREREREREREXE6FaVERERERERERMTpVJQSERERERERERGnU1FKREREREREREScTkUpERERERERERFxOhWlRERERERERETE6VSUEhERERERERERp1NRSkREREREREREnE5FKRERERERERERcToVpURERERERERExOlUlBIREREREREREadTUUpERERERERERJxORSkREREREREREXE6FaVERERERERERMTpVJQSERERERERERGnU1FKREREREREREScTkUpERERERERERFxOhWlRERERERERETE6VSUEhERERERERERp1NRSkREREREREREnE5FKRERERERERERcToVpURERERERERExOlUlBIREREREREREadTUUpERERERERERJxORSkREREREREREXE6FaVERERERERERMTpVJQSERERERERERGnU1FKREREREREREScTkUpERERERERERFxOhWlRERERERERETE6VSUEhERERERERERp1NRSkREREREREREnE5FKRERERERERERcToVpURERERERERExOlUlBIREREREREREadTUUpERERERERERJxORSkREREREREREXE6FaVERERERERERMTpVJQSERERERERERGnU1FKREREREREREScTkUpERERERERERFxOhWlRERERERERETE6VSUEhERERERERERp1NRSkREREREREREnE5FKRERERERERERcToVpURERERERERExOlUlBIREREREREREadTUUpERERERERERJxORSkREREREREREXE6FaVERERERERERMTpVJQSERERERERERGnU1FKREREREREREScTkUpERERERERERFxOhWlRERERERERETE6VSUEhERERERERERp1NRSkREREREREREnE5FKRERERERERERcToVpURERERERERExOlUlBIREREREREREadTUUpERERERERERJxORSkREREREREREXE6FaVERERERERERMTpVJQSERERERERERGnU1FKREREREREREScTkUpERERERERERFxOhWlRERERERERETE6VSUEhERERERERERp3PN7QBERETuetHRVx/Xs3t31rZSpa4+REREREQkCxWlnMxms5GWlpbbYchNsNvtpKWlkZycjNWqToX3Kjc3N1xcXHI7DLnXhYTAW29lbR/x/1/tdqhfP+v2CRNg4sQ7GZmIiIiIyD1LRSknMQyDM2fOcPHixdwORW6SYRjY7XYSEhKwWCy5HY78D4oUKYK/v79+jvLPDRwInTtnbf+hIyTHgJ8f7Fqfdbt6SYmIiIiI5EhFKSfJKEj5+fnh5eWlP47vAYZhkJ6ejqurq35e9yjDMLhy5QoxMTEAlFKBQP6pnIbhbXaDZMDNDR580OlhiYiIiIjcy1SUcgKbzWYWpIoXL57b4chNUlEqbyhQoAAAMTEx+Pn5aSifiIiIiIjIXUIT5ThBxhxSXl5euRyJSP6U8bun+dxERERERETuHipKOZF624jkDv3uiYiIiIiI3H3uiqJUUlISL730Ej4+PpQsWZIpU6bkuO/XX39NtWrV8PT0pG3bthw7dszcZhgGb731Fn5+fhQqVIi+ffty+fJlc3tCQgJ9+/alRIkS+Pv7M3LkSFJTU83t586d48knn8TLy4sKFSrwySef3JkbzkOSk5NJT0/P7TDuevv27XPINREREREREZH87q4oSo0aNYq//vqLnTt3snTpUqZPn84XX3yRZb+DBw/SvXt3nn/+efbv30/Dhg1p3bo1CQkJACxcuJAZM2bwwQcfsHPnTqKioujVq5d5/GuvvUZYWBg//PAD69evZ8OGDQ4FsD59+pCSksL+/fuZPn06gwcP5rfffrvzL8A9YOzYsbzxxhtZ2nv16kVoaOhNnWPJkiXm8Kk//viDAwcOZLtfv379ePvtt83nDRo04McffwRg1apVtGzZ8taC/3+bN2+mXr16/+jY67lw4QKjR49m6tSp2W5PSUmhcePGREdH3/Q5n3rqKTZs2ODQNmPGDPr163fd4/bu3XvT1xARERERERHJTblelEpKSmLRokXMmDGDmjVr0rZtW4YPH85HH32UZd/g4GCaNGnC+PHjqVq1KlOmTKFgwYIsX74cgPnz5zN8+HCeeeYZatSowSeffMLq1as5evQoAFu2bGHAgAEEBAQQEBDAoEGDWL16NQDh4eF89913fPjhh1SuXJlnn32WwMBAgoODnfdi3IU+++wz6tWrx8cff8zixYupV6+ew+PHH39k0qRJ5vcZvv32W/7973+bz3///XdefvllrNarKTd9+nS+++676177q6++IigoiMjISBYsWEBQUBChoaEcP36coKAgVq1aZe7bqVMnLBZLloe3tzfz58+nT58+N33Pzz33HD/99BNwtSBmtVqznHfJkiUsWLCAnj17UrZsWTZt2kTr1q3Nc4SFhbFixQpWrFjBrFmz8PDwcGi79rF+/X+XkY+LiyM+Pp64uDiSk5NvOma4WnRr1qwZJ0+eJD09PceHYRgAPPLII+zfv/+WrnGrvQmDg4MpV64cXl5edOvWjbNnz5rbUlNTGTp0KEWKFKF48eK89tprDr3uIiMjeeyxxyhQoACVK1fms88+y/YaR48exdPT85buQ0RERERERHJfrhel9u7dS1paGg8//LDZ1rRpU8LCwsw/njMcP36chx56yHxusVioW7cu27dvz3Z76dKl8fX1NbcHBATw9ddfc+nSJS5dusSqVasoXLgwADt27KBs2bJUrFjRIY7ff//9tt/zveTxxx9nyZIldOnShSeeeIIlS5Y4PBo1akS/fv3M7zN4e3szaNAg1q5dC8DMmTPp1KkTaWlpREREsGbNGrp27Xrd4X9paWkkJydjt9tJTU0lOTmZtLQ07Ha7+X2GNWvWsG3bNqpXr05aWhrfffcdTZs2JT4+/pbud9OmTURFRdGmTRvgao8uu92OYRgOj/T0dEaPHo2/vz8bN25k+/btDve/ZcsWQkNDCQ0N5b333qNSpUrm88yPFStWmMctWbKEAQMGOMTUsmVLLBYLo0aNYtGiRWZh7MyZM+Y+Z86coU+fPkyePJk1a9bg5uaW42POnDkAjBkzhn/961+39PrcSm/CjRs3MnToUMaMGcOff/6Jj48Pjz/+ODabDYCJEyeahblNmzbx008/MWrUKPP47t27Ex8fT1hYGB9++CHDhw93KETC1cJV586dSUlJuaX7EBERERERkdznmtsBREZGUqJECdzd3c220qVLk5yczPnz5ylRooTZ7ufnl2UI1MmTJylatGi22xMTE4mLizP/eJ83bx6tWrWiWLFiAPj7+7Ny5UozjjJlyjicu3Tp0kRFRd3Gu733FC9enOLFi+Pr68u0adPMXmkZkpOTefbZZ6ldu7ZDe6tWrfjwww8ZN24chQoVYsWKFRiGwZIlS8x9qlevDsDAgQMJDg4mNDSUF1980dzu4uLC1q1bKVSoEFWrVqVYsWLExsby4IMP0qlTJxo3bmzua7VacXFxAcDV1dXskZXRdrMmT57Myy+/7NB2/vx5h+cFCxYEoHbt2syaNSvb84wcOZKRI0dy7tw5ypcvzzfffEOFChVueP09e/awadMmkpKSCAoK4ujRo/z0008YhsGsWbM4cuQIISEh5n0CnDhxgjZt2tCsWTOGDh2Km5sbQ4cONc9ZtWpV5s+fT4cOHRyu9eijj5pFpWuLwjnJ6E0YHh5OxYoVqVy5Mhs2bCA4ODjb4+fPn0/Pnj0ZNGgQAB999BFlypTh559/pl27dsyfP5/Zs2fTtm1bAD744ANatWrFpEmTOHr0KL/99hvHjx+ncuXK1K5dm1GjRjF9+nS6du0KXB3KOXDgQEqXLn3D2EVEREREROTuk+tFqeTkZDw8PBzaMp4nJSU5tPfo0YPOnTvTq1cv2rVrx9KlS/njjz9o0aKFuf3dd9+lRYsWlCtXzux1kTHB9LBhw0hPT2fTpk2kp6czYcIEczhRTnFkjuFaKSkpDj00Ll26BIDdbsdut5vtmXva3IsMw2DYsGFMnjzZoT0wMDDH++revTsNGjSgbdu2uLi4sHHjRlq2bMnatWt577332Lp1q8P5X3jhBQIDA3nyySdp2LAhY8aMoVOnTnz//fdZzr1v3z6++eYb4OrPzsvLy9x27UprFouFxo0bU6NGDTPGnH4GR44cYefOnaxdu9ZhX39/f7N3D8C0adMoUaIEhmE49NaCq0Wwa68/e/ZskpKSHHrgXWvnzp00aNAAuJpP69evZ/z48YSEhNC5c2c6duxoFtgyzptRaMuI8cKFCzz22GNMnz6dHj160LhxY4ceRxn7Znffzz77LCEhITRp0gSAF198kYiICDZt2pRl3+3bt1O2bFkqVKhgnuvhhx9m+vTp2Z77+PHj/Otf/zK3eXp6Uq1aNX777TceeOABEhISaNy4sbm9bt26JCcns3v3bs6ePYuvry+VKlVy2D5hwgTS0tJwdXVl3bp1TJw4kRo1atC6devr/m5l3H/m381blfG7/L+cQ/Iu5YWA3ifEkfJBMlNOSGbKCcns2nXD7+W8uNnYc70o5enpmWXunIxCT4ECBRza27dvz6RJk3jyySdJS0ujTZs2BAYGmkO0xo0bx4kTJ6hRowbu7u4MHjyYWrVq4ePjQ1RUFEuXLmX79u1mDxt3d3c6duzI6dOnc4wjcwzXevfdd3nrrbeytMfGxjqcK2PIWcacPte63rxBVqvVoQfZ7dj3n869Y7fbmTVrFrNnz87S3r59+xyH4G3atIlu3brx448/Osy5lBEzwO7du82eVunp6ezYsYO6deuahYTJkyfTpUsX87gvvviC/fv3m9d0dXXlypUrfP/998yZM4d169bxww8/8N577/Hjjz8SEhLC7t27sdls5tC77Kxbt46AgADc3NzMuZcyilG///475cqVY+jQoWZhY8uWLQ6veUZs3bp1A+A///kPs2bNonjx4pw6dSrL9erUqYPNZjPjWbp0KS4uLvTv35+vvvqKgIAAIiMjqVOnjsNxGfM4DR48mDlz5lCrVi1Gjx5N586dgas9z86fP4/FYqFQoUIADte5VpMmTRg8eLC57f33389x35MnT1K6dGmHbSVLliQqKirb/X19fR222e12IiMjiY6OxsfHBzc3NyIjI7nvvvvM1wsgKiqKkiVLEh8fT0JCgvk7GBERQWpqKjExMfj5+fHBBx8A8MsvvwBcdxXI9PR07HY758+fx83NLcf9bsRutxMfH49hGGb+Sv5ms9vMrzExMbkcjdwN9D4h11I+SGbKCclMOSGZ+dpsuAB2m43Ye/jzZcaCdDeS60WpMmXKcP78edLS0sw/FqOiovD09KR48eJZ9n/99dcZMWIECQkJFCtWjG7dulG5cmUAvLy8WLZsGQsWLMBiseDl5YWfnx+VK1cmKioKwzCoW7euea6AgAASEhI4dOgQZcqUyTI0MCoqKsuQvmu9+eabjBgxwnx+6dIlypUrh6+vr1kMgKsFooSEBFxdXc0hVxl69uyZ4/nr16/PhAkTzOcvvvhijnPn1KpVi3fffdd8PmjQILPn1rXWrFmT4/Wux2q1MmrUqCwrzD3zzDNYrdYs9wWwdu1a/vjjD4KDgwkICGDDhg088sgjrF+/npkzZ5oTo3t4eJi9gNavX8/58+eZOnUqAQEBWCwWxo0bx7hx4xzO3aVLF4drpqenM3PmTHr16oWnpydubm5YLBY8PT0ZNmwYFouF9evX5xgrXO19VbNmzWy3+/r64u/vT4ECBbBarVitVlq0aMGmTZt4/vnnadq0qTlMDa4WIl944QXq1avH33//neM1XVxczG1t27bFw8MDd3d3LBYLERERFClShCtXrjgcM378eHr27Ent2rVxdXXl66+/ZsSIEVy8eJFVq1ZRqFAhXn31VTw9Pc2f1xNPPAFgxpyhXr16REdHc+bMGcqWLUuRIkWyjROu9jj09PR0uBcvLy+SkpKyvb8ePXowZswYnnzySerVq8eMGTOIiYkhLS0NT09PunXrxvjx46lbty7e3t6MGTMGV1dXbDYbTZs2pXTp0gQFBTFnzhxOnz7N3Llzgav/cF97vWuHbeYkY0hn8eLF/6dJ0e12OxaLBV9fX31oEABcrC7mVz8/v1yORu4Gep+QaykfJDPlhGSmnJDMLP//943V5d7+fHmzf3flelGqXr16uLq68ttvv5nD8LZu3UqjRo0chkEBLF++nF27djF9+nSKFSvGlStX2LhxI1988QUAb7zxBnXq1CEwMNA8T3x8PM2aNTN7vBw4cMAcLnX48GHg6txSxYoVIzIykoiICHOo1datWx0mTs/Mw8Mjy5A/wCxaXPv82pXbbtat7H+z+97K9eHqqnbXrpI3bdq0LPusXLmSAQMG0Lt3b0JDQ832AwcOcOHCBfOaHh4eFChQwCy6ZNcL7Z133sHDw4PWrVszdOhQqlevbg4DzPDrr79iGIZ53sOHD9O7d29atGjBgAEDsFgsFC9eHKvVSmpqKikpKZQrV45Lly7x/PPP5/gaxMTEULduXXP7tdfIKHzC1ZwFx9f82u8Nw6B///64u7szY8YMHnrooSw9qjJce1yFChWoUKECFy5c4Pz584waNYp+/fplWTnwhx9+oEOHDmZepaWl8fHHH/Pmm29muwIhwHfffcejjz6aJU98fX0BOHv2LOXKlcs2xgwFChQgOTnZ4fjU1FQKFCiQ7Wvav39/Dh8+TNOmTbFYLDzzzDO0a9eOQoUKYbFYmDdvnrl6YcGCBZkwYQK//PILhQoVwtPTk5UrV9KjRw8KFSqEn58fI0aMICgoyDz+2tfw2q/Xe50z/27+E7frPJL3KCckg94n5FrKB8lMOSGZKSfkWtdOSnIv58TNxp7rRSkvLy969+7NyJEj+fTTT4mJiWHu3LnmZM7nzp3Dx8cHDw8PqlWrxosvvkjz5s2pU6cOY8eOpXLlyjz22GPA1aFE48eP5/7778disTB06FAGDBhgToQeGBjIgAEDWLhwIRaLhQEDBtChQwfzj/F27drxr3/9i3nz5vHnn3/y5Zdfsnnz5jt6/1999VWO2zL/EK+dJPxG+y5atOh/C+z/rVmzhiNHjjBp0iRsNhuhoaEMGzaMihUrEhQUxPDhwzl//jwTJkygZs2aDsd+//33PPXUU+bzVq1aOWzPKCLs27eP2rVrExwczN9//023bt2oWbMmc+bM4dSpU5w+fdq8xvz58+nWrZtDAaVatWqEhIRw8eJFcygXwP79+1mxYgVlypThq6++wjAMc6hYTjKPe01NTcVms3Hq1Clz0n03NzfmzZuHt7d3tuf466+/OH36NGvWrOHEiRMUL17cYaW8DDVq1HB4fuXKFV5//XUWL15MSkoKU6dOJSkpKdvhZm3btqVKlSocO3aMHj16AFd77uUkpx5itzIR/K32JnRxcWH27NlMnTqVlJQUChcuTP369enYsSMAJUqUYOPGjcTHx+Ph4UFqaipBQUFmAfDBBx/k77//5ty5cxQtWpR169ZRrFgxc8VMERERERERubflelEKYObMmfzrX/+icePGeHt7M3r0aJ599lngak+OxYsX06dPH+rXr88HH3zAsGHDiImJoVWrVqxdu9YsyAwbNoyIiAjatWuH3W6nZ8+ezJw507xOSEgIr732Gp06dcJut/P44487bP/ss8/o27cvtWvXxs/PjwULFly3p9TtcCtDie7UvjnZuXMnffv25cqVK3Tv3p25c+fi5+dHSkoKVqvVHBo2ceJE3njjDU6cOMG8efNo0aIFx44d49dff+XTTz81z/f999+bw/dmzJjBTz/9BPx3Yntvb29CQ0P54YcfgKvzOD3//PMOMV37/KuvvuLpp5/GxcUFd3d3xo4d67BvYmIi77//vsNr0bt3b/r375/t/fr7+2dZae/MmTN4enpSunRph8Lf8ePHKVWqVLbneeCBB8x7OHHixE3PY+Tl5UWTJk0YOHCg2Uusa9euJCQkUKFCBYYMGYKLiwv16tVj9uzZtGzZ8obnvJG4uDiAm+oW2rhx41vqTTh79mxsNhsjR47E09OTkydPsmfPHjMnevfuTe/evc25xr766iv8/PyoVasWFy5coGvXrqxevdosBq5atSpLYVNERERERETuXXdFUcrb25vPPvuMzz77LMu2zCtqvfTSS7z00kvZnsfFxYU5c+YwZ86cbLcXLFiQDz74wJwgObOSJUuydu3aW4w+72rQoIG5MpvFYmHKlCnA1fmqKlasyBtvvGHuGxQUxJkzZ8zhYFOnTuWJJ56gfPnyN7xOSkoKnp6e9OrVC8As6PTs2ZOnn36ao0eP0rZtW4YMGcKCBQvYvHkzZcuWdRgSd//997Nt2zaH8/r7+7N8+XKqVq16U/fbsGFDFi9e7ND2888/8/DDDzsUpAzDYOPGjYwePfqmzlu8eHHOnTuXpT27uLKbY2zkyJH069eP7du3m0NVb5eDBw/i6+tr9jxLTEzEZrNl2xupSpUq1+1NaLfbiYuLo3Dhwri5uVGhQgX69etHw4YNKVmyJEOGDOGxxx4zJ7UvUqQIb775Jp999hmxsbGMGTOGUaNGYbVaKVq0KBcvXuS1115j7NixbNmyhWXLlt3xnosiIiIiIiJOER199ZFZaup/v+7enXV7qVJXH3nEXVGUkruT1Wpl2bJlNG3aNNvtmYeLZcwp9Z///IclS5awZ88eh+3t27d3eH7tnFIJCQlZhsNFRETw+eefM2/ePN577z369u1LamoqTZo0YcyYMQQGBuLr68sbb7zBe++9l22M1apVy9JWoUIFIiIisrQ/+uijvPrqqyQmJuLt7U1CQgJTp07l7bffdtjv008/5fz58w5DE29FfHw8np6eOU5an1np0qVZvXo1hw8fJi0tjaSkpBznTzIMg+TkZGw2GzabjeTkZAzDIC0tzVyR0WKxmL3TfvvtN9q3b2+eb+jQoURERORY/Lleb8KTJ09SqVIlNm3aRMuWLXnyySc5dOgQ3bt3JzExkc6dOzN//nzzXO+88w6DBw+mUaNGFChQgEGDBjFy5Ehz+7///W8GDBhAjRo1KFeuHJ9//rm5cqaIiIiIiMg9LSQE3norS3PGX3qW2FioXz/rcRMmwMSJdzQ0Z1JRSq5r/vz5DoWEm1G5cmWOHDlChQoVHNozihU365133iE1NZWtW7ea81W9/fbbtG3bljlz5rB9+3a+/PJL3n77bSbewi9lTgWdypUr07x5c7755huef/554uLiaNKkCd27dzf3MQyD0NBQgoOD8fHxuelrXuv1118nJCSE++67z2EC9etxc3Nj0aJFzJ07lxo1api9jTL7888/zeFwADNmzACgc+fOZtu1RbkvvviChQsXmtuunag+O9frTVixYsUsPRtHjx6dY48yb29vPv/88xyvVb16dYc5wnLSsmXLLNcVERERERG5qw0cCNf8nZYhYwRKsWLFsp8sPA/1kgKwGPpr7ra5dOkShQsXJj4+nkKFCpntycnJhIeHU6lSpdsy15PcOT/99BNvvfUWW7ZswTAM0tPTcXV1dShk2e32e3oVhAw//vgjb7/9dr4YEne7fgftdjsxMTH4+fnliRyQ/13ZWWWJSoiijE8ZIkdE5nY4chfQ+4RcS/kgmSknJDPlhGSWV3Iip/pIZvfuHYrcAW3atKFMmTLmJOzZuZffGK41efLkHOdXExEREREREbnTNHxPJJOMycTzeifCmxkaJyIiIiIiInKn5I0uHyIiIiIiIiIick9RUUpERERERERERJxORSkREREREREREXE6FaVERERERERERMTpNNH53Sw6+urjVpUqdfVxB9jtdofV52w2Gy4uLnfkWiIiIiIiIiKSd6mn1N0sJATq17/1R0jIbQthxYoVpKWlmc8nTpzIqFGjALh8+TI1atQgLi4ux+MrVqzI4cOHb+maTz31FBs2bHBomzFjBv369bvucXv37r2l6/z9998cPXoUgCNHjpCSkgJAaGgoXbt2NffbsmXLTZ1v5syZvPnmm7cUg4iIiIiIiEh+paLU3ezxx8Hd/daOcXe/etxtYLfbWb9+PW3btuXKlSsAjB07lj///JN///vfTJo0iSeeeIJixYo5HJeWlkZqaqr5/N1338VisWR5VK1a1eG4uLg44uPjiYuLIzk5+ZZi3bx5M82aNePkyZOkp6fn+DAMwzxm586dNGrUiC+++IKxY8fSrl07EhISHM77888/89xzz3H58mUALl686HAPzZs3B+DQoUNMmjSJ4ODgWy7CiYiIiIiIiORHKkrdzVxc4Jrizk1JTb163G1gtVpZtGgRRYsWZd68ebRs2RIPDw9++OEHunfvzrRp03j//fexWCxs3rzZPO69995jypQp5vNRo0aRlpbm8Dh27FiW6y1ZsoQBAwY4tLVs2RKLxcKoUaNYtGiRWQw6c+aMuc+ZM2fo06cPkydPZs2aNbi5ueX4mDNnjnlcr169WLNmDS+//DIhISH4+/szfPhwc3tYWBg9e/bko48+omDBgg5xJSUlERQUxMMPP8zp06fp3LkzkydPNntZRUZG/uPXXURERERERCQ/0JxSckOff/45BQsWZOTIkURERADw9ddf8/vvv/Pee+8RGBh43eMtFguuro6plt08VHv27GHTpk1mwefo0aP89NNPGIbBrFmzOHLkCCH/PzQx43wnTpygTZs2NGvWjKFDh+Lm5sbQoUPNc1atWpX58+fToUMHh2ulp6dz4sQJmjdvzr59+yhWrBgfffQRmzZtIjExEYCUlBRmzZpF586ds8R6+fJlFi9eTGhoKI888givvPIKL7/8shlbq1atmDZtGk8++eR1XxsRERERERGR/EpFKclReHg4q1evBuCll14iPDycJk2a4OfnR2JiIsnJyezatQsAT0/PHM9jsVhueK2UlBTWr1/PhAkTCA4OpkuXLjz22GNm8cpqtWZb3Lp48SKPP/4406dPp0ePHjz00EPmnFfXs2HDBrMXVGBgIKtWrcpSQMq492+//ZYVK1Y4bJs8eTK1a9dmwIAB1K5dm2HDhjFs2DBze9OmTZkwYQLx8fH06dPnhvGIiIiIiIiI5DcqSkmOEhMTOXz4MEuXLqVr16788ssv9OvXj7lz5xIaGsrmzZsJDQ294XkMw+DcuXMObRcuXHB4vnz5clxcXBg8eDBfffUVDRo0IDo6mtq1azvst2jRIgCGDBnC/PnzeeCBBxg/frzZm2nIkCHEx8djsVgoVKhQjjF16tSJVatW8cQTT1ChQgUAmjdvzsaNGx3i/vTTT/nxxx+zHN+rVy+WLFnChg0bCAgIIC0tjRkzZnDw4EE++eQTXFxcMAwDNze3G74+IiIiIiIiIvmRilK5qUEDuGZupCxudT6pDB06XH+CdH9/+OOPG56mTp06BAcHmyvh/fLLL6xZs4YFCxZgs9mw2+0sX77c3P/UqVP4+vo6nCMtLY0jR45kKS4BVKlSxfw+Y7ib1Xp1mrPjx49TpEgRkpKSHI4ZP348PXv2NM/39ddfM3z4cC5evMjq1avx8vLi1VdfxdPTk6lTpwLQsWNHAFq0aOEw91Xr1q2ZMmXKPyocNWjQgGHDhjF79mzq1asHwPbt2zlz5gzz588HoGzZsvTo0eOWzy0iIiIiIiKSH2ii89x05gxEReX8iI39Z+eNjb3+ea9XCLuOlStXkpaWxpgxY/D39+f5558nOTmZmJgYrFYrJUqUcNg/OTmZM2fO0L59+xtOdF6+fHkCAwO5cOEC58+fJygoiB07duDq6urw2LhxI+fPnzeHBKampvLxxx9TrVq1HOP+7rvvSEtL46effsqy7eWXX6Z06dIAbN26lQIFCpgPLy8vBg8enO054+LiqFmzJidOnCAiIoIjR45w4cIFs3fZsWPHiI6OvqXXV0RERERERCQ/UU+p3OTvf/3tqan/rDDl63vjnlL/gGEYzJw5k4ULFzJs2DB2797Nhx9+yJUrV6hdu3aWuaN++eUX6tatm2XlOsg60fmVK1d4/fXXWbx4MSkpKUydOpWkpKRsezG1bduWKlWqcOzYMbMn0ptvvplj3FarNctcVBnCwsIYOXIkQUFBWXpSGYbBJ598wvr167Mc9/TTT7Nt2zZCQ0O5cuUKmzZtonPnzuzfvx8/Pz+OHTtmTnwuIiIiIiIiIlmpKJWbbjSEbvduqF//1s+7YQM8+OA/iykbNpuN8PBwunXrhs1mY/PmzWzZsgUPDw+OHTtGeHg477zzTpbjpk2bdtOTfHt5edGkSRMGDhzI0KFDqV69Ol27diUhIYEKFSowZMgQXFxcqFevHrNnz6Zly5a35d4+//xzc2W+X375JdtJ2bt165albdWqVbi5uXHgwAHatGnDunXr2Lp1K3B1iGGnTp0ICgpi9uzZtyVOERERERERkbxGw/fkul577TXKli3L0KFDadeuHb/99huVK1emYsWKNGzYkFmzZvHNN9/Qrl07Uv9/DqyxY8dSsGBBwsPD6devH+np6VkeNpsty7WunSsqw8iRI9mwYQO9evW67fcWGRnJ0qVL6du3LwCPPPKIwxDD1NRUFixYkO2xhQoVYvfu3bRq1Yq33nqLpk2bmts8PT1ZtmwZn3/+Odu2bbvtcYuIiIiIiIjkBeopJTlat24d33//PVu2bOH8+fOMHTuWihUrkpSUZA6/GzNmDIZhcOXKFQICAti5cycfffQRkydP5qeffqJNmzb8/vvv2Z7/2onOc1K6dGlWr17N4cOHSUtLIykpKdveTHB1uF1ycjI2mw2bzUZycjKGYZCWlkZycjIAFosFDw8P7HY7AwYMYODAgZQqVcrcljHMz2azYbVaSU9PNydfz6x69ep8+OGHPPPMM1y6dImLFy+aww39/PwICQmhaNGiN7xHERERERERkfxIRSnJ0WOPPUbLli3x8vKicOHCLFu27IbHvP/++7zzzjt8++23NGzYkB07dmS7X0REBG3btr2pONzc3Fi0aBFz586lRo0a2a7kB/Dnn3/SunVr8/mMGTMA6Ny5s9lWoUIFIiIi2Lt3L+np6UycODHbc23fvp1HHnmEYsWKMWXKlGz3KVGiBM8//zyRkZFUrFiRIkWKEBoaam5/+umnb+r+RERERERERPIji2EYRm4HkVdcunSJwoULEx8fT6FChcz25ORkwsPDqVSpEp6enjd/wrAwaNbs6oTnN8vdHbZtg4YNbyHy2+fy5ctcunTJ7H10LzMMg/T0dFxdXR16ZyUnJ9/az1Fy3T/+HczEbrcTExODn59fjj3oJH8pO6ssUQlRlPEpQ+SIyNwOR+4Cep+QaykfJDPlhGSmnJDM8kpO5FQfyezevcP84Lvvbq0gBVf3/+67OxPPTShYsGCeKEhdjwpSIiIiIiIiIv87Dd+7mw0cCNcMPbtpebwoJCIiIiIiIiL3PhWl7malSqnAJCIiIiIiIiJ5kobviYiIiIiIiIiI06koJSIiIiIiIiIiTqeilIiIiIiIiIiIOJ3mlLqLRSdEE50YfcvHlfIuRSmf3J+Lymaz4eLiktthiIiIiIiIiMhdSEWpu1jIrhDe+uWtWz5uQosJTGw58bbH8/fff3PfffcBkJKSwogRI3j22Wdp0aJFln0vXLjAww8/zIoVK6hVq5bDto8//pgNGzawYsWKHK/11FNPMWDAADp06GC2zZgxg8OHD/Pxxx/neNzevXupV6/eLd7Zrbt48SIlSpQgPT39jl9LREREREREJC9SUeouNrD+QDpX75ylvfWnrYlPiaewR2F+7v1zlu2lvG9/L6mwsDCefvppwsPDsVqteHh40LJlS5555hkWLlxIly5dHPYvWrQoTz/9NN26deOvv/7C3d39pq4TFxeHi4sLcXFxJCcn31KMmzdvplOnThw8eJDSpUvnuJ+LiwsWiwWAl19+mbp169K/f38A5s6dS8OGDfn999/Zu3cvCxcuvOF1X331VebMmZNjTNkV7URERERERETyOxWl7mKlfLIOwzMMgytpVwC4knaFAP8As8ByJ82bN4+hQ4ditVqpWLEiJ06cMLd17drV/H7y5MmMHTsWgIkTJ7Jx40aOHj2apbdUTpYsWcKvv/7q0NayZUt++eUX8/miRYsAiI6Oxt/fH4AzZ87Qp08fJk+ezJo1a3j55ZdzvMb777/Pq6++CkDfvn1p3bo1derU4aGHHmL58uWUK1cOu92OYRikp6djsVhwdc35V2XGjBnUqVOHBQsWsHHjRjw8PJg4cSJbt27l4Ycfvqn7FhEREREREclvVJS6x2w8vpE0exoAafY0Nh7fSPuq7e/oNSMjI1m/fj3z58/n008/ZfPmzZQtW5YhQ4YQFxfHF198wblz5yhRooTDHFIuLi5s27YNNze3m77Wnj172LRpE0lJSQQFBXH06FF++uknDMNg1qxZHDlyhJCQEACzUHTixAnatGlDs2bNGDp0KG5ubgwdOtQ8Z9WqVZk/f77DUMAM9erVY9y4cfz2228UL16c33//naeeesrc/tlnn1GrVi32799vtl1bJLNYLLRp04Yff/yRPXv28Oijj1KmTBmOHz/Ohg0bbuneRURERERERPITFaXuIYZhMG7TOIe2cZvG8WiVR+9ob6l33nmHQYMGERkZyfDhwzlw4ABvv/02O3bsYNOmTZw4cYLGjRsTHBzM008/TVJSEh988IF5fFBQEGlpadhsNgDS09Ox2+3m8DyLxYKHhwcpKSmsX7+eCRMmEBwcTJcuXXjsscfMQpfVas2219LFixd5/PHHmT59Oj169OChhx5i1KhRN7yv6Oho4uPj6dixI+XLl6d3794MHDiQDz/8kNmzZ5vD9zJf76effuLixYuULFmS5ORkrFYrly9fpkKFCoSEhLBz507GjRtH0aJF/6fXXUTuHjktPJFqSzW/7o7enWX73bLwhIiIiIjI3UhFqXvIxuMbCTsd5tAWdjrsjvaWunjxIh9//DF169Zl9uzZvPPOOwwcOJAtW7bw8ssvM2XKFLOX1HPPPYe/vz8PPPAAf/zxB+np6axcuZKgoCD69+/Pp59+6nDuAgUKAFCmTBkiIyNZvnw5Li4uDB48mK+++ooGDRoQHR1N7dq1HY7LGL43ZMgQ5s+fzwMPPMD48ePp3Lmz2R4fH4/FYqFQoUI53tubb75pxrRp0yYSEhJYsWIFH330kblPxvYKFSoQEREBXO0BllEoc3V1ZejQoSxevBh3d3dmzpxJeno6Y8aMYcGCBXTr1o0RI0ZQpUqVf/ojEJG7wI0Wnoi9Ekv9BfWztN+phSdERERERPICFaVyUYMFDTiTeOam9jUMg9grsdlue+KLJ/D18r3p3lL+3v78MeCPm9q3SJEiLFq0iG+//ZZy5crx8ssv4+HhQfny5Tl9+jQPPPAAJUqUoGPHjpw9e5Zdu3bRrFkzli9fTmJiIitXrgQgNDSU0NBQIOfV91q1asW0adOwWq0AHD9+nCJFipCUlOSw3/jx4+nZs6dZrPr6668ZPnw4Fy9eZPXq1Xh5efHqq6/i6enJ1KlTAejYsSMALVq0YPPmzQC88cYb9OnTh379+gGwbt067HY7r7zyCt988w0A3bt3d4gpsx9++IEmTZrQsGFDnnzySZYsWQLA0aNHCQ4O5scff6RgwYI39VqLyN0rp4Un7HY7cXFxFCtWLNv3iTux8ISIiIiISF6holQuOpN4hqiEqP/5PGn2NE4nnr4NEeUsLCyMQYMG8eyzz9KgQQOCgoIICAhg0aJFPPTQQ7z99tt07979f7pG+fLlCQwM5MKFC5w/f56goCD69etH7969HfbbuHEj7du3N4twqampfPzxx7z55ps5nvu7777j0UcdhznWqFGDGjVq4O3tDVwdHmi1Wjl8+DCjR48mOjqapKQkXF1dHY5LSUlh5cqV2Gw2Xn/9dXr16sWkSZN45ZVXSElJwTAMxo8fD8CoUaPMydhF5N6V3cITcLUoFeMSg5+fX47FaxERERERyZ6KUrnI3/vmihUZvaQyJjjPjpvV7aZ7S93sdeFqAWbEiBGUKlWK2NhY+vTpQ/PmzTl58iQ+Pj43fZ6bceXKFV5//XUWL15MSkoKU6dOJSkpKdvJwtu2bUuVKlU4duwYPXr0ALhuUcpqtV53Bb0Me/fuZfPmzbz//vt8+eWXZvsrr7zCyJEj8ff3p0GDBnh7e+Pi4sKuXbtISUnhpZdeAq6uxJeens4bb7wBgLu7+y29BiIiIiIiIiL5hYpSuehmh9B9f+x7OizNunLctdLsaXzS5ZPbPreUh4cHEREReHt7ExcXx6pVqxg9ejTNmjWjatWqWfYPCgpi6tSpN1UAyszLy4smTZowcOBAhg4dSvXq1enatSsJCQlUqFCBIUOG4OLiQr169Zg9ezYtW7b8n+8vPT2dy5cvA1d7YPXu3ZvAwEDq1q3LypUrWbt2LXFxcXz77bdMnjwZd3d3fv/9d1JTUylRogRRUVGUK1cuy3nfe+898/uEhASzN5aIiIiIiIiIXKWxBne5jBX3XCwu193PxeLCuE3jMAzjtl7fbrfzxRdf8Oijj1KuXDlWrVpF7dq1GT9+PH379gWurp6XkpLC7t27Wb9+fZaC1JUrV5g2bRqXLl264fWunSsqw8iRI9mwYQO9evW6fTcGXLhwgccff5zLly/zxhtv0LdvX7p168bHH38MwLBhwxg2bBgPPPAAn332mdljy8vLyzxH2bJlMQzDfEyePJkJEyY4tKkgJSIiIiIiIpKVekrd5bJbcS87NsN2R1biy5hjqVu3bixfvpzPPvuMsWPH8tJLLxEYGAhAs2bNeOKJJ/Dw8OCDDz4wjz137hwA1apVo02bNnh6ev6jGEqXLs3q1as5fPgwaWlpJCUl5ThM0TAMkpOTsdls2Gw2kpOTMQyDtLQ0kpOTgatFNDc3N9q0aUNAQABr165l9erVfPTRR3zyyScO93Ctr7/+mieffJKEhARiY7OfdF5EREREREREbo6KUnexjF5SVqzYsd9wfytWxm0ax6NVHr3plfhuxsyZM83va9WqxfLly2nbtq3ZNn36dHO42rUT/ZYuXZphw4bRv3//LL2fbpWbmxuLFi1i7ty51KhRI8fz/fnnn7Ru3dp8PmPGDAA6d/7vqlkVKlQgIiKCdevWmZOQP/300zz99NMYhoHNZgOuvv7p6enmROcZPcA2bNjAs88+S8OGDf+nexIRERERERHJzyzG7R7vlY9dunSJwoULEx8fT6FChcz25ORkwsPDqVSp0i31Foq4EEH9hfWJS4q76WOKFyjOH/3/oGLRircSumQjc1FK7l3/9HcwM7vdTkyMVlqT/1JOSGbKCbmW8kEyU05IZsoJySyv5ERO9ZHM7t07zAdC/wy9pYIUwPmk84T+GXpnAhIRERERERERuU00fO8uNrD+QDpX73zjHTMp5V3qDkQjIiIiIiIiInL7qCh1FyvlU4pSPiowiYiIiIiIiEjeo+F7IiIiIiIiIiLidCpKOZHdfuMV9ETk9tPvnoiIiIiIyN1Hw/ecwN3dHavVyunTp/H19cXd3V2rud0DtPrevc8wDFJTU4mNjcVqteLu7p7bIYmIiIiIiMj/U1HKCaxWK5UqVSI6OprTp0/ndjhykwzDwG63Y7VaVZS6x3l5eVG+fPl7eklVERERERGRvEZFKSdxd3enfPnypKenY7PZcjscuQl2u53z589TvHhxFTPuYS4uLurtJiIiIiIichdSUcqJLBYLbm5uuLm55XYochPsdjtubm54enqqKCUiIiIiIiJym+kvbRERERERERERcToVpURERERERERExOlUlBIREREREREREafTnFK3kWEYAFy6dCmXI5HbwW63k5CQoDmlxKSckMyUE5KZckKupXyQzJQTkplyQjLLKzmRURfJqJPkREWp2yghIQGAcuXK5XIkIiIiIiIiIiK5KyEhgcKFC+e43WLcqGwlN81ut3P69Gl8fHy0/HwecOnSJcqVK8epU6coVKhQbocjdwHlhGSmnJDMlBNyLeWDZKackMyUE5JZXskJwzBISEigdOnS1+3xpZ5St5HVaqVs2bK5HYbcZoUKFbqn3wzk9lNOSGbKCclMOSHXUj5IZsoJyUw5IZnlhZy4Xg+pDPfuAEUREREREREREblnqSglIiIiIiIiIiJOp6KUSA48PDyYMGECHh4euR2K3CWUE5KZckIyU07ItZQPkplyQjJTTkhm+S0nNNG5iIiIiIiIiIg4nXpKiYiIiIiIiIiI06koJSIiIiIiIiIiTqeilIiIiIiIiIiIOJ2KUiKAYRhMmDABf39/ihYtyoABA0hOTgbgP//5D61atcLT05Pq1auzdu3aXI5WnOHMmTN069aNIkWKUL58ed59911zm3Iif0lNTeX+++9n8+bNZtuNcmDjxo3UrFmTAgUK8Mgjj/D33387OWq5k7LLiU2bNtGwYUO8vb2pW7dulpxYsmQJFStWxMvLiyeeeIKzZ886OWq5U7LLhwxpaWnUqVOHiRMnOrTrPSJvyy4nLly4QM+ePfHx8aFs2bLMmzfP4RjlRN6WXU78/vvvNGnSBG9vb2rXrs13333ncIxyIm8KDw/niSeeoHDhwlSqVIl3330Xu90OwO7du2nQoAGenp4EBATw+++/OxybVz9LqCglAkyfPp3g4GCWL1/O5s2b+eWXX5g4cSJ2u52uXbtSqVIljhw5wssvv8wzzzxDeHh4bocsd9iLL77IhQsX2LFjB0uXLmXevHmEhoYqJ/KZlJQUAgMDOXTokNl2oxw4efIkTz75JK+88gqHDh2iUqVKdO3a1fzAIfe27HLi2LFjdOrUicDAQPbv38/gwYN56qmn2LdvH3D1D4/+/fsza9Ys/vrrL1JTU3nhhRdy6xbkNsouH641bdo09u/f79Cm94i8LaecCAwM5MSJE+zcuZMFCxbw2muvsX79ekA5kddllxOXL1+mU6dONGnShAMHDjBq1CieeeYZjhw5Aign8qrU1FSeeOIJihQpwu7duwkODub9998nJCSExMREHnvsMTp06MCRI0fo0KEDnTp1IiEhAcjjnyUMkXzOZrMZvr6+xpIlS8y2JUuWGG3atDF+/vlnw8vLy0hMTDS3tWrVyhg3blxuhCpO5OXlZXz77bfm8xEjRhhdu3ZVTuQjBw4cMOrVq2fUq1fPAIxNmzYZhmHcMAfGjx9vtGjRwtx2+fJlo2DBgsZPP/3kzPDlDsgpJyZNmmQ88sgjDvu2a9fOGDNmjGEYhvHCCy8YvXv3NrdFREQYFovFOH78uLNClzsgp3zI8PfffxulS5c2atWqZUyYMMFs13tE3pVTTvz111+Gi4uLERERYe47ZMgQ/buRD+SUEzt37jQAIyEhwdz3wQcfNGbPnm0YhnIir9qyZYvh4eFhXLlyxWx75513jKZNmxqffPKJUbFiRcNutxuGYRh2u92oXLmysWjRIsMw8vZnCfWUknzvwIEDnDt3js6dO5ttgYGB/Pjjj+zYsYMHH3yQggULmtuaNm2apSul5D0BAQF88cUXpKSkcPbsWb7//nsKFy6snMhHtmzZQqtWrdi2bZtD+41yYMeOHTRv3tzc5uXllW0XbLn35JQT3bt3Z+bMmQ5tFouFy5cvA1lzokKFCpQpU0Y5cY/LKR8yDBw4kLfeeosSJUo4tOs9Iu/KKSc2b95MQEAAFSpUMNvmz5/PpEmTAOVEXpZTTlSpUgUfHx9CQ0MxDIOwsDAOHz5M4cKFAeVEXlWjRg2+/fZbChQoYLZlfF7YsWMHzZo1w2KxmO0PP/xwjp8v89JnCRWlJN87fvw4vr6+bN26lYCAACpVqsTIkSNJTU0lMjKSMmXKOOxfunRpoqKicilacZYlS5awbds2vL29KVWqFDabjbFjxyon8pFBgwYxa9Ysh+ITcMMcUI7kXTnlxH333UeDBg3M54cOHWLTpk20bt0aUE7kVTnlA8DixYtJTU2lb9++WbYpH/KunHLi+PHjVKpUiWnTplG5cmVq167Nxx9/bG5XTuRdOeVEsWLFWL58OaNGjcLT05NGjRrRpk0bevToASgn8ipfX1/atWtnPk9JSeGTTz6hdevW+frzpWtuByCS2xITE0lMTGT06NHMnj0bgH79+mGz2UhOTsbDw8Nhfw8PD5KSknIhUnGmF154gUqVKrFixQqio6OZOnUqsbGxygm5YQ4oR/K3mJgYunbtSv369enUqROgnMhvYmNjefPNN/nxxx/N//G+lvIh/0lMTOTHH38kNTWVlStXcuDAAQYMGECJEiXo2rWrciIfio6O5sUXX6Rfv37069ePX375hdWrV3Pp0iU8PT2VE/mAzWYjMDCQ8+fPExQURK9evfLt50sVpSTfc3V15cqVK8yePZuWLVsCVyc+79mzJ3369CExMdFh/5SUFIcul5L3bN++nV9//ZXIyEhKlSoFQEJCAoGBgXTs2JFz58457K+cyF88PT2vmwMZHyYzby9UqJDTYpTcERcXR7t27UhOTmbFihVmQSKnnND7Rt706quv0qdPH2rXrp3tdr1H5D+urq6kpqayZMkSvL29CQgIICwsjI8++oiuXbsqJ/Kh0NBQihcvzty5c7FYLDzwwAPs27eP119/ncWLFysn8ji73c5LL73Et99+y7p16yhVqtQNPyvk5c8SGr4n+Z6/vz8A1atXN9tq1KhBcnIy/v7+REdHO+wfFRWVpeuk5C2nTp2iRIkSZkEKrs4xFR4ejp+fn3IinytTpsx1c+BG2yVviomJoUWLFsTHx/Pzzz87/LyVE/nLsmXL+OCDDyhSpAhFihRh27ZtTJ06lbp16wLKh/zI39+fMmXK4O3tbbbVqFGDyMhIQDmRH506dYo6deo49KYMCAhg586dgHIiL7PZbPTq1Yt///vfrFy5kjZt2gD5+/OlilKS79WrVw83Nzd2795tth06dAgfHx+aNWvG7t27zclqAbZu3cpDDz2UG6GKk1SpUoVz585x9uxZs+3w4cO4u7vTpEkT5UQ+99BDD103Bx566CG2bt1qbrt8+TJ79uxRjuRhycnJPPbYYyQlJbFt2zaqVKnisD1zToSHhxMVFaWcyKPCw8PZt28fe/fuZe/evTRo0IBBgwaxbt06QO8R+VGjRo04ceIEcXFxZtuhQ4eoXLkyoJzIj6pUqcLBgwcd2g4fPmz+Z7lyIu965ZVXWLVqFWvXrjWH+cPVn/m2bdswDAMAwzD49ddfc/x8mac+S+Ty6n8id4UBAwYYVatWNX777TcjLCzMuO+++4zXXnvNsNlsRs2aNY0XX3zRiIiIMD788EOjQIECDkv6St7UrFkzo127dsaBAweMLVu2GJUqVTIGDRqknMinuGYZ5xvlwH/+8x/D09PT+OCDD4yIiAjjxRdfNGrXrm3YbLZcvAO53a7NialTpxoeHh7G9u3bjdjYWPORsdT3tm3bDA8PD2PFihXG8ePHjY4dOxodO3bMxejldrs2HzJr0aKFMWHCBPO53iPyh2tzIjU11ahdu7bRqVMn4/Dhw8bKlSsNLy8vY926dYZhKCfyi2tzIiYmxihSpIgxatQo4/jx48aSJUsMT09PY/ny5YZhKCfyqh07dhiAMX/+fIfPC3FxcUZ8fLxRokQJY8yYMcaJEyeMMWPGGH5+fvnis4SKUiKGYaSkpBivvvqqUaxYMcPHx8fo37+/kZycbBiGYRw5csRo1qyZ4eHhYVSvXt1Yv359LkcrzhAbG2s899xzRrFixYwyZcoYI0eONK5cuWIYhnIiP8r8B+eNcuDbb7817rvvPsPDw8No0aKFcezYMSdHLHfatTnRoEEDA8jy6N27t7n/okWLjHLlyhmenp5G586djZiYmNwJXO6IWylKGYbeI/KDzDkRGRlpdOnSxShQoIBRqlQpY86cOQ77Kyfyvsw5sXfvXqN58+aGt7e3Ub16dWPhwoUO+ysn8p6goKBsPy9UqFDBMIyrRat69eoZ7u7uxoMPPmiEhYU5HJ9XP0tYDOP/+4eJiIiIiIiIiIg4ieaUEhERERERERERp1NRSkREREREREREnE5FKRERERERERERcToVpURERERERERExOlUlBIREREREREREadTUUpERETkDjp27BhnzpzJ0n7ixAl+/fXXXIjoKi3ALCIiIrlNRSkRERGRO6hPnz4EBwdnaf/mm28YOHDgdY/966+/GDFiBCkpKbd0TcMwWLhwIRcuXGDHjh24uroCEBsby9dffw3A2LFjCQoKuuG5IiMjOXz48A0fIiIiIrfKNbcDEBEREcmLrly5gpeX1z8+PjU1lX79+uHl5UVCQgLh4eHX3d9qtXLfffeZ196wYQNvv/02c+fOBWDv3r0899xz1KtXj6eeeooTJ05QrFixG8bRr18/vv/++xvup55XIiIicqtUlBIRERG5A7p3707Dhg0d2o4fP063bt2Aq72W4uLiqFevHgB169bls88+M/cdOnQohw8f5q+//iI0NJRRo0Zd93oeHh4kJyebzxcuXMjw4cM5e/YsAKdPn6Zp06ZMmTIFu93OyZMnadCgwU3dy8MPP0yrVq2y3RYREcHSpUtv6jwiIiIi11JRSkREROQOOHjwINWqVSMpKYlz585x5MgRfHx8eOihhwDYt28fSUlJ5vPKlSsDYLPZeOWVV/j000/58ssvqVixIkFBQQ5D7fbv30+dOnXYs2ePWdS6Vvfu3fnuu+8AzELX448/DsCiRYvYvXs3u3bt4ty5c6xatco8rnr16oSEhGQ5X4ECBShRokS29xkXF3eLr4yIiIjIVRZDfa1FREREbqtTp05Rvnx5hzYXFxfS09PN57Nnz+bjjz9m//79Dvvt2rWLNm3asHz5ctq3b0///v1p3749zzzzjLnPjYpS4eHhXLhwgZ49e1K0aFHCwsJYsGABNpuNhg0bcuHCBVq3bs3IkSMpUqQIu3btYseOHUyZMoUXX3zR4VwdOnTQ8D0RERG5I9RTSkREROQ2++KLLyhbtiwnTpzgkUceoW3btkycOJETJ06QlJQEQExMDKmpqQ6ThJcqVYr69esTHh5OkSJFGDZsGJ9//jm9e/e+petXqFCBjz76iAsXLjBt2jSeeuoprFYrQ4cOZe3atfz73/8G4LHHHqN169aMHj0awKEgtWTJEnbs2EGFChXo27fvDa85dOhQvL29mTp16i3FKiIiIvmXilIiIiIit1FKSgpz5sxh8ODBWK2OCx13796d33//3aGtZs2a5veLFy+mT58+uLu706NHD7755htWr15N8+bN2b59O5s3b+bNN9+8YQyTJ09m0aJFrFu3zuzB1KdPH6Kioli/fj1LliyhaNGi7Nu3j9atW7Nv3z4aNWrkcA4vLy8KFizI+fPnb+q+XVxcKFy48E3tKyIiIgIaviciIiJy2/3222/Url2bQoUK0axZM4oWLUpaWhoLFizIMqxvypQprFu3jm3btpnHvvTSS5w6dYqiRYsSGRkJwPz585k6dSqRkZHm8L1rHTp0iBo1agBw6dIlEhMT8fb25qGHHqJMmTL88MMPADz11FNERETQpUsXDh48yBdffEGpUqX45JNPzHmnMuzYsYMmTZrc1D2PHDmSGTNm3PqLJSIiIvmWekqJiIiI3GYPP/wwAL/++iunTp3i119/pUqVKkRGRnLlyhWHfWNiYkhMTOTw4cN4e3uza9cuAgICGDZsGFOmTLnudb755huzEJUxUTqA3W7HarWyYsUKoqKi2LBhA2fOnMFut+Pj48P8+fOx2Wy89957fP/998THx9O0adMcr/P666/j6ekJwLZt2zh48CADBgwwty9ZsuTWXiARERERVJQSERERue02bNjA6NGj2bNnD25ubgwaNIjXXnvNoXCUWc2aNXn88cdZu3YtL7/8MqGhoTe8TsWKFc2i1LV69eplrr4HV+eYytC7d28efvhhbDYbvr6+DB48mC5dulCkSJEcr1OyZEkKFCgAgI+PD25ubvj7+5vbPTw8bhiriIiISGYqSomIiIjcZvfffz+tW7dmyZIlDBgwgJIlS1KpUiVOnz7N2LFjGTJkCA8++CAAb7/9NmvXrmXHjh237fpffvkl33zzDYMGDeLAgQMULlyYbt26UbJkST788EPg6hxQ/fv3Z9y4ccydO/e65xsxYkSWtsGDBzs879ix422LX0RERPIHFaVEREREbrPy5ctnO7+Sh4cH3t7eNGnShJCQEPr06XNHrl+wYEG2b99Oeno6jz32GB06dOCXX34hLCwMLy8v4OoQv19++QWAZcuW0blz5yzn2bt3LwMHDszS9p///IennnrKod1isRAbG4uvr+8duScRERHJe6w33kVEREREbodixYoxZ84cPvzwQ7788kvsdvsdu9YHH3zAiRMncHNzY9asWXh7e5OYmAhAamoqvXv3Zvv27Xz44YesWLGC4cOHZzlHSkoK3t7eDg93d3esVmuWdsMwsNlsd+x+REREJO9RTykRERERJ+vbty+NGjXi4MGDHDlyBDc3txz3jY+PJzo6mrNnz5Kens7hw4cJDw8HICIiwpyAHMDX15fixYvzxx9/sG7dOhYuXIibmxvLli3j+++/p0OHDqxcuZLx48dz7NgxvvvuO1q0aIGXlxcvvfQSsbGxfPLJJ/z222+0atXquvcwc+bMbNv69u3Lxx9//A9fGREREclPVJQSERERyQWff/4506dPp3jx4kyfPj3H/b755htefPFF83nNmjXN75988kmHfSdPnszYsWOZNGkSp06d4vXXX6d///54eHjw3HPPMWLECKZOnUrBggXZtWsXlSpVAq5Ofl60aFGWL1+Oq6srjRo14tChQ//ovq43YbqIiIjItSyGYRi5HYSIiIhIfpMxdM9qvf2zKaSlpeXY++p620REREScSUUpERERERERERFxOk10LiIiIiIiIiIiTqeilIiIiIiIiIiIOJ2KUiIiIiIiIiIi4nQqSomIiIiIiIiIiNOpKCUiIiIiIiIiIk6nopSIiIiIiIiIiDidilIiIiIiIiIiIuJ0KkqJiIiIiIiIiIjTqSglIiIiIiIiIiJOp6KUiIiIiIiIiIg43f8B/24EGht2EP8AAAAASUVORK5CYII=", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 特征数量与性能关系分析\n", "def plot_features_vs_performance(evaluation_df, figsize=(12, 6)):\n", "    \"\"\"\n", "    绘制特征数量与性能的关系图\n", "    \n", "    Args:\n", "        evaluation_df: 评估结果DataFrame\n", "        figsize: 图形大小\n", "    \"\"\"\n", "    # 过滤掉基准模型\n", "    df_filtered = evaluation_df[~evaluation_df['method'].str.contains('baseline')].copy()\n", "    \n", "    # 提取方法类型\n", "    df_filtered['method_type'] = df_filtered['method'].str.extract(r'([^_]+)_\\d+')\n", "    \n", "    plt.figure(figsize=figsize)\n", "    \n", "    # 为不同方法类型使用不同颜色和标记\n", "    method_styles = {\n", "        'lgb': {'color': 'blue', 'marker': 'o', 'label': 'LightGBM重要性'},\n", "        'rfe': {'color': 'red', 'marker': 's', 'label': '递归特征消除'},\n", "        'statistical': {'color': 'green', 'marker': '^', 'label': '统计特征选择'}\n", "    }\n", "    \n", "    for method_type, style in method_styles.items():\n", "        method_data = df_filtered[df_filtered['method_type'] == method_type]\n", "        if not method_data.empty:\n", "            plt.errorbar(method_data['n_features'], method_data['cv_mean'], \n", "                        yerr=method_data['cv_std'], \n", "                        **style, markersize=8, linewidth=2, capsize=5)\n", "    \n", "    # 添加基准线\n", "    baseline_score = evaluation_df[evaluation_df['method'].str.contains('baseline')]['cv_mean'].iloc[0]\n", "    plt.axhline(y=baseline_score, color='black', linestyle='--', alpha=0.7, \n", "                label=f'基准模型 (所有特征): {baseline_score:.4f}')\n", "    \n", "    # 设置标题和标签\n", "    plt.title('特征数量与模型性能关系', fontsize=16, fontweight='bold')\n", "    plt.xlabel('特征数量', fontsize=12)\n", "    plt.ylabel('AUC得分', fontsize=12)\n", "    \n", "    # 添加图例\n", "    plt.legend()\n", "    \n", "    # 添加网格\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # 调整布局\n", "    plt.tight_layout()\n", "    \n", "    # 保存图片\n", "    plt.savefig('features_vs_performance.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "\n", "# 绘制特征数量与性能关系图\n", "plot_features_vs_performance(evaluation_df)"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 6. 结果导出和报告生成\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["生成最终报告: lightgbm_feature_selection_results.xlsx\n", "✓ 报告已保存到: lightgbm_feature_selection_results.xlsx\n", "\n", "============================================================\n", "LightGBM特征选择分析总结\n", "============================================================\n", "\n", "📊 数据概况:\n", "  - 原始特征数量: 331\n", "  - 样本数量: 4,402\n", "  - 标签分布: {0: 2201, 1: 2201}\n", "\n", "🏆 最佳性能方法:\n", "  - 方法: lgb_importance_50\n", "  - 特征数量: 50\n", "  - AUC得分: 0.9999 ± 0.0001\n", "  - 相比基准改进: +0.0008\n", "\n", "📈 前5个最重要特征:\n", "  257. 银行卡涉赌涉诈风险类型: 24639.80\n", "  50. 按身份证号查询距最近在非银行机构申请的间隔天数: 3000.41\n", "  115. 按手机号查询距最近在非银行机构申请的间隔天数: 169.34\n", "  278. 近期稳定指数: 139.34\n", "  4. 浙数社保经济能力评分: 8.16\n", "\n", "💾 输出文件:\n", "  - 详细报告: lightgbm_feature_selection_results.xlsx\n", "  - 特征重要性图: lightgbm_feature_importance.png\n", "  - 性能对比图: feature_selection_performance_comparison.png\n", "  - 特征数量关系图: features_vs_performance.png\n", "\n", "✅ LightGBM特征选择分析完成！\n"]}], "source": ["# 生成最终报告和导出结果\n", "def generate_final_report(feature_importance_df, selection_results, evaluation_df, \n", "                         output_file='lightgbm_feature_selection_results.xlsx'):\n", "    \"\"\"\n", "    生成最终的特征选择报告\n", "    \n", "    Args:\n", "        feature_importance_df: 特征重要性DataFrame\n", "        selection_results: 特征选择结果\n", "        evaluation_df: 性能评估结果\n", "        output_file: 输出文件名\n", "    \"\"\"\n", "    print(f\"生成最终报告: {output_file}\")\n", "    \n", "    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:\n", "        # 1. 特征重要性排序\n", "        feature_importance_df.to_excel(writer, sheet_name='特征重要性排序', index=False)\n", "        \n", "        # 2. 性能评估结果\n", "        evaluation_df.sort_values('cv_mean', ascending=False).to_excel(\n", "            writer, sheet_name='性能评估结果', index=False\n", "        )\n", "        \n", "        # 3. 各种方法选择的特征列表\n", "        for n_features, methods in selection_results.items():\n", "            sheet_name = f'选择{n_features}个特征'\n", "            \n", "            # 创建对比表\n", "            max_len = max(len(features) for features in methods.values())\n", "            comparison_data = {}\n", "            \n", "            for method_name, features in methods.items():\n", "                # 补齐长度\n", "                padded_features = features + [''] * (max_len - len(features))\n", "                comparison_data[method_name] = padded_features\n", "            \n", "            comparison_df = pd.DataFrame(comparison_data)\n", "            comparison_df.to_excel(writer, sheet_name=sheet_name, index=False)\n", "        \n", "        # 4. 推荐的最佳特征集\n", "        best_method = evaluation_df.loc[evaluation_df['cv_mean'].idxmax()]\n", "        best_method_name = best_method['method']\n", "        \n", "        # 解析最佳方法的特征数量和类型\n", "        if 'baseline' not in best_method_name:\n", "            method_parts = best_method_name.split('_')\n", "            # method_type = method_parts[0]\n", "            # n_features_best = int(method_parts[1])\n", "            \n", "            if len(method_parts) == 3:\n", "                method_type = '_'.join(method_parts[:2])  # \"lgb_importance\"\n", "                n_features_best = int(method_parts[2])  \n", "            else:\n", "                method_type = method_parts[0]\n", "                n_features_best = int(method_parts[1])\n", "            best_features = selection_results[n_features_best][method_type]\n", "            \n", "            # 获取这些特征的重要性信息\n", "            best_features_info = feature_importance_df[\n", "                feature_importance_df['feature'].isin(best_features)\n", "            ].copy()\n", "            \n", "            best_features_info.to_excel(writer, sheet_name='推荐特征集', index=False)\n", "    \n", "    print(f\"✓ 报告已保存到: {output_file}\")\n", "    \n", "    # 打印总结信息\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"LightGBM特征选择分析总结\")\n", "    print(\"=\"*60)\n", "    \n", "    print(f\"\\n📊 数据概况:\")\n", "    print(f\"  - 原始特征数量: {len(feature_importance_df)}\")\n", "    print(f\"  - 样本数量: {X_balanced.shape[0]:,}\")\n", "    print(f\"  - 标签分布: {dict(zip(*np.unique(y_balanced, return_counts=True)))}\")\n", "    \n", "    print(f\"\\n🏆 最佳性能方法:\")\n", "    best_row = evaluation_df.loc[evaluation_df['cv_mean'].idxmax()]\n", "    print(f\"  - 方法: {best_row['method']}\")\n", "    print(f\"  - 特征数量: {best_row['n_features']}\")\n", "    print(f\"  - AUC得分: {best_row['cv_mean']:.4f} ± {best_row['cv_std']:.4f}\")\n", "    if 'improvement' in best_row:\n", "        print(f\"  - 相比基准改进: {best_row['improvement']:+.4f}\")\n", "    \n", "    print(f\"\\n📈 前5个最重要特征:\")\n", "    for i, row in feature_importance_df.head(5).iterrows():\n", "        print(f\"  {i+1}. {row['feature']}: {row['importance']:.2f}\")\n", "    \n", "    print(f\"\\n💾 输出文件:\")\n", "    print(f\"  - 详细报告: {output_file}\")\n", "    print(f\"  - 特征重要性图: lightgbm_feature_importance.png\")\n", "    print(f\"  - 性能对比图: feature_selection_performance_comparison.png\")\n", "    print(f\"  - 特征数量关系图: features_vs_performance.png\")\n", "    \n", "    print(\"\\n✅ LightGBM特征选择分析完成！\")\n", "\n", "# 生成最终报告\n", "generate_final_report(feature_importance_df, selection_results, evaluation_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 修复版本：如果上面的报告生成出错，使用这个简化版本\n", "def generate_simple_report(feature_importance_df, selection_results, evaluation_df, \n", "                          output_file='lightgbm_feature_selection_results_simple.xlsx'):\n", "    \"\"\"\n", "    生成简化版本的特征选择报告（修复版本）\n", "    \"\"\"\n", "    print(f\"生成简化报告: {output_file}\")\n", "    \n", "    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:\n", "        # 1. 特征重要性排序\n", "        feature_importance_df.to_excel(writer, sheet_name='特征重要性排序', index=False)\n", "        \n", "        # 2. 性能评估结果\n", "        evaluation_df.sort_values('cv_mean', ascending=False).to_excel(\n", "            writer, sheet_name='性能评估结果', index=False\n", "        )\n", "        \n", "        # 3. 各种方法选择的特征列表\n", "        for n_features, methods in selection_results.items():\n", "            sheet_name = f'选择{n_features}个特征'\n", "            \n", "            # 创建对比表\n", "            max_len = max(len(features) for features in methods.values())\n", "            comparison_data = {}\n", "            \n", "            for method_name, features in methods.items():\n", "                # 补齐长度\n", "                padded_features = features + [''] * (max_len - len(features))\n", "                comparison_data[method_name] = padded_features\n", "            \n", "            comparison_df = pd.DataFrame(comparison_data)\n", "            comparison_df.to_excel(writer, sheet_name=sheet_name, index=False)\n", "        \n", "        # 4. 推荐特征集（使用前100个最重要特征）\n", "        recommended_features = feature_importance_df.head(100).copy()\n", "        recommended_features.to_excel(writer, sheet_name='推荐特征集', index=False)\n", "    \n", "    print(f\"✓ 简化报告已保存到: {output_file}\")\n", "    \n", "    # 打印总结信息\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"LightGBM特征选择分析总结（简化版）\")\n", "    print(\"=\"*60)\n", "    \n", "    print(f\"\\n📊 数据概况:\")\n", "    print(f\"  - 原始特征数量: {len(feature_importance_df)}\")\n", "    \n", "    print(f\"\\n🏆 最佳性能方法:\")\n", "    best_row = evaluation_df.loc[evaluation_df['cv_mean'].idxmax()]\n", "    print(f\"  - 方法: {best_row['method']}\")\n", "    print(f\"  - 特征数量: {best_row['n_features']}\")\n", "    print(f\"  - AUC得分: {best_row['cv_mean']:.4f} ± {best_row['cv_std']:.4f}\")\n", "    \n", "    print(f\"\\n📈 前5个最重要特征:\")\n", "    for i, row in feature_importance_df.head(5).iterrows():\n", "        print(f\"  {i+1}. {row['feature']}: {row['importance']:.2f}\")\n", "    \n", "    print(f\"\\n💾 输出文件:\")\n", "    print(f\"  - 简化报告: {output_file}\")\n", "    \n", "    print(\"\\n✅ 简化版LightGBM特征选择分析完成！\")\n", "\n", "# 如果上面的报告生成失败，运行这个\n", "# generate_simple_report(feature_importance_df, selection_results, evaluation_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可选: 使用最佳特征集训练最终模型\n", "def train_final_model_with_best_features(X, y, feature_importance_df, n_best_features=100):\n", "    \"\"\"\n", "    使用最佳特征集训练最终模型\n", "    \n", "    Args:\n", "        X: 特征数据\n", "        y: 标签数据\n", "        feature_importance_df: 特征重要性\n", "        n_best_features: 使用的最佳特征数量\n", "    \n", "    Returns:\n", "        final_model: 最终训练的模型\n", "        selected_features: 选择的特征列表\n", "    \"\"\"\n", "    print(f\"使用前{n_best_features}个最重要特征训练最终模型...\")\n", "    \n", "    # 选择最重要的特征\n", "    best_features = feature_importance_df.head(n_best_features)['feature'].tolist()\n", "    \n", "    if hasattr(X, 'columns'):\n", "        X_final = X[best_features]\n", "    else:\n", "        feature_indices = [i for i, name in enumerate([f'feature_{i}' for i in range(X.shape[1])]) if name in best_features]\n", "        X_final = X[:, feature_indices]\n", "    \n", "    # 分割数据\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        X_final, y, test_size=0.2, random_state=42, stratify=y\n", "    )\n", "    \n", "    # 训练最终模型\n", "    final_model = lgb.LGBMClassifier(\n", "        objective='binary',\n", "        n_estimators=500,\n", "        learning_rate=0.05,\n", "        num_leaves=31,\n", "        feature_fraction=0.9,\n", "        bagging_fraction=0.8,\n", "        bagging_freq=5,\n", "        random_state=42,\n", "        verbose=-1\n", "    )\n", "    \n", "    final_model.fit(X_train, y_train)\n", "    \n", "    # 评估最终模型\n", "    train_pred = final_model.predict_proba(X_train)[:, 1]\n", "    test_pred = final_model.predict_proba(X_test)[:, 1]\n", "    \n", "    train_auc = roc_auc_score(y_train, train_pred)\n", "    test_auc = roc_auc_score(y_test, test_pred)\n", "    \n", "    print(f\"✓ 最终模型训练完成\")\n", "    print(f\"  - 使用特征数: {len(best_features)}\")\n", "    print(f\"  - 训练集AUC: {train_auc:.4f}\")\n", "    print(f\"  - 测试集AUC: {test_auc:.4f}\")\n", "    \n", "    return final_model, best_features\n", "\n", "# 训练最终模型（可选）\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"训练最终优化模型\")\n", "print(\"=\"*50)\n", "\n", "final_model, final_features = train_final_model_with_best_features(\n", "    X_balanced, y_balanced, feature_importance_df, n_best_features=100\n", ")\n", "\n", "print(f\"\\n🎯 最终模型使用的特征:\")\n", "print(f\"前10个特征: {final_features[:10]}\")\n", "print(f\"...等共{len(final_features)}个特征\")"]}], "metadata": {"kernelspec": {"display_name": "risk", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}