
def predict_with_fixed_model(model_path, new_data, debug=True):
    """
    使用修正后的模型进行预测
    
    注意：现在的编码是 1=高风险(坏客户), 0=低风险(好客户)
    """
    import joblib
    import pandas as pd
    import numpy as np
    
    # 加载模型
    model_package = joblib.load(model_path)
    model = model_package['best_model']
    selected_features = model_package['selected_features']
    optimal_threshold = model_package['optimal_threshold']
    
    if debug:
        print(f"模型信息:")
        print(f"- 选择特征数: {len(selected_features)}")
        print(f"- 最优阈值: {optimal_threshold:.3f}")
    
    # 数据预处理（简化版，实际使用时需要完整的预处理流程）
    try:
        # 选择特征
        X_new = new_data[selected_features]
        
        # 预测概率
        if hasattr(model, 'predict_proba'):
            # sklearn模型
            prob_predictions = model.predict_proba(X_new)[:, 1]
        else:
            # LightGBM
            prob_predictions = model.predict(X_new)
        
        # 二分类预测
        binary_predictions = (prob_predictions >= optimal_threshold).astype(int)
        
        # 风险等级
        risk_levels = []
        for prob in prob_predictions:
            if prob >= 0.7:
                risk_levels.append('高风险')
            elif prob >= 0.3:
                risk_levels.append('中风险')  
            else:
                risk_levels.append('低风险')
        
        if debug:
            print(f"\n预测结果:")
            print(f"- 样本数: {len(prob_predictions)}")
            print(f"- 平均风险概率: {prob_predictions.mean():.3f}")
            print(f"- 高风险样本数: {(prob_predictions >= 0.7).sum()}")
            print(f"- 中风险样本数: {((prob_predictions >= 0.3) & (prob_predictions < 0.7)).sum()}")
            print(f"- 低风险样本数: {(prob_predictions < 0.3).sum()}")
        
        return {
            'probabilities': prob_predictions,
            'predictions': binary_predictions,
            'risk_levels': risk_levels,
            'threshold': optimal_threshold
        }
        
    except Exception as e:
        print(f"预测失败: {e}")
        return None

# 使用示例
if __name__ == "__main__":
    # 测试预测
    try:
        # 加载测试数据
        test_data = pd.read_csv('result_data_fixed.csv').head(10)
        
        # 进行预测
        results = predict_with_fixed_model('overdue_prediction_model.pkl', test_data)
        
        if results:
            # 显示结果
            result_df = pd.DataFrame({
                'sample_id': range(1, len(results['probabilities']) + 1),
                'risk_probability': results['probabilities'],
                'predicted_class': results['predictions'],
                'risk_level': results['risk_levels']
            })
            
            print("\n=== 预测结果示例 ===")
            print(result_df)
            
    except Exception as e:
        print(f"测试预测失败: {e}")
