"""
数据标记任务工具函数模块
优化版本 - 提供高效、健壮的数据处理功能
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
from typing import Tuple, Dict, Any, Optional
import re

warnings.filterwarnings('ignore')

class DataLabelingProcessor:
    """数据标记处理器 - 优化版本"""
    
    def __init__(self, verbose: bool = True):
        self.verbose = verbose
        self.stats = {}
        
    def log(self, message: str, level: str = "INFO"):
        """统一的日志输出函数"""
        if self.verbose:
            timestamp = datetime.now().strftime('%H:%M:%S')
            prefix = "✓" if level == "SUCCESS" else "⚠️" if level == "WARNING" else "✗" if level == "ERROR" else "•"
            print(f"[{timestamp}] {prefix} {message}")
    
    def clean_idcard_vectorized(self, idcard_series: pd.Series) -> pd.Series:
        """向量化的身份证号清洗函数"""
        # 转换为字符串并去除空格
        cleaned = idcard_series.astype(str).str.strip()
        # 去除.0后缀（Excel数字格式导致）
        cleaned = cleaned.str.replace(r'\.0$', '', regex=True)
        # 将'nan'字符串转换为NaN
        cleaned = cleaned.replace('nan', np.nan)
        return cleaned
    
    def validate_idcard_vectorized(self, idcard_series: pd.Series) -> pd.Series:
        """向量化的身份证号验证函数"""
        # 检查是否为空
        not_null = idcard_series.notna()
        
        # 检查长度（15位或18位）
        valid_length = idcard_series.str.len().isin([15, 18])
        
        # 检查格式（15位全数字，18位前17位数字+最后一位数字或X）
        pattern_15 = r'^\d{15}$'
        pattern_18 = r'^\d{17}[\dXx]$'
        
        valid_format = (
            (idcard_series.str.len() == 15) & idcard_series.str.match(pattern_15) |
            (idcard_series.str.len() == 18) & idcard_series.str.match(pattern_18)
        )
        
        return not_null & valid_length & valid_format
    
    def load_and_validate_data(self, main_file: str, label_file: str) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """加载和验证数据文件"""
        self.log("开始加载数据文件...")
        
        # 加载主数据文件
        try:
            self.log(f"正在读取主数据文件: {main_file}")
            main_df = pd.read_excel(main_file)
            self.log(f"主数据文件加载成功 - 形状: {main_df.shape}", "SUCCESS")
            
            if 'APP_IDCARD' not in main_df.columns:
                raise ValueError("主数据文件缺少 APP_IDCARD 列")
                
        except Exception as e:
            self.log(f"主数据文件加载失败: {e}", "ERROR")
            return None, None
        
        # 加载标记文件
        try:
            self.log(f"正在读取标记文件: {label_file}")
            label_df = pd.read_excel(label_file)
            self.log(f"标记文件加载成功 - 形状: {label_df.shape}", "SUCCESS")
            
            required_cols = ['证件号', '标签']
            missing_cols = [col for col in required_cols if col not in label_df.columns]
            if missing_cols:
                raise ValueError(f"标记文件缺少必要列: {missing_cols}")
                
        except Exception as e:
            self.log(f"标记文件加载失败: {e}", "ERROR")
            return None, None
        
        return main_df, label_df
    
    def clean_and_validate(self, main_df: pd.DataFrame, label_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """清洗和验证数据"""
        self.log("开始数据清洗和验证...")
        
        # 清洗主数据
        main_df = main_df.copy()
        main_df['APP_IDCARD_CLEAN'] = self.clean_idcard_vectorized(main_df['APP_IDCARD'])
        main_valid = self.validate_idcard_vectorized(main_df['APP_IDCARD_CLEAN'])
        
        valid_count = main_valid.sum()
        valid_rate = valid_count / len(main_df) * 100
        self.log(f"主数据有效身份证号: {valid_count:,}/{len(main_df):,} ({valid_rate:.1f}%)")
        
        # 清洗标记数据
        label_df = label_df.copy()
        label_df['证件号_CLEAN'] = self.clean_idcard_vectorized(label_df['证件号'])
        label_valid = self.validate_idcard_vectorized(label_df['证件号_CLEAN'])
        
        valid_count = label_valid.sum()
        valid_rate = valid_count / len(label_df) * 100
        self.log(f"标记数据有效身份证号: {valid_count:,}/{len(label_df):,} ({valid_rate:.1f}%)")
        
        # 清洗标签
        label_df['标签_CLEAN'] = label_df['标签'].astype(str).str.strip()
        
        # 处理重复数据
        duplicates = label_df['证件号_CLEAN'].duplicated().sum()
        if duplicates > 0:
            self.log(f"发现重复身份证号: {duplicates}个，将保留第一条记录", "WARNING")
            label_df = label_df.drop_duplicates(subset=['证件号_CLEAN'], keep='first')
        
        # 更新统计信息
        self.stats.update({
            'main_total': len(main_df),
            'main_valid_idcard': main_valid.sum(),
            'label_total': len(label_df),
            'label_valid_idcard': label_valid.sum(),
            'label_duplicates': duplicates
        })
        
        return main_df, label_df
    
    def merge_data(self, main_df: pd.DataFrame, label_df: pd.DataFrame) -> pd.DataFrame:
        """合并数据"""
        self.log("开始数据合并...")
        
        # 准备合并数据
        merge_cols = ['证件号_CLEAN', '标签_CLEAN']
        optional_cols = ['姓名', '手机号']
        
        for col in optional_cols:
            if col in label_df.columns:
                merge_cols.append(col)
        
        label_for_merge = label_df[merge_cols].rename(columns={
            '证件号_CLEAN': 'IDCARD_KEY',
            '标签_CLEAN': 'LABEL'
        })
        
        # 执行合并
        main_df['IDCARD_KEY'] = main_df['APP_IDCARD_CLEAN']
        merged_df = pd.merge(main_df, label_for_merge, on='IDCARD_KEY', how='left')
        
        # 统计匹配结果
        matched_count = merged_df['LABEL'].notna().sum()
        match_rate = matched_count / len(merged_df) * 100
        
        self.log(f"数据合并完成 - 匹配率: {matched_count:,}/{len(merged_df):,} ({match_rate:.1f}%)", "SUCCESS")
        
        # 更新统计信息
        self.stats.update({
            'merged_total': len(merged_df),
            'matched_count': matched_count,
            'match_rate': match_rate
        })
        
        return merged_df
    
    def generate_output(self, merged_df: pd.DataFrame, output_prefix: str = "result_labeled") -> str:
        """生成输出文件"""
        self.log("准备生成输出文件...")
        
        # 准备输出数据
        output_df = merged_df.copy()
        
        # 清理临时列
        temp_cols = ['APP_IDCARD_CLEAN', 'IDCARD_KEY']
        output_df = output_df.drop(columns=[col for col in temp_cols if col in output_df.columns])
        
        # 重命名标签列
        if 'LABEL' in output_df.columns:
            output_df = output_df.rename(columns={'LABEL': '好坏标签'})
        
        # 调整列顺序
        cols = list(output_df.columns)
        if '好坏标签' in cols:
            cols.remove('好坏标签')
            cols.insert(1, '好坏标签')
        output_df = output_df[cols]
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_filename = f"{output_prefix}_{timestamp}.xlsx"
        
        # 保存文件
        try:
            output_df.to_excel(output_filename, index=False)
            self.log(f"输出文件保存成功: {output_filename}", "SUCCESS")
            
            # 更新统计信息
            labeled_count = output_df['好坏标签'].notna().sum()
            coverage_rate = labeled_count / len(output_df) * 100
            
            self.stats.update({
                'output_filename': output_filename,
                'output_rows': len(output_df),
                'output_cols': len(output_df.columns),
                'label_coverage': coverage_rate
            })
            
            return output_filename
            
        except Exception as e:
            self.log(f"文件保存失败: {e}", "ERROR")
            return None
    
    def generate_report(self, output_filename: Optional[str] = None) -> str:
        """生成处理报告"""
        self.log("生成处理报告...")
        
        report_lines = [
            "# 数据标记任务处理报告（优化版）",
            f"\n## 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "\n## 处理统计"
        ]
        
        if self.stats:
            report_lines.extend([
                f"- 主数据记录数: {self.stats.get('main_total', 0):,}",
                f"- 主数据有效身份证: {self.stats.get('main_valid_idcard', 0):,}",
                f"- 标记数据记录数: {self.stats.get('label_total', 0):,}",
                f"- 标记数据有效身份证: {self.stats.get('label_valid_idcard', 0):,}",
                f"- 数据匹配率: {self.stats.get('match_rate', 0):.1f}%",
                f"- 标签覆盖率: {self.stats.get('label_coverage', 0):.1f}%"
            ])
            
            if output_filename:
                report_lines.extend([
                    f"\n## 输出文件",
                    f"- 文件名: {output_filename}",
                    f"- 数据行数: {self.stats.get('output_rows', 0):,}",
                    f"- 数据列数: {self.stats.get('output_cols', 0):,}"
                ])
        
        report_lines.append(f"\n## 任务状态: {'成功完成' if output_filename else '执行失败'}")
        
        # 保存报告
        report_filename = f"数据标记报告_优化版_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_lines))
            self.log(f"处理报告已保存: {report_filename}", "SUCCESS")
        except Exception as e:
            self.log(f"报告保存失败: {e}", "ERROR")
        
        return report_filename
    
    def process_complete_pipeline(self, main_file: str, label_file: str, output_prefix: str = "result_labeled") -> Dict[str, Any]:
        """完整的数据处理流水线"""
        self.log("=" * 60)
        self.log("开始执行数据标记任务（优化版）")
        self.log("=" * 60)
        
        start_time = datetime.now()
        
        # 步骤1: 加载数据
        main_df, label_df = self.load_and_validate_data(main_file, label_file)
        if main_df is None or label_df is None:
            return {'success': False, 'error': '数据加载失败'}
        
        # 步骤2: 清洗和验证
        main_df, label_df = self.clean_and_validate(main_df, label_df)
        
        # 步骤3: 合并数据
        merged_df = self.merge_data(main_df, label_df)
        
        # 步骤4: 生成输出
        output_filename = self.generate_output(merged_df, output_prefix)
        
        # 步骤5: 生成报告
        report_filename = self.generate_report(output_filename)
        
        # 计算总耗时
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        self.log("=" * 60)
        self.log(f"任务完成 - 耗时: {duration:.2f}秒")
        self.log("=" * 60)
        
        return {
            'success': output_filename is not None,
            'output_file': output_filename,
            'report_file': report_filename,
            'stats': self.stats.copy(),
            'duration': duration
        }


def quick_label_data(main_file: str, label_file: str, output_prefix: str = "result_labeled", verbose: bool = True) -> Dict[str, Any]:
    """快速数据标记函数 - 一键执行"""
    processor = DataLabelingProcessor(verbose=verbose)
    return processor.process_complete_pipeline(main_file, label_file, output_prefix)


if __name__ == "__main__":
    # 示例用法
    result = quick_label_data(
        main_file="result_20250616_152548.xlsx",
        label_file="/Users/<USER>/Downloads/0206样本.xlsx"
    )
    
    if result['success']:
        print(f"✓ 任务成功完成")
        print(f"✓ 输出文件: {result['output_file']}")
        print(f"✓ 报告文件: {result['report_file']}")
    else:
        print(f"✗ 任务执行失败") 