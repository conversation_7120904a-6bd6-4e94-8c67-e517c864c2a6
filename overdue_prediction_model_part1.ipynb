{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 逾期概率预测模型 - 第一部分：数据预处理与数据集划分\n", "\n", "本notebook实现完整的逾期概率预测模型，包括：\n", "1. 数据加载与探索\n", "2. 数据预处理（缺失值、异常值处理）\n", "3. 训练集/测试集划分\n", "4. 特征工程准备"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import joblib\n", "\n", "# 机器学习相关\n", "from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score\n", "from sklearn.preprocessing import LabelEncoder, StandardScaler, RobustScaler\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.metrics import (\n", "    roc_auc_score, roc_curve, precision_recall_curve, \n", "    classification_report, confusion_matrix, f1_score,\n", "    average_precision_score\n", ")\n", "\n", "# LightGBM\n", "import lightgbm as lgb\n", "\n", "# 设置随机种子\n", "RANDOM_STATE = 42\n", "np.random.seed(RANDOM_STATE)\n", "\n", "# 设置显示选项\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"✓ 库导入完成\")\n", "print(f\"✓ 随机种子设置为: {RANDOM_STATE}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 数据加载与基本信息探索"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载数据\n", "print(\"=== 数据加载 ===\")\n", "try:\n", "    # 假设数据文件名为 'credit_data.csv'，请根据实际文件名修改\n", "    data = pd.read_csv('credit_data.csv')\n", "    print(f\"✓ 数据加载成功\")\n", "    print(f\"✓ 数据形状: {data.shape}\")\n", "except FileNotFoundError:\n", "    print(\"❌ 请确保数据文件存在，并修改文件路径\")\n", "    # 这里可以添加示例数据生成代码用于测试\n", "    raise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 数据基本信息\n", "print(\"=== 数据基本信息 ===\")\n", "print(f\"数据维度: {data.shape}\")\n", "print(f\"\\n列名预览:\")\n", "print(data.columns.tolist()[:10])  # 显示前10个列名\n", "if len(data.columns) > 10:\n", "    print(f\"... 还有 {len(data.columns) - 10} 个列\")\n", "\n", "print(f\"\\n数据类型分布:\")\n", "print(data.dtypes.value_counts())\n", "\n", "print(f\"\\n前5行数据:\")\n", "display(data.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 检查目标变量\n", "print(\"=== 目标变量分析 ===\")\n", "\n", "# 假设目标变量列名为 'target' 或 'label' 或 'overdue'，请根据实际情况修改\n", "target_candidates = ['target', 'label', 'overdue', 'default', 'y', 'is_overdue']\n", "target_col = None\n", "\n", "for col in target_candidates:\n", "    if col in data.columns:\n", "        target_col = col\n", "        break\n", "\n", "if target_col is None:\n", "    print(\"❌ 未找到目标变量列，请手动指定\")\n", "    print(\"可能的列名:\", [col for col in data.columns if any(keyword in col.lower() for keyword in ['target', 'label', 'overdue', 'default'])])\n", "    # 手动指定目标变量列名\n", "    target_col = input(\"请输入目标变量列名: \")\n", "\n", "print(f\"✓ 目标变量列: {target_col}\")\n", "\n", "if target_col in data.columns:\n", "    print(f\"\\n目标变量分布:\")\n", "    print(data[target_col].value_counts())\n", "    print(f\"\\n目标变量比例:\")\n", "    print(data[target_col].value_counts(normalize=True))\n", "    \n", "    # 可视化目标变量分布\n", "    plt.figure(figsize=(10, 4))\n", "    \n", "    plt.subplot(1, 2, 1)\n", "    data[target_col].value_counts().plot(kind='bar')\n", "    plt.title('目标变量分布')\n", "    plt.xlabel('类别')\n", "    plt.ylabel('数量')\n", "    \n", "    plt.subplot(1, 2, 2)\n", "    data[target_col].value_counts(normalize=True).plot(kind='pie', autopct='%1.2f%%')\n", "    plt.title('目标变量比例')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 计算不平衡比例\n", "    class_counts = data[target_col].value_counts()\n", "    imbalance_ratio = class_counts.max() / class_counts.min()\n", "    print(f\"\\n数据不平衡比例: {imbalance_ratio:.2f}:1\")\n", "    if imbalance_ratio > 3:\n", "        print(\"⚠️  数据存在较严重的不平衡问题，需要特殊处理\")\n", "else:\n", "    print(f\"❌ 列 '{target_col}' 不存在于数据中\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 数据质量分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 缺失值分析\n", "print(\"=== 缺失值分析 ===\")\n", "\n", "def analyze_missing_values(df):\n", "    \"\"\"\n", "    分析数据框的缺失值情况\n", "    \"\"\"\n", "    missing_stats = pd.DataFrame({\n", "        'column': df.columns,\n", "        'missing_count': df.isnull().sum(),\n", "        'missing_percentage': (df.isnull().sum() / len(df)) * 100,\n", "        'dtype': df.dtypes\n", "    })\n", "    \n", "    missing_stats = missing_stats[missing_stats['missing_count'] > 0].sort_values(\n", "        'missing_percentage', ascending=False\n", "    )\n", "    \n", "    return missing_stats\n", "\n", "missing_stats = analyze_missing_values(data)\n", "\n", "if len(missing_stats) > 0:\n", "    print(f\"发现 {len(missing_stats)} 个列存在缺失值:\")\n", "    display(missing_stats.head(20))\n", "    \n", "    # 可视化缺失值分布\n", "    if len(missing_stats) > 0:\n", "        plt.figure(figsize=(12, 6))\n", "        top_missing = missing_stats.head(20)\n", "        \n", "        plt.barh(range(len(top_missing)), top_missing['missing_percentage'])\n", "        plt.yticks(range(len(top_missing)), top_missing['column'])\n", "        plt.xlabel('缺失值百分比 (%)')\n", "        plt.title('Top 20 缺失值最多的列')\n", "        plt.gca().invert_yaxis()\n", "        \n", "        # 添加百分比标签\n", "        for i, v in enumerate(top_missing['missing_percentage']):\n", "            plt.text(v + 0.5, i, f'{v:.1f}%', va='center')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\nelse:\n", "    print(\"✓ 没有发现缺失值\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 检查特殊值（如-999, -1等）\n", "print(\"=== 特殊值检查 ===\")\n", "\n", "def check_special_values(df, special_values=[-999, -1, 999, 9999]):\n", "    \"\"\"\n", "    检查数据中的特殊值\n", "    \"\"\"\n", "    special_stats = []\n", "    \n", "    for col in df.select_dtypes(include=[np.number]).columns:\n", "        for val in special_values:\n", "            count = (df[col] == val).sum()\n", "            if count > 0:\n", "                special_stats.append({\n", "                    'column': col,\n", "                    'special_value': val,\n", "                    'count': count,\n", "                    'percentage': (count / len(df)) * 100\n", "                })\n", "    \n", "    return pd.DataFrame(special_stats)\n", "\n", "special_stats = check_special_values(data)\n", "\n", "if len(special_stats) > 0:\n", "    print(\"发现特殊值:\")\n", "    display(special_stats.sort_values('percentage', ascending=False).head(20))\nelse:\n", "    print(\"✓ 未发现常见的特殊值\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 数据预处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 数据预处理函数\n", "print(\"=== 数据预处理 ===\")\n", "\n", "def preprocess_data(df, target_col, missing_threshold=0.8, special_values=[-999]):\n", "    \"\"\"\n", "    数据预处理主函数\n", "    \n", "    Parameters:\n", "    -----------\n", "    df : pandas.DataFrame\n", "        原始数据\n", "    target_col : str\n", "        目标变量列名\n", "    missing_threshold : float\n", "        缺失值阈值，超过此比例的列将被删除\n", "    special_values : list\n", "        需要替换为NaN的特殊值\n", "    \n", "    Returns:\n", "    --------\n", "    X_processed : pandas.DataFrame\n", "        处理后的特征数据\n", "    y : pandas.Series\n", "        目标变量\n", "    preprocessing_info : dict\n", "        预处理信息\n", "    \"\"\"\n", "    \n", "    print(f\"原始数据形状: {df.shape}\")\n", "    \n", "    # 复制数据\n", "    df_processed = df.copy()\n", "    preprocessing_info = {}\n", "    \n", "    # 1. 分离特征和目标变量\n", "    if target_col not in df_processed.columns:\n", "        raise ValueError(f\"目标变量列 '{target_col}' 不存在\")\n", "    \n", "    y = df_processed[target_col].copy()\n", "    X = df_processed.drop(columns=[target_col])\n", "    \n", "    print(f\"✓ 分离特征和目标变量 - 特征数: {X.shape[1]}\")\n", "    \n", "    # 2. 处理特殊值\n", "    if special_values:\n", "        print(f\"\\n处理特殊值: {special_values}\")\n", "        for val in special_values:\n", "            replaced_count = (X == val).sum().sum()\n", "            X = X.replace(val, np.nan)\n", "            if replaced_count > 0:\n", "                print(f\"  ✓ 将 {replaced_count} 个 {val} 替换为 NaN\")\n", "    \n", "    # 3. 删除高缺失率的列\n", "    print(f\"\\n删除缺失率超过 {missing_threshold*100}% 的列\")\n", "    missing_ratios = X.isnull().sum() / len(X)\n", "    high_missing_cols = missing_ratios[missing_ratios > missing_threshold].index.tolist()\n", "    \n", "    if high_missing_cols:\n", "        X = X.drop(columns=high_missing_cols)\n", "        print(f\"  ✓ 删除了 {len(high_missing_cols)} 个高缺失率列\")\n", "        preprocessing_info['removed_high_missing_cols'] = high_missing_cols\n", "    else:\n", "        print(f\"  ✓ 没有需要删除的高缺失率列\")\n", "    \n", "    # 4. 删除常数列（方差为0的列）\n", "    print(f\"\\n删除常数列\")\n", "    numeric_cols = X.select_dtypes(include=[np.number]).columns\n", "    constant_cols = []\n", "    \n", "    for col in numeric_cols:\n", "        if X[col].nunique() <= 1:\n", "            constant_cols.append(col)\n", "    \n", "    if constant_cols:\n", "        X = X.drop(columns=constant_cols)\n", "        print(f\"  ✓ 删除了 {len(constant_cols)} 个常数列\")\n", "        preprocessing_info['removed_constant_cols'] = constant_cols\n", "    else:\n", "        print(f\"  ✓ 没有需要删除的常数列\")\n", "    \n", "    print(f\"\\n预处理后数据形状: {X.shape}\")\n", "    \n", "    return X, y, preprocessing_info\n", "\n", "# 执行预处理\n", "X_processed, y, preprocessing_info = preprocess_data(data, target_col)\n", "\n", "print(f\"\\n=== 预处理总结 ===\")\n", "print(f\"原始特征数: {data.shape[1] - 1}\")\n", "print(f\"处理后特征数: {X_processed.shape[1]}\")\n", "print(f\"删除特征数: {data.shape[1] - 1 - X_processed.shape[1]}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}