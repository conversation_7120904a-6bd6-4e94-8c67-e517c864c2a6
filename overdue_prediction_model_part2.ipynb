{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 逾期概率预测模型 - 第二部分：缺失值处理与数据集划分\n", "\n", "本notebook继续第一部分的工作，包括：\n", "1. 缺失值处理\n", "2. 数据类型处理\n", "3. 训练集/测试集划分\n", "4. 数据集基本统计"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 继续第一部分的工作\n", "# 假设已经运行了第一部分，得到了 X_processed, y, preprocessing_info\n", "\n", "print(\"=== 继续数据预处理 ===\")\n", "print(f\"当前数据形状: {X_processed.shape}\")\n", "print(f\"目标变量形状: {y.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 缺失值处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def handle_missing_values(X, strategy='auto'):\n", "    \"\"\"\n", "    处理缺失值\n", "    \n", "    Parameters:\n", "    -----------\n", "    X : pandas.DataFrame\n", "        特征数据\n", "    strategy : str\n", "        处理策略 ('auto', 'median', 'mean', 'mode')\n", "    \n", "    Returns:\n", "    --------\n", "    X_filled : pandas.DataFrame\n", "        填充后的数据\n", "    imputers : dict\n", "        各列的填充器\n", "    \"\"\"\n", "    \n", "    X_filled = X.copy()\n", "    imputers = {}\n", "    \n", "    # 分析当前缺失值情况\n", "    missing_info = X.isnull().sum()\n", "    missing_cols = missing_info[missing_info > 0].index.tolist()\n", "    \n", "    if not missing_cols:\n", "        print(\"✓ 没有缺失值需要处理\")\n", "        return X_filled, imputers\n", "    \n", "    print(f\"需要处理 {len(missing_cols)} 个列的缺失值\")\n", "    \n", "    # 分别处理数值型和分类型变量\n", "    numeric_cols = X.select_dtypes(include=[np.number]).columns.tolist()\n", "    categorical_cols = X.select_dtypes(exclude=[np.number]).columns.tolist()\n", "    \n", "    # 处理数值型变量的缺失值\n", "    numeric_missing = [col for col in missing_cols if col in numeric_cols]\n", "    if numeric_missing:\n", "        print(f\"\\n处理 {len(numeric_missing)} 个数值型列的缺失值\")\n", "        \n", "        if strategy == 'auto':\n", "            # 自动选择策略：偏态分布用中位数，正态分布用均值\n", "            for col in numeric_missing:\n", "                skewness = X[col].skew()\n", "                if abs(skewness) > 1:  # 偏态分布\n", "                    imputer = SimpleImputer(strategy='median')\n", "                    strategy_used = 'median'\n", "                else:  # 接近正态分布\n", "                    imputer = SimpleImputer(strategy='mean')\n", "                    strategy_used = 'mean'\n", "                \n", "                X_filled[col] = imputer.fit_transform(X_filled[[col]]).ravel()\n", "                imputers[col] = imputer\n", "                \n", "                missing_count = missing_info[col]\n", "                print(f\"  ✓ {col}: 使用{strategy_used}填充 {missing_count} 个缺失值\")\n", "        else:\n", "            # 使用指定策略\n", "            imputer = SimpleImputer(strategy=strategy)\n", "            X_filled[numeric_missing] = imputer.fit_transform(X_filled[numeric_missing])\n", "            for col in numeric_missing:\n", "                imputers[col] = imputer\n", "                print(f\"  ✓ {col}: 使用{strategy}填充 {missing_info[col]} 个缺失值\")\n", "    \n", "    # 处理分类型变量的缺失值\n", "    categorical_missing = [col for col in missing_cols if col in categorical_cols]\n", "    if categorical_missing:\n", "        print(f\"\\n处理 {len(categorical_missing)} 个分类型列的缺失值\")\n", "        \n", "        for col in categorical_missing:\n", "            # 分类变量用众数填充\n", "            imputer = SimpleImputer(strategy='most_frequent')\n", "            X_filled[col] = imputer.fit_transform(X_filled[[col]]).ravel()\n", "            imputers[col] = imputer\n", "            \n", "            missing_count = missing_info[col]\n", "            print(f\"  ✓ {col}: 使用众数填充 {missing_count} 个缺失值\")\n", "    \n", "    # 验证缺失值处理结果\n", "    remaining_missing = X_filled.isnull().sum().sum()\n", "    if remaining_missing == 0:\n", "        print(f\"\\n✓ 缺失值处理完成，无剩余缺失值\")\n", "    else:\n", "        print(f\"\\n⚠️  仍有 {remaining_missing} 个缺失值\")\n", "    \n", "    return X_filled, imputers\n", "\n", "# 执行缺失值处理\n", "X_filled, imputers = handle_missing_values(X_processed, strategy='auto')\n", "\n", "print(f\"\\n缺失值处理前后对比:\")\n", "print(f\"处理前缺失值总数: {X_processed.isnull().sum().sum()}\")\n", "print(f\"处理后缺失值总数: {X_filled.isnull().sum().sum()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 数据类型处理与编码"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def encode_categorical_features(X, encoding_method='label'):\n", "    \"\"\"\n", "    对分类特征进行编码\n", "    \n", "    Parameters:\n", "    -----------\n", "    X : pandas.DataFrame\n", "        特征数据\n", "    encoding_method : str\n", "        编码方法 ('label', 'onehot')\n", "    \n", "    Returns:\n", "    --------\n", "    X_encoded : pandas.DataFrame\n", "        编码后的数据\n", "    encoders : dict\n", "        编码器字典\n", "    \"\"\"\n", "    \n", "    X_encoded = X.copy()\n", "    encoders = {}\n", "    \n", "    # 识别分类变量\n", "    categorical_cols = X.select_dtypes(exclude=[np.number]).columns.tolist()\n", "    \n", "    if not categorical_cols:\n", "        print(\"✓ 没有分类变量需要编码\")\n", "        return X_encoded, encoders\n", "    \n", "    print(f\"发现 {len(categorical_cols)} 个分类变量需要编码\")\n", "    \n", "    if encoding_method == 'label':\n", "        # 标签编码\n", "        for col in categorical_cols:\n", "            le = LabelEncoder()\n", "            X_encoded[col] = le.fit_transform(X_encoded[col].astype(str))\n", "            encoders[col] = le\n", "            \n", "            unique_count = len(le.classes_)\n", "            print(f\"  ✓ {col}: 标签编码完成 ({unique_count} 个唯一值)\")\n", "    \n", "    elif encoding_method == 'onehot':\n", "        # 独热编码（仅对低基数分类变量使用）\n", "        from sklearn.preprocessing import OneHotEncoder\n", "        \n", "        for col in categorical_cols:\n", "            unique_count = X[col].nunique()\n", "            \n", "            if unique_count <= 10:  # 低基数变量使用独热编码\n", "                ohe = OneHotEncoder(sparse=False, drop='first')\n", "                encoded_cols = ohe.fit_transform(X_encoded[[col]])\n", "                \n", "                # 创建新列名\n", "                feature_names = [f\"{col}_{cat}\" for cat in ohe.categories_[0][1:]]\n", "                \n", "                # 添加编码后的列\n", "                for i, name in enumerate(feature_names):\n", "                    X_encoded[name] = encoded_cols[:, i]\n", "                \n", "                # 删除原列\n", "                X_encoded = X_encoded.drop(columns=[col])\n", "                encoders[col] = ohe\n", "                \n", "                print(f\"  ✓ {col}: 独热编码完成 ({unique_count} -> {len(feature_names)} 列)\")\n", "            \n", "            else:  # 高基数变量使用标签编码\n", "                le = LabelEncoder()\n", "                X_encoded[col] = le.fit_transform(X_encoded[col].astype(str))\n", "                encoders[col] = le\n", "                \n", "                print(f\"  ✓ {col}: 标签编码完成 (高基数: {unique_count} 个唯一值)\")\n", "    \n", "    print(f\"\\n编码完成，最终特征数: {X_encoded.shape[1]}\")\n", "    \n", "    return X_encoded, encoders\n", "\n", "# 执行分类特征编码\n", "X_encoded, encoders = encode_categorical_features(X_filled, encoding_method='label')\n", "\n", "print(f\"\\n数据类型分布:\")\n", "print(X_encoded.dtypes.value_counts())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 训练集/测试集划分"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def split_dataset(X, y, test_size=0.2, validation_size=0.1, random_state=42):\n", "    \"\"\"\n", "    划分数据集为训练集、验证集和测试集\n", "    \n", "    Parameters:\n", "    -----------\n", "    X : pandas.DataFrame\n", "        特征数据\n", "    y : pandas.Series\n", "        目标变量\n", "    test_size : float\n", "        测试集比例\n", "    validation_size : float\n", "        验证集比例（从训练集中划分）\n", "    random_state : int\n", "        随机种子\n", "    \n", "    Returns:\n", "    --------\n", "    dict: 包含所有数据集的字典\n", "    \"\"\"\n", "    \n", "    print(f\"=== 数据集划分 ===\")\n", "    print(f\"原始数据: {X.shape[0]} 样本, {X.shape[1]} 特征\")\n", "    print(f\"测试集比例: {test_size}\")\n", "    print(f\"验证集比例: {validation_size}\")\n", "    \n", "    # 首先划分训练集和测试集\n", "    X_temp, X_test, y_temp, y_test = train_test_split(\n", "        X, y, \n", "        test_size=test_size, \n", "        random_state=random_state, \n", "        stratify=y  # 保持类别比例\n", "    )\n", "    \n", "    # 从临时训练集中划分出验证集\n", "    if validation_size > 0:\n", "        val_size_adjusted = validation_size / (1 - test_size)  # 调整验证集比例\n", "        X_train, X_val, y_train, y_val = train_test_split(\n", "            X_temp, y_temp,\n", "            test_size=val_size_adjusted,\n", "            random_state=random_state,\n", "            stratify=y_temp\n", "        )\n", "    else:\n", "        X_train, y_train = X_temp, y_temp\n", "        X_val, y_val = None, None\n", "    \n", "    # 打印划分结果\n", "    print(f\"\\n划分结果:\")\n", "    print(f\"训练集: {X_train.shape[0]} 样本 ({X_train.shape[0]/X.shape[0]*100:.1f}%)\")\n", "    if X_val is not None:\n", "        print(f\"验证集: {X_val.shape[0]} 样本 ({X_val.shape[0]/X.shape[0]*100:.1f}%)\")\n", "    print(f\"测试集: {X_test.shape[0]} 样本 ({X_test.shape[0]/X.shape[0]*100:.1f}%)\")\n", "    \n", "    # 检查类别分布\n", "    print(f\"\\n各数据集的类别分布:\")\n", "    print(f\"训练集: {dict(y_train.value_counts())}\")\n", "    if y_val is not None:\n", "        print(f\"验证集: {dict(y_val.value_counts())}\")\n", "    print(f\"测试集: {dict(y_test.value_counts())}\")\n", "    \n", "    # 返回数据集字典\n", "    datasets = {\n", "        'X_train': X_train,\n", "        'y_train': y_train,\n", "        'X_test': X_test,\n", "        'y_test': y_test\n", "    }\n", "    \n", "    if X_val is not None:\n", "        datasets['X_val'] = X_val\n", "        datasets['y_val'] = y_val\n", "    \n", "    return datasets\n", "\n", "# 执行数据集划分\n", "datasets = split_dataset(X_encoded, y, test_size=0.2, validation_size=0.1, random_state=RANDOM_STATE)\n", "\n", "# 提取各个数据集\n", "X_train = datasets['X_train']\n", "y_train = datasets['y_train']\n", "X_val = datasets.get('X_val')\n", "y_val = datasets.get('y_val')\n", "X_test = datasets['X_test']\n", "y_test = datasets['y_test']\n", "\n", "print(f\"\\n✓ 数据集划分完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 数据集基本统计与可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 数据集统计信息\n", "def analyze_datasets(datasets):\n", "    \"\"\"\n", "    分析各个数据集的基本统计信息\n", "    \"\"\"\n", "    \n", "    print(\"=== 数据集统计分析 ===\")\n", "    \n", "    # 基本信息\n", "    for name, data in datasets.items():\n", "        if 'X_' in name:\n", "            print(f\"\\n{name}:\")\n", "            print(f\"  形状: {data.shape}\")\n", "            print(f\"  内存使用: {data.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "            \n", "            # 数据类型分布\n", "            dtype_counts = data.dtypes.value_counts()\n", "            print(f\"  数据类型: {dict(dtype_counts)}\")\n", "    \n", "    # 目标变量分布对比\n", "    print(f\"\\n目标变量分布对比:\")\n", "    fig, axes = plt.subplots(1, 3, figsize=(15, 4))\n", "    \n", "    y_datasets = [(name, data) for name, data in datasets.items() if 'y_' in name]\n", "    \n", "    for i, (name, y_data) in enumerate(y_datasets):\n", "        if i < 3:  # 最多显示3个子图\n", "            y_data.value_counts().plot(kind='bar', ax=axes[i], title=name)\n", "            axes[i].set_ylabel('数量')\n", "            \n", "            # 添加百分比标签\n", "            total = len(y_data)\n", "            for j, v in enumerate(y_data.value_counts()):\n", "                axes[i].text(j, v + total*0.01, f'{v/total*100:.1f}%', \n", "                           ha='center', va='bottom')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 特征统计\n", "    print(f\"\\n训练集特征统计:\")\n", "    X_train = datasets['X_train']\n", "    \n", "    # 数值型特征统计\n", "    numeric_features = X_train.select_dtypes(include=[np.number]).columns\n", "    print(f\"数值型特征数量: {len(numeric_features)}\")\n", "    \n", "    if len(numeric_features) > 0:\n", "        print(f\"数值型特征统计摘要:\")\n", "        display(X_train[numeric_features].describe())\n", "    \n", "    return True\n", "\n", "# 执行数据集分析\n", "analyze_datasets(datasets)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 保存预处理后的数据和相关信息\n", "print(\"=== 保存预处理结果 ===\")\n", "\n", "# 保存数据集\n", "save_info = {\n", "    'datasets': datasets,\n", "    'preprocessing_info': preprocessing_info,\n", "    'imputers': imputers,\n", "    'encoders': encoders,\n", "    'target_col': target_col,\n", "    'random_state': RANDOM_STATE\n", "}\n", "\n", "# 使用joblib保存\n", "joblib.dump(save_info, 'preprocessed_data.pkl')\n", "print(\"✓ 预处理结果已保存到 'preprocessed_data.pkl'\")\n", "\n", "# 也可以保存为CSV格式（可选）\n", "X_train.to_csv('X_train.csv', index=False)\n", "y_train.to_csv('y_train.csv', index=False)\n", "X_test.to_csv('X_test.csv', index=False)\n", "y_test.to_csv('y_test.csv', index=False)\n", "\n", "if X_val is not None:\n", "    X_val.to_csv('X_val.csv', index=False)\n", "    y_val.to_csv('y_val.csv', index=False)\n", "\n", "print(\"✓ 数据集已保存为CSV文件\")\n", "\n", "print(f\"\\n=== 第二部分完成 ===\")\n", "print(f\"准备进入第三部分：特征选择与降维\")\n", "print(f\"当前训练集形状: {X_train.shape}\")\n", "print(f\"当前特征数量: {X_train.shape[1]}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}