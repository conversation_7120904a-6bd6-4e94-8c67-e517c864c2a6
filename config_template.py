# -*- coding: utf-8 -*-
"""
数据库配置模板
请复制此文件为 config.py 并填写真实的数据库连接信息
"""

# 数据库连接配置
DATABASE_CONFIG = {
    'host': 'localhost',           # 数据库服务器地址，如：'*************'
    'port': 3306,                 # 数据库端口，默认MySQL端口为3306
    'user': 'your_username',      # 数据库用户名
    'password': 'your_password',  # 数据库密码
    'database': 'your_database'   # 数据库名称
}

# Excel导出配置
EXCEL_CONFIG = {
    'filename': 'crd_datasource_params_export.xlsx',  # 输出文件名
    'sheet_name': '数据源参数'                          # Excel工作表名称
} 