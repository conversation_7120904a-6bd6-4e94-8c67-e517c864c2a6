from sqlalchemy import create_engine
import pandas as pd
import json
from datetime import datetime

engine = create_engine(
    'mysql+pymysql://root:root%402024@*************:3306/smartdecision?charset=utf8mb4')

serial_numbers_input = input("请输入 SERIAL_NUMBER（多个用逗号分隔）：")
serial_numbers = [sn.strip()
                  for sn in serial_numbers_input.split(',') if sn.strip()]
serial_numbers_str = ",".join([f"'{sn}'" for sn in serial_numbers])

sql1 = f"""
SELECT ctsd.SERIAL_NUMBER, ctsd.RESOURCE_CODE, ctsd.SERVER_RESPONSE_DATA
FROM crd_trade_server_data ctsd
INNER JOIN crd_trade_log ctl ON ctsd.SERIAL_NUMBER = ctl.SERIAL_NUMBER
WHERE ctl.PRODUCT_CODE = '202088888820250327150945' 
  and ctsd.SERIAL_NUMBER in ({serial_numbers_str});
"""
df_data = pd.read_sql(sql1, engine)

sql2 = """
select RESOURCE_CODE, ENAME, CNAME 
from crd_datasource_params 
where FIELD_TYPE='2' and VERSION='V1' 
and RESOURCE_CODE not in ('yinkesit10001', '976652b23d41493c89396367fbfba2a9', 'test001') 
and CNAME not in ('返回码', '返回代码', '错误码', '状态') 
and ENAME not in ('supplement_case_list','loan_willingness_profile','execution_limited','execution_pro','loan_rate_profile') 
order by RESOURCE_CODE;
"""
df_params = pd.read_sql(sql2, engine)

# 生成数据记录
records = []
for _, row in df_data.iterrows():
    sn = row['SERIAL_NUMBER']
    rc = row['RESOURCE_CODE']
    try:
        data = json.loads(row['SERVER_RESPONSE_DATA'])

        def normalize_null(v):
            if v is None:
                return -999
            if isinstance(v, str) and v.strip().lower().replace('"', '') == 'null':
                return -999
            return v
        data = {k: normalize_null(v) for k, v in data.items()}
    except Exception:
        data = {}

    params = df_params[df_params['RESOURCE_CODE'] == rc]
    for _, prow in params.iterrows():
        ename = prow['ENAME']
        cname = prow['CNAME']
        value = data.get(ename, None)
        records.append({
            'RESOURCE_CODE': rc,
            'ENAME': ename,
            'CNAME': cname,
            'SERIAL_NUMBER': sn,
            'VALUE': value
        })

df_long = pd.DataFrame(records)

# 针对每个 SERIAL_NUMBER 生成单独的 Excel 文件
unique_serial_numbers = df_long['SERIAL_NUMBER'].unique()
for sn in unique_serial_numbers:
    df_sn = df_long[df_long['SERIAL_NUMBER'] == sn]

    # pivot so that: index=["RESOURCE_CODE", "ENAME", "CNAME"], columns=["SERIAL_NUMBER"], values=["VALUE"]
    df_pivot = df_sn.pivot_table(index=['RESOURCE_CODE', 'ENAME', 'CNAME'],
                                 columns='SERIAL_NUMBER',
                                 values='VALUE',
                                 aggfunc='first').reset_index()

    # 将列名恢复为普通列
    df_pivot.columns.name = None

    # 保存
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'result_{sn}_{timestamp}.xlsx'
    df_pivot.to_excel(filename, index=False)
    print(f"处理完成，结果已保存为 {filename}")
