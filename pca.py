#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据字段降维与分类分析脚本
对实际数据进行PCA分析，选择最重要的字段
"""

import pandas as pd
import numpy as np
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
import os
import logging
from collections import Counter

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataPCAAnalyzer:
    """数据PCA分析器"""
    
    def __init__(self, data_file=None, fields_file=None):
        """
        初始化数据PCA分析器
        
        Args:
            data_file (str): 包含实际数据的文件路径
            fields_file (str): 包含字段信息的文件路径
        """
        self.data_file = data_file
        self.fields_file = fields_file
        self.data_df = None
        self.fields_df = None
        self.pca_model = None
        self.pca_result = None
        self.feature_importance = None
        
    def load_data(self):
        """加载数据和字段信息"""
        try:
            # 加载字段信息
            if self.fields_file:
                logger.info(f"正在加载字段信息文件: {self.fields_file}")
                self.fields_df = pd.read_excel(self.fields_file)
                logger.info(f"字段信息加载成功，共 {len(self.fields_df)} 个字段")
            
            # 加载实际数据
            if self.data_file:
                logger.info(f"正在加载数据文件: {self.data_file}")
                self.data_df = pd.read_excel(self.data_file)
                logger.info(f"数据加载成功，共 {len(self.data_df)} 行，{len(self.data_df.columns)} 列")
            
            return True
        except Exception as e:
            logger.error(f"数据加载失败: {str(e)}")
            return False
    
    def prepare_data(self):
        """准备数据，处理缺失值和非数值型数据"""
        if self.data_df is None:
            logger.error("请先加载数据")
            return False
        
        logger.info("正在准备数据...")
        
        # 只保留数值型列
        numeric_cols = self.data_df.select_dtypes(include=['int64', 'float64']).columns
        logger.info(f"数值型列数量: {len(numeric_cols)}")
        
        # 如果数值型列太少，可以尝试将一些分类变量转换为数值
        if len(numeric_cols) < 10:
            logger.warning("数值型列太少，尝试将分类变量转换为数值...")
            # 这里可以添加更多的数据转换逻辑
        
        # 处理缺失值
        self.data_df[numeric_cols] = self.data_df[numeric_cols].fillna(self.data_df[numeric_cols].mean())
        
        # 只保留数值型数据
        self.processed_data = self.data_df[numeric_cols]
        logger.info(f"数据准备完成，最终使用 {self.processed_data.shape[1]} 个数值型特征")
        
        return True
    
    def apply_pca(self, n_components=None, variance_threshold=0.95):
        """
        应用PCA降维
        
        Args:
            n_components (int): 主成分数量，如果为None则自动根据方差阈值选择
            variance_threshold (float): 累积方差解释率阈值
        """
        if not hasattr(self, 'processed_data') or self.processed_data is None:
            logger.error("请先准备数据")
            return False
        
        logger.info("正在应用PCA降维...")
        
        # 标准化数据
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(self.processed_data)
        
        # 确定组件数量
        if n_components is None:
            # 先用全部特征运行PCA
            temp_pca = PCA()
            temp_pca.fit(scaled_data)
            
            # 计算需要多少个组件才能达到方差阈值
            cumulative_variance = np.cumsum(temp_pca.explained_variance_ratio_)
            n_components = np.argmax(cumulative_variance >= variance_threshold) + 1
            logger.info(f"根据 {variance_threshold} 方差阈值，选择了 {n_components} 个主成分")
        
        # 应用PCA
        self.pca_model = PCA(n_components=n_components)
        self.pca_result = self.pca_model.fit_transform(scaled_data)
        
        # 计算方差解释率
        explained_variance = self.pca_model.explained_variance_ratio_
        logger.info(f"PCA降维完成，解释方差比例: {explained_variance}")
        logger.info(f"累积方差比例: {np.sum(explained_variance):.4f}")
        
        # 计算特征重要性
        self.calculate_feature_importance()
        
        return True
    
    def calculate_feature_importance(self):
        """计算原始特征对主成分的重要性"""
        if self.pca_model is None:
            logger.error("请先应用PCA")
            return False
        
        # 获取特征重要性
        feature_names = self.processed_data.columns
        components = self.pca_model.components_
        
        # 计算每个特征的总体重要性（基于所有主成分的加权和）
        importance = np.sum(
            np.abs(components.T) * self.pca_model.explained_variance_ratio_,
            axis=1
        )
        
        # 创建特征重要性DataFrame
        self.feature_importance = pd.DataFrame({
            'Feature': feature_names,
            'Importance': importance
        }).sort_values('Importance', ascending=False)
        
        logger.info("特征重要性计算完成")
        logger.info(f"前10个最重要的特征: \n{self.feature_importance.head(10)}")
        
        return True
    
    def visualize_pca(self):
        """可视化PCA结果"""
        if self.pca_model is None:
            logger.error("请先应用PCA")
            return False
        
        logger.info("正在生成PCA可视化...")
        
        # 1. 绘制方差解释率图
        plt.figure(figsize=(10, 6))
        explained_variance = self.pca_model.explained_variance_ratio_
        cumulative_variance = np.cumsum(explained_variance)
        
        plt.bar(range(1, len(explained_variance) + 1), explained_variance, alpha=0.7, label='单个方差')
        plt.step(range(1, len(cumulative_variance) + 1), cumulative_variance, where='mid', label='累积方差')
        plt.axhline(y=0.95, linestyle='--', color='r', label='95% 方差阈值')
        
        plt.xlabel('主成分数量')
        plt.ylabel('解释方差比例')
        plt.title('PCA方差解释率')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.savefig('pca_explained_variance.png', dpi=300, bbox_inches='tight')
        
        # 2. 绘制特征重要性图
        plt.figure(figsize=(12, 8))
        top_features = self.feature_importance.head(20)  # 只显示前20个特征
        sns.barplot(x='Importance', y='Feature', data=top_features)
        plt.title('特征重要性 (前20个特征)')
        plt.tight_layout()
        plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
        
        # 3. 如果有实际数据，绘制前两个主成分的散点图
        if self.pca_result is not None and self.pca_result.shape[1] >= 2:
            plt.figure(figsize=(10, 8))
            plt.scatter(self.pca_result[:, 0], self.pca_result[:, 1], alpha=0.7)
            plt.xlabel('主成分1')
            plt.ylabel('主成分2')
            plt.title('数据在前两个主成分上的分布')
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.savefig('pca_scatter.png', dpi=300, bbox_inches='tight')
        
        logger.info("PCA可视化图已保存")
        return True
    
    def export_results(self, output_file='pca_analysis_results.xlsx'):
        """
        导出PCA分析结果
        
        Args:
            output_file (str): 输出文件名
        """
        if self.feature_importance is None:
            logger.error("请先应用PCA并计算特征重要性")
            return False
        
        logger.info(f"正在将PCA分析结果导出到 {output_file}...")
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 导出特征重要性
            self.feature_importance.to_excel(writer, sheet_name='特征重要性', index=False)
            
            # 导出主成分解释方差
            explained_variance_df = pd.DataFrame({
                '主成分': range(1, len(self.pca_model.explained_variance_ratio_) + 1),
                '解释方差比例': self.pca_model.explained_variance_ratio_,
                '累积方差比例': np.cumsum(self.pca_model.explained_variance_ratio_)
            })
            explained_variance_df.to_excel(writer, sheet_name='方差解释率', index=False)
            
            # 导出主成分载荷
            loadings = pd.DataFrame(
                self.pca_model.components_.T,
                columns=[f'PC{i+1}' for i in range(self.pca_model.components_.shape[0])],
                index=self.processed_data.columns
            )
            loadings.to_excel(writer, sheet_name='主成分载荷')
            
            # 如果有字段信息，将特征重要性与字段信息合并
            if self.fields_df is not None:
                merged_importance = pd.merge(
                    self.feature_importance,
                    self.fields_df,
                    left_on='Feature',
                    right_on='RESOURCE_CODE',  # 根据实际情况调整
                    how='left'
                )
                merged_importance.to_excel(writer, sheet_name='特征重要性(带字段信息)', index=False)
        
        logger.info(f"PCA分析结果已成功导出到 {output_file}")
        return True
    
    def recommend_fields(self, top_n=50):
        """
        推荐最重要的字段
        
        Args:
            top_n (int): 推荐的字段数量
        
        Returns:
            list: 推荐的字段列表
        """
        if self.feature_importance is None:
            logger.error("请先应用PCA并计算特征重要性")
            return None
        
        logger.info(f"正在推荐前 {top_n} 个最重要的字段...")
        
        recommended_fields = self.feature_importance.head(top_n)['Feature'].tolist()
        
        # 如果有字段信息，显示字段的中文名称
        if self.fields_df is not None:
            field_info = []
            for field in recommended_fields:
                field_row = self.fields_df[self.fields_df['RESOURCE_CODE'] == field]
                if not field_row.empty:
                    cname = field_row.iloc[0]['CNAME'] if 'CNAME' in field_row.columns else '未知'
                    field_info.append(f"{field} ({cname})")
                else:
                    field_info.append(field)
            
            logger.info(f"推荐的字段: {', '.join(field_info[:10])}...")
        else:
            logger.info(f"推荐的字段: {', '.join(recommended_fields[:10])}...")
        
        return recommended_fields

def main():
    """主函数"""
    # 设置matplotlib中文显示
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 输入文件
    fields_file = "crd_datasource_params_export.xlsx"  # 字段信息文件
    data_file = None  # 实际数据文件，需要用户提供
    
    # 检查字段文件是否存在
    if not os.path.exists(fields_file):
        logger.error(f"字段信息文件不存在: {fields_file}")
    
    # 提示用户
    logger.info("注意: 要进行正确的PCA分析，需要提供包含这些字段的实际数据文件")
    logger.info("当前只有字段信息，无法进行完整的PCA分析")
    
    if data_file is None or not os.path.exists(data_file):
        logger.warning("未提供实际数据文件，只能展示字段信息")
        
        # 加载字段信息
        fields_df = pd.read_excel(fields_file)
        logger.info(f"字段信息加载成功，共 {len(fields_df)} 个字段")
        
        # 显示字段信息
        logger.info(f"字段示例:\n{fields_df.head()}")
        logger.info("要进行PCA分析，请提供包含这些字段的实际数据样本")
        return
    
    # 创建分析器
    analyzer = DataPCAAnalyzer(data_file=data_file, fields_file=fields_file)
    
    # 加载数据
    if not analyzer.load_data():
        return
    
    # 准备数据
    if not analyzer.prepare_data():
        return
    
    # 应用PCA
    if not analyzer.apply_pca(variance_threshold=0.95):
        return
    
    # 可视化PCA结果
    analyzer.visualize_pca()
    
    # 推荐字段
    analyzer.recommend_fields(top_n=50)
    
    # 导出结果
    analyzer.export_results(output_file='pca_analysis_results.xlsx')
    
    logger.info("PCA分析完成！")

if __name__ == "__main__":
    main()
