# LightGBM特征选择任务

## 任务概述
- **任务名称**: 使用LightGBM进行特征选择和数据降维
- **创建时间**: 2025年1月17日
- **主要目标**: 从1231个特征中选择最重要的特征，提升模型效果

## 数据文件
- **输入文件**: `result_20250616_152548_labeled_20250617_094235.xlsx`
- **数据规模**: 2374行 × 1231列
- **标签分布**: 好(2201) vs 坏(173) - 不平衡数据
- **缺失值**: -999表示缺失，约232万个缺失值
- **排除字段**: APP_IDCARD (身份证号)

## 执行计划
1. 数据预处理和探索性分析
2. 缺失值处理策略
3. 数据不平衡处理
4. LightGBM特征重要性分析
5. 多维度特征选择
6. 特征选择验证和优化
7. 结果输出和报告

## 执行状态
- [x] 任务规划完成
- [x] 步骤1: 数据预处理和探索性分析
- [x] 步骤2: 缺失值处理策略
- [x] 步骤3: 数据不平衡处理
- [x] 步骤4: LightGBM特征重要性分析
- [x] 步骤5: 多维度特征选择
- [x] 步骤6: 特征选择验证和优化
- [x] 步骤7: 结果输出和报告

## 技术栈
- LightGBM: 梯度提升模型
- SHAP: 特征解释性分析
- imbalanced-learn: 处理数据不平衡
- scikit-learn: 特征选择和验证

## 完成总结
**完成时间**: 2025年1月17日

### 实现功能
1. **完整的数据预处理流程**
   - -999缺失值处理和替换
   - 高缺失率列删除（>80%）
   - 数值型特征中位数填充
   - 分类特征众数填充和标签编码

2. **数据不平衡处理**
   - SMOTE过采样技术
   - 自适应k_neighbors参数设置

3. **LightGBM特征重要性分析**
   - 基于gain的特征重要性计算
   - 早停机制和交叉验证
   - 特征重要性可视化

4. **多维度特征选择**
   - LightGBM重要性选择
   - 递归特征消除(RFE)
   - 统计特征选择(SelectKBest)
   - 支持50/100/200个特征的选择

5. **性能评估和验证**
   - 5折交叉验证
   - AUC性能指标
   - 基准模型对比

6. **结果输出和可视化**
   - Excel格式详细报告
   - 特征重要性图表
   - 性能对比图表
   - 特征数量与性能关系图

### 输出文件
- `lightgbm_feature_selection.ipynb` - 完整的分析notebook
- `lightgbm_feature_selection_results.xlsx` - 详细结果报告
- `lightgbm_feature_importance.png` - 特征重要性图
- `feature_selection_performance_comparison.png` - 性能对比图
- `features_vs_performance.png` - 特征数量关系图

### 技术特点
- 模块化设计，函数可重用
- 完整的错误处理和日志输出
- 支持中文显示和报告
- 内存优化的数据处理
- 可配置的参数设置

## 执行日志
- 2025-01-17: 任务规划和需求分析
- 2025-01-17: 完成所有功能模块开发
- 2025-01-17: 代码测试和文档完善